#!/usr/bin/env python3
"""
测试客户端-服务器健壮性改进
模拟各种异常情况，验证系统是否能正确处理而不卡死
"""

import sys
import os
import time
import json
import threading
import traceback
from unittest.mock import Mock, MagicMock, patch

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入改进的函数
from client_robustness_patch import safe_execute_task, safe_send_response

# 模拟常量
SUCCESS_CODE = 0
GENERAL_ERROR = 9999
ARM_STATE_ERROR = 1001
PARSING_ERROR = 6001

class MockButtonHandler:
    """模拟按钮处理器，用于测试各种异常情况"""
    
    def __init__(self):
        self.test_mode = "normal"
    
    def execute_continuous_sequence(self, params):
        """模拟 execute_continuous_sequence 的各种行为"""
        if self.test_mode == "normal":
            return (SUCCESS_CODE, {
                'error_message': 'Sequence completed successfully',
                'completed_operations': ['find_buttons', 'click_button'],
                'operation_count': 2
            })
        
        elif self.test_mode == "exception":
            raise ValueError("Simulated execution exception")
        
        elif self.test_mode == "timeout":
            time.sleep(10)  # 模拟长时间阻塞
            return (SUCCESS_CODE, {'message': 'Should not reach here'})
        
        elif self.test_mode == "invalid_return":
            return "invalid_return_format"  # 错误的返回格式
        
        elif self.test_mode == "missing_error_message":
            return (ARM_STATE_ERROR, {'some_data': 'but no error_message'})
        
        elif self.test_mode == "non_dict_result":
            return (SUCCESS_CODE, "This should be a dict")
        
        else:
            return (GENERAL_ERROR, {'error_message': f'Unknown test mode: {self.test_mode}'})
    
    def run(self, params):
        """模拟单个操作执行"""
        if self.test_mode == "normal":
            return (SUCCESS_CODE, {'message': 'Single operation completed'})
        else:
            return self.execute_continuous_sequence(params)


def test_normal_execution():
    """测试正常执行情况"""
    print("=" * 60)
    print("测试1: 正常执行")
    print("=" * 60)
    
    handler = MockButtonHandler()
    handler.test_mode = "normal"
    
    params = {
        "operating": ["switch_manual", "turn_on"],
        "checkpoint": [1.0, 2.0, 3.0, 4.0, 5.0, 6.0],
        "checkpoint_bool": True
    }
    
    result = safe_execute_task(
        handler.execute_continuous_sequence,
        "test_normal",
        params,
        timeout=5
    )
    
    error_code, result_data = result
    print(f"结果代码: {error_code}")
    print(f"结果数据: {result_data}")
    
    if error_code == SUCCESS_CODE:
        print("✅ 正常执行测试通过")
        return True
    else:
        print("❌ 正常执行测试失败")
        return False


def test_exception_handling():
    """测试异常处理"""
    print("\n" + "=" * 60)
    print("测试2: 异常处理")
    print("=" * 60)
    
    handler = MockButtonHandler()
    handler.test_mode = "exception"
    
    params = {"operating": ["switch_manual"]}
    
    result = safe_execute_task(
        handler.execute_continuous_sequence,
        "test_exception",
        params,
        timeout=5
    )
    
    error_code, result_data = result
    print(f"结果代码: {error_code}")
    print(f"结果数据: {result_data}")
    
    if error_code == GENERAL_ERROR and 'exception_type' in result_data:
        print("✅ 异常处理测试通过")
        return True
    else:
        print("❌ 异常处理测试失败")
        return False


def test_timeout_handling():
    """测试超时处理"""
    print("\n" + "=" * 60)
    print("测试3: 超时处理")
    print("=" * 60)
    
    handler = MockButtonHandler()
    handler.test_mode = "timeout"
    
    params = {"operating": ["switch_manual"]}
    
    start_time = time.time()
    result = safe_execute_task(
        handler.execute_continuous_sequence,
        "test_timeout",
        params,
        timeout=2  # 2秒超时
    )
    end_time = time.time()
    
    error_code, result_data = result
    execution_time = end_time - start_time
    
    print(f"执行时间: {execution_time:.2f}秒")
    print(f"结果代码: {error_code}")
    print(f"结果数据: {result_data}")
    
    # 超时机制应该在约2秒内完成，但由于线程池的实现，可能会稍微延迟
    if error_code == GENERAL_ERROR and 'timeout' in result_data:
        print("✅ 超时处理测试通过")
        return True
    else:
        print("❌ 超时处理测试失败")
        print(f"   预期: error_code={GENERAL_ERROR}, 'timeout' in result_data")
        print(f"   实际: error_code={error_code}, result_data keys={list(result_data.keys()) if isinstance(result_data, dict) else 'not dict'}")
        return False


def test_invalid_return_format():
    """测试无效返回格式处理"""
    print("\n" + "=" * 60)
    print("测试4: 无效返回格式处理")
    print("=" * 60)
    
    handler = MockButtonHandler()
    handler.test_mode = "invalid_return"
    
    params = {"operating": ["switch_manual"]}
    
    result = safe_execute_task(
        handler.execute_continuous_sequence,
        "test_invalid_return",
        params,
        timeout=5
    )
    
    error_code, result_data = result
    print(f"结果代码: {error_code}")
    print(f"结果数据: {result_data}")
    
    if error_code == GENERAL_ERROR and 'original_result' in result_data:
        print("✅ 无效返回格式处理测试通过")
        return True
    else:
        print("❌ 无效返回格式处理测试失败")
        return False


def test_missing_error_message():
    """测试缺失错误消息的处理"""
    print("\n" + "=" * 60)
    print("测试5: 缺失错误消息处理")
    print("=" * 60)
    
    handler = MockButtonHandler()
    handler.test_mode = "missing_error_message"
    
    params = {"operating": ["switch_manual"]}
    
    result = safe_execute_task(
        handler.execute_continuous_sequence,
        "test_missing_error_message",
        params,
        timeout=5
    )
    
    error_code, result_data = result
    print(f"结果代码: {error_code}")
    print(f"结果数据: {result_data}")
    
    if error_code == ARM_STATE_ERROR and 'error_message' in result_data:
        print("✅ 缺失错误消息处理测试通过")
        return True
    else:
        print("❌ 缺失错误消息处理测试失败")
        return False


def test_non_dict_result():
    """测试非字典结果数据处理"""
    print("\n" + "=" * 60)
    print("测试6: 非字典结果数据处理")
    print("=" * 60)
    
    handler = MockButtonHandler()
    handler.test_mode = "non_dict_result"
    
    params = {"operating": ["switch_manual"]}
    
    result = safe_execute_task(
        handler.execute_continuous_sequence,
        "test_non_dict_result",
        params,
        timeout=5
    )
    
    error_code, result_data = result
    print(f"结果代码: {error_code}")
    print(f"结果数据: {result_data}")
    print(f"结果数据类型: {type(result_data)}")
    
    if error_code == SUCCESS_CODE and isinstance(result_data, dict) and 'original_type' in result_data:
        print("✅ 非字典结果数据处理测试通过")
        return True
    else:
        print("❌ 非字典结果数据处理测试失败")
        return False


def test_response_format_validation():
    """测试响应格式验证"""
    print("\n" + "=" * 60)
    print("测试7: 响应格式验证")
    print("=" * 60)
    
    # 模拟 send_response 函数
    def mock_send_response(response):
        print(f"Mock sending response: {response}")
        return True
    
    # 替换全局函数
    import client_robustness_patch
    client_robustness_patch.send_response = mock_send_response
    
    test_cases = [
        ("正常响应", {
            "code": SUCCESS_CODE,
            "status": "success",
            "command": "start",
            "message": "Test message",
            "data": {"test": "data"}
        }),
        ("缺失字段响应", {
            "code": SUCCESS_CODE,
            "status": "success"
            # 缺失 command 和 message
        }),
        ("非字典响应", "This is not a dict"),
        ("非字典data字段", {
            "code": SUCCESS_CODE,
            "status": "success",
            "command": "start",
            "message": "Test message",
            "data": "This should be a dict"
        })
    ]
    
    passed = 0
    total = len(test_cases)
    
    for test_name, response in test_cases:
        print(f"\n测试子项: {test_name}")
        try:
            result = safe_send_response(response)
            if result:
                print(f"✅ {test_name} 通过")
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print(f"\n响应格式验证测试结果: {passed}/{total}")
    return passed == total


def main():
    """运行所有测试"""
    print("开始客户端-服务器健壮性改进测试...")
    print("=" * 80)
    
    tests = [
        ("正常执行", test_normal_execution),
        ("异常处理", test_exception_handling),
        ("超时处理", test_timeout_handling),
        ("无效返回格式", test_invalid_return_format),
        ("缺失错误消息", test_missing_error_message),
        ("非字典结果数据", test_non_dict_result),
        ("响应格式验证", test_response_format_validation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 发生异常: {e}")
            print(f"异常详情: {traceback.format_exc()}")
    
    print("\n" + "=" * 80)
    print("测试总结")
    print("=" * 80)
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！健壮性改进工作正常。")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步检查。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
