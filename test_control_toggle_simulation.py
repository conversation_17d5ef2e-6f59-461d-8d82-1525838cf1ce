#!/usr/bin/env python3
"""
模拟测试 control_toggle 任务的执行
测试命令: {"command": "start", "task": "control_toggle", "params": {"operating": ["switch_manual", "turn_on", "turn_off", "switch_automatic"], "checkpoint": [3.184, 48.916, -45.813, -75.0, -100.044, -0.491], "checkpoint_bool": true}}
"""

import sys
import os
import time
import json
import datetime
import traceback
from unittest.mock import Mock, MagicMock, patch
from typing import Dict, Any, List, Tuple

# Add paths
sys.path.append(os.path.join(os.path.dirname(__file__), 'task'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'buttonControl'))

# Import constants
try:
    from task.button_processor import (
        SUCCESS_CODE, BUTTON_DETECTION_ERROR, BUTTON_OPERATION_ERROR, 
        BUTTON_STATE_ERROR, ARM_STATE_ERROR, EMERGENCY_STOP_ERROR, UNKNOWN_ERROR
    )
except ImportError:
    # 如果导入失败，定义常量
    SUCCESS_CODE = 0
    BUTTON_DETECTION_ERROR = 1001
    BUTTON_OPERATION_ERROR = 1002
    BUTTON_STATE_ERROR = 1003
    ARM_STATE_ERROR = 1004
    EMERGENCY_STOP_ERROR = 1005
    UNKNOWN_ERROR = 9999

class MockButtonProcessor:
    """模拟 ButtonProcessor 类"""
    
    def __init__(self):
        self.emergency_stopped = False
        self.status = 'uncertain'
        self.states = []
        self._in_reset_process = False
        self._auto_reset_delay = 0.1
        
        # Mock robot config
        self.robot_config = Mock()
        self.robot_config.get_distances.return_value = {
            'observation_distance': 0.4,
            'touch_distance': 0.25,
            'click_depth': 0.076,
            'turn_depth': 0.062
        }
        self.robot_config.get_movement_parameters.return_value = {
            'v': 20, 'r': 50, 'connect': 0, 'block': 1
        }
        self.robot_config.get_processing_parameters.return_value = {
            'use_robust_normal': True
        }
        
        # Mock arm and camera
        self.arm = Mock()
        self.camera_instance = Mock()
        
        # Mock data manager
        self.data_manager = Mock()
        
        # 操作映射和状态跟踪
        self.operation_mapping = {
            'switch_manual': ['find_buttons', 'observe_buttons', 'touch_left_button', 'finetune', 'click_button', 'restore'],
            'turn_on': ['observe_buttons', 'touch_knob', 'finetune', 'turn_knob', 'restore'],
            'turn_off': ['observe_buttons', 'touch_knob', 'finetune', 'turn_knob', 'restore'],
            'switch_automatic': ['observe_buttons', 'touch_right_button', 'finetune', 'click_button', 'restore']
        }
        
        self.operation_counts = {}
        self.execution_log = []
        
    def reset(self) -> int:
        """模拟重置操作"""
        print("MockButtonProcessor: Reset called")
        self.emergency_stopped = False
        self.status = 'uncertain'
        self.states = []
        self._in_reset_process = False
        self.execution_log.append(('reset', time.time(), SUCCESS_CODE, 'Reset completed'))
        return SUCCESS_CODE
    
    def collect_data(self, frame_count=5):
        """模拟数据收集"""
        return {
            'button_coords': [(100, 150, 10), (200, 150, 10), (100, 50, 10), (200, 50, 10)],
            'button_labels': ['bottom_left', 'bottom_right', 'top_left', 'top_right'],
            'knob_coord': (150, 100, 8),
            'knob_angle': 45.0,
            'pose': [0, 0, 0, 0, 0, 0],
            'joint': [0, 0, 0, 0, 0, 0],
            'point_cloud': None,
            'timestamp': int(time.time())
        }
    
    def set_status(self, status, message):
        """模拟状态设置"""
        self.status = status
        self.states.append((status, message, time.time()))
        print(f"Status changed to: {status} - {message}")
    
    def _execute_action(self, action: str, extra_params: Dict = None) -> Tuple[int, str, Dict]:
        """模拟动作执行"""
        if extra_params is None:
            extra_params = {}
            
        # 记录操作
        self.operation_counts[action] = self.operation_counts.get(action, 0) + 1
        execution_time = time.time()
        
        print(f"MockButtonProcessor: Executing action '{action}' (count: {self.operation_counts[action]})")
        
        # 模拟执行时间
        time.sleep(0.01)
        
        # 模拟各种操作的结果
        if action == 'find_buttons':
            # 使用传入的checkpoint
            checkpoint = extra_params.get('checkpoint', [3.184, 48.916, -45.813, -75.0, -100.044, -0.491])
            result = (SUCCESS_CODE, "Buttons found successfully", {'checkpoint': checkpoint})
            self.set_status('buttons_found', 'Buttons detected and located')
            
        elif action == 'observe_buttons':
            result = (SUCCESS_CODE, "Observation completed", {})
            self.set_status('observing', 'Observing button positions')
            
        elif action == 'touch_left_button':
            result = (SUCCESS_CODE, "Left button touched", {})
            self.set_status('touching_left_button', 'Approaching left button')
            
        elif action == 'touch_right_button':
            result = (SUCCESS_CODE, "Right button touched", {})
            self.set_status('touching_right_button', 'Approaching right button')
            
        elif action == 'touch_knob':
            result = (SUCCESS_CODE, "Knob touched", {})
            self.set_status('touching_knob_center', 'Approaching knob center')
            
        elif action == 'finetune':
            result = (SUCCESS_CODE, "Finetune completed", {})
            # 保持当前状态，finetune不改变主要状态
            
        elif action == 'click_button':
            result = (SUCCESS_CODE, "Button clicked", {})
            if self.status == 'touching_left_button':
                self.set_status('clicked_left_button', 'Left button clicked')
            elif self.status == 'touching_right_button':
                self.set_status('clicked_right_button', 'Right button clicked')
            else:
                self.set_status('button_clicked', 'Button clicked')
                
        elif action == 'turn_knob':
            result = (SUCCESS_CODE, "Knob turned", {})
            self.set_status('knob_turned', 'Knob rotation completed')
            
        elif action == 'restore':
            result = (SUCCESS_CODE, "Position restored", {})
            self.set_status('restored', 'Returned to safe position')
            
        else:
            result = (BUTTON_STATE_ERROR, f"Unknown action: {action}", {})
        
        # 记录执行日志
        self.execution_log.append((action, execution_time, result[0], result[1]))
        
        return result
    
    def _return_error_with_reset(self, error_code: int, error_message: str, extra_data: Dict = None):
        """模拟错误返回"""
        if extra_data is None:
            extra_data = {}
        return (error_code, {
            'error_message': error_message,
            **extra_data
        })
    
    def execute_continuous_sequence(self, params=None) -> Tuple[int, Dict[str, Any]]:
        """模拟 execute_continuous_sequence 方法"""
        if params is None:
            params = {}
        
        print('Reset before action sequence')
        try:
            reset_code = self.reset()
            if reset_code != SUCCESS_CODE:
                return (ARM_STATE_ERROR, {
                    'error_message': f'Reset before sequence failed: {reset_code}',
                    'init_reset_code': reset_code
                })
        except Exception as e:
            return (ARM_STATE_ERROR, {
                'error_message': f'Reset before sequence failed: {e}',
                'init_reset_code': -9999
            })
        
        # 获取参数
        operating_sequence = params.get('operating', [])
        checkpoint = params.get('checkpoint')
        checkpoint_bool = params.get('checkpoint_bool', False)
        
        print(f"ButtonController: Operating sequence: {operating_sequence}")
        print(f"ButtonController: Checkpoint: {checkpoint}")
        print(f"ButtonController: Checkpoint bool: {checkpoint_bool}")
        
        # 展开高级操作为基础操作
        expanded_sequence = []
        for high_level_op in operating_sequence:
            if high_level_op in self.operation_mapping:
                expanded_ops = self.operation_mapping[high_level_op]
                expanded_sequence.extend(expanded_ops)
                print(f"Expanded '{high_level_op}' to: {expanded_ops}")
            else:
                print(f"Warning: Unknown high-level operation '{high_level_op}'")
                expanded_sequence.append(high_level_op)
        
        print(f"ButtonController: Starting continuous sequence with {len(expanded_sequence)} operations")
        
        # 执行序列
        completed_operations = []
        sequence_checkpoint = None
        
        for i, action in enumerate(expanded_sequence):
            # 检查紧急停止
            if self.emergency_stopped:
                print("ButtonController: Emergency stop detected during continuous sequence")
                return (EMERGENCY_STOP_ERROR, {
                    'error_message': 'Emergency stop during continuous sequence',
                    'completed_operations': completed_operations,
                    'failed_operation': action,
                    'operation_index': i
                })
            
            print(f"ButtonController: Executing operation {i+1}/{len(expanded_sequence)}: {action}")
            
            # 准备额外参数
            extra_params = {}
            if action == 'find_buttons' and checkpoint:
                extra_params['checkpoint'] = checkpoint
            
            # 执行操作
            try:
                result_code, result_message, extra_data = self._execute_action(action, extra_params)
                
                # 保存checkpoint
                if action == 'find_buttons' and extra_data and 'checkpoint' in extra_data:
                    sequence_checkpoint = extra_data['checkpoint']
                
                if result_code != SUCCESS_CODE:
                    print(f"ButtonController: Operation '{action}' failed: {result_message}")
                    return self._return_error_with_reset(result_code, f"Operation '{action}' failed: {result_message}", {
                        'completed_operations': completed_operations,
                        'failed_operation': action,
                        'operation_index': i
                    })
                
                completed_operations.append(action)
                print(f"ButtonController: Operation '{action}' completed successfully")
                
                # 操作间延迟
                if i < len(expanded_sequence) - 1:
                    time.sleep(0.01)  # 快速测试
                    
            except Exception as e:
                error_msg = f"Exception during operation '{action}': {str(e)}"
                print(f"ButtonController: {error_msg}")
                return self._return_error_with_reset(BUTTON_OPERATION_ERROR, error_msg, {
                    'completed_operations': completed_operations,
                    'failed_operation': action,
                    'operation_index': i
                })
        
        # 构建结果
        result_data = {
            'error_message': 'Continuous sequence completed successfully',
            'completed_operations': completed_operations,
            'operation_count': len(completed_operations),
            'final_status': self.status,
            'status_history': self.states[-10:]  # 最近10个状态变化
        }
        
        # 处理checkpoint
        if checkpoint_bool:
            if sequence_checkpoint is not None:
                result_data['checkpoint'] = sequence_checkpoint
                print(f"ButtonController: Adding checkpoint to response: {sequence_checkpoint}")
            else:
                print("ButtonController: Warning - checkpoint_bool is True but no checkpoint was found")
        
        # 最终重置
        try:
            reset_code = self.reset()
            result_data['final_reset_code'] = reset_code
            if reset_code == SUCCESS_CODE:
                result_data['final_reset_message'] = 'Reset after sequence completed successfully'
            else:
                result_data['final_reset_message'] = f'Reset after sequence failed with code {reset_code}'
        except Exception as e:
            result_data['final_reset_code'] = -9999
            result_data['final_reset_message'] = f'Reset after sequence failed: {e}'
        
        print("ButtonController: Continuous sequence completed successfully")
        return (SUCCESS_CODE, result_data)

def simulate_control_toggle_command():
    """模拟 control_toggle 命令执行 - 基于 client.py 的实际实现"""
    print("=" * 80)
    print("模拟 control_toggle 任务执行 (基于 client.py)")
    print("=" * 80)

    # 模拟的命令
    command = {
        "command": "start",
        "task": "control_toggle",
        "params": {
            "operating": ["switch_manual", "turn_on", "turn_off", "switch_automatic"],
            "checkpoint": [3.184, 48.916, -45.813, -75.0, -100.044, -0.491],
            "checkpoint_bool": True
        }
    }

    print(f"接收到命令: {json.dumps(command, indent=2)}")

    # 创建模拟处理器
    processor = MockButtonProcessor()

    # 模拟 client.py 中的处理逻辑
    try:
        start_time = time.time()

        # 提取命令详情 (基于 client.py 第158-162行)
        command_type = command.get("command")
        task_type = command.get("task")
        params = command.get("params", {})

        print(f'[{datetime.datetime.now().strftime("%m-%d %H:%M:%S")}] Processing command: {command}')

        # 处理 start-control_toggle 命令 (基于 client.py 第308-377行)
        if command_type == "start" and task_type == "control_toggle":
            print("Processing start-control_toggle command...")
            current_task = "control_toggle"

            # 模拟初始响应 (基于 client.py 第327-335行)
            initial_response = {
                "code": SUCCESS_CODE,
                "status": "received",
                "command": "start",
                "task": "control_toggle",
                "message": "Receive start-control_toggle command",
                "data": {}
            }
            print(f"发送初始响应: {initial_response}")

            # 检查是否为连续序列请求 (基于 client.py 第337-342行)
            operating_sequence = params.get("operating", None)
            checkpoint_bool = params.get("checkpoint_bool", None)

            if operating_sequence is not None:
                # 执行连续序列 (基于 client.py 第344-346行)
                print(f"ButtonController: Executing continuous sequence: {operating_sequence}")
                result = processor.execute_continuous_sequence(params)
            else:
                # 执行单个动作 (原始行为)
                print("ButtonController: Executing single action")
                result = processor.run(params)  # 这里会失败，因为我们没有实现run方法

            result_code, result_data = result

        else:
            print(f"未知命令类型: {command_type}, 任务: {task_type}")
            return False

        end_time = time.time()
        execution_time = end_time - start_time
        
        # 构建响应 (基于 client.py 第351-377行)
        if result_code == 0:
            response = {
                "code": 0,
                "status": "success",
                "command": "start",
                "task": "control_toggle",
                "message": "Complete start-control_toggle command",
                "data": result_data if len(result) > 1 else {}
            }
            response["data"]["checkpoint_bool"] = checkpoint_bool
            if checkpoint_bool and "checkpoint" in result_data:
                response["data"]["checkpoint"] = result_data["checkpoint"]
            current_task = None
        else:
            error_code = result_code
            error_message = result_data['error_message']
            response = {
                "code": error_code,
                "status": "error",
                "command": "start",
                "task": "control_toggle",
                "message": "Error during start-control_toggle command: " + error_message,
                "data": result_data if len(result) > 1 else {}
            }
            response["data"]["checkpoint_bool"] = checkpoint_bool
            current_task = None

        print(f"最终响应: {json.dumps(response, indent=2)}")

        # 分析结果
        print(f"\n" + "=" * 60)
        print("执行结果分析")
        print("=" * 60)

        print(f"结果代码: {result_code}")
        print(f"执行时间: {execution_time:.3f} 秒")
        print(f"响应状态: {response['status']}")

        if result_code == SUCCESS_CODE:
            print("✅ 任务执行成功!")

            # 详细结果分析
            print(f"\n完成的操作数量: {result_data.get('operation_count', 0)}")
            print(f"最终状态: {result_data.get('final_status', 'unknown')}")

            completed_ops = result_data.get('completed_operations', [])
            print(f"\n完成的操作序列 ({len(completed_ops)} 个):")
            for i, op in enumerate(completed_ops, 1):
                print(f"  {i:2d}. {op}")

            # 检查点信息 (基于 client.py 第361-362行的逻辑)
            response_data = response.get('data', {})
            if response_data.get('checkpoint_bool') and 'checkpoint' in response_data:
                checkpoint = response_data['checkpoint']
                print(f"\n响应中的检查点: {checkpoint}")
                print(f"检查点类型: {type(checkpoint)}")
                print(f"检查点长度: {len(checkpoint) if isinstance(checkpoint, (list, tuple)) else 'N/A'}")
            else:
                print(f"\n检查点处理: checkpoint_bool={response_data.get('checkpoint_bool')}, 无检查点数据")

            # 状态历史
            status_history = result_data.get('status_history', [])
            if status_history:
                print(f"\n状态变化历史 ({len(status_history)} 个):")
                for i, (status, message, timestamp) in enumerate(status_history, 1):
                    print(f"  {i:2d}. {status}: {message}")

            # 重置信息
            reset_code = result_data.get('final_reset_code')
            reset_message = result_data.get('final_reset_message')
            print(f"\n最终重置: 代码={reset_code}, 消息={reset_message}")

        else:
            print("❌ 任务执行失败!")
            error_message = result_data.get('error_message', '未知错误')
            print(f"错误消息: {error_message}")

            if 'failed_operation' in result_data:
                failed_op = result_data['failed_operation']
                op_index = result_data.get('operation_index', -1)
                print(f"失败操作: {failed_op} (索引: {op_index})")

            completed_ops = result_data.get('completed_operations', [])
            print(f"已完成操作: {completed_ops}")
        
        # 操作统计
        print(f"\n" + "=" * 60)
        print("操作统计")
        print("=" * 60)
        
        print(f"操作计数: {processor.operation_counts}")
        
        # 执行日志分析
        execution_log = processor.execution_log
        print(f"\n执行日志 ({len(execution_log)} 条):")
        for i, (action, timestamp, code, message) in enumerate(execution_log, 1):
            status = "✅" if code == SUCCESS_CODE else "❌"
            print(f"  {i:2d}. {status} {action}: {message}")
        
        return result_code == SUCCESS_CODE
        
    except Exception as e:
        print(f"❌ 模拟执行异常: {e}")
        print(f"错误详情: {traceback.format_exc()}")
        return False

def test_edge_cases():
    """测试边界情况"""
    print("\n" + "=" * 80)
    print("边界情况测试")
    print("=" * 80)
    
    edge_cases = [
        {
            "name": "空操作序列",
            "params": {
                "operating": [],
                "checkpoint": [3.184, 48.916, -45.813, -75.0, -100.044, -0.491],
                "checkpoint_bool": True
            }
        },
        {
            "name": "无效操作",
            "params": {
                "operating": ["invalid_operation"],
                "checkpoint": [3.184, 48.916, -45.813, -75.0, -100.044, -0.491],
                "checkpoint_bool": True
            }
        },
        {
            "name": "无checkpoint参数",
            "params": {
                "operating": ["switch_manual"],
                "checkpoint_bool": True
            }
        },
        {
            "name": "checkpoint_bool为False",
            "params": {
                "operating": ["switch_manual"],
                "checkpoint": [3.184, 48.916, -45.813, -75.0, -100.044, -0.491],
                "checkpoint_bool": False
            }
        },
        {
            "name": "无效checkpoint格式",
            "params": {
                "operating": ["switch_manual"],
                "checkpoint": "invalid_checkpoint",
                "checkpoint_bool": True
            }
        }
    ]
    
    results = []
    
    for case in edge_cases:
        print(f"\n测试: {case['name']}")
        print(f"参数: {case['params']}")
        
        processor = MockButtonProcessor()
        
        try:
            result_code, result_data = processor.execute_continuous_sequence(case['params'])
            
            if result_code == SUCCESS_CODE:
                print(f"✅ 测试通过")
                completed_ops = result_data.get('completed_operations', [])
                print(f"   完成操作: {len(completed_ops)} 个")
                if 'checkpoint' in result_data:
                    print(f"   返回检查点: {result_data['checkpoint']}")
            else:
                print(f"❌ 测试失败: {result_data.get('error_message', '未知错误')}")
            
            results.append((case['name'], result_code == SUCCESS_CODE))
            
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((case['name'], False))
    
    # 总结边界测试
    print(f"\n边界测试总结:")
    passed = sum(1 for name, success in results if success)
    total = len(results)
    print(f"通过: {passed}/{total}")
    
    for name, success in results:
        status = "✅" if success else "❌"
        print(f"  {status} {name}")
    
    return passed == total

def main():
    """主函数"""
    print("开始 control_toggle 任务模拟测试...")
    
    try:
        # 主要模拟测试
        main_test_success = simulate_control_toggle_command()
        
        # 边界情况测试
        edge_test_success = test_edge_cases()
        
        # 总结
        print(f"\n" + "=" * 80)
        print("测试总结")
        print("=" * 80)
        
        print(f"主要功能测试: {'✅ 通过' if main_test_success else '❌ 失败'}")
        print(f"边界情况测试: {'✅ 通过' if edge_test_success else '❌ 失败'}")
        
        if main_test_success and edge_test_success:
            print(f"\n✅ 所有测试通过! control_toggle 任务模拟成功")
            return 0
        else:
            print(f"\n❌ 部分测试失败，需要进一步检查")
            return 1
            
    except Exception as e:
        print(f"测试过程中出现严重错误: {e}")
        print(f"错误详情: {traceback.format_exc()}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
