#  RMDemo_QtExample_C


## **1. 简介**
本项目是一个跨平台的Qt应用程序，支持Linux和Windows操作系统。项目旨在演示如何使用Qt的qmake构建系统来导入并使用睿尔曼C语言版本二次开发包完成机械臂的连接、获取机械臂版本、机械臂运动以及断开连接。本Readme文档将说明使用环境要求、并指导用户如何新建項目-->导入库-->调用接口-->构建和运行项目。

## **2. 代码结构**
```
RMDemo_QtExample_C
├── API_QtExample_C.pro		# 项目文件，包含了项目的配置信息
├── include
│   ├── rm_define.h  # 机械臂二次开发包头文件，包含了定义的数据类型、结构体
│   └── rm_interface.h # 机械臂二次开发包头文件，声明了机械臂所有操作接口
├── lib
│   ├── api_c.dll    # Windows 的 API 库
│   ├── api_c.lib    # Windows 的 API 库
│   └── libapi_c.so  # Linux 的 API 库
├── main.c           # 主函数
└── readme.md            # 示例工程说明文档
```

## **3. 环境要求**

- **Qt 版本**：下载并安装适合你操作系统的Qt版本（Qt 5 或 Qt 6）。
- **操作系统**：支持Linux、Windows  
- **编译器**：
  - Windows环境下，编译器要求MSVC 2015以上
  - Linux环境下，使用GCC 7.5或更高版本  
- **其他依赖**：睿尔曼二次开发包（下载链接）


## **4. 项目步骤**

#### 项目配置

1. **创建Qt项目**：

   - 打开Qt Creator，选择“文件” > “新建文件或项目”。
   - 选择“应用程序”下的模板（例如“Qt Console Application”），点击“选择”按钮继续。
   - 填写项目名称、位置等信息，选择qmake构建系统，点击“下一步”。
   - 构建套件选择MSVC编译器（例如Windows下选择MSVC2017 64bit），点击“下一步”直到完成向导。

2. **配置qmake**：

   - 根据选择的编译器将对应版本的睿尔曼二次开发包动态库文件及头文件放置到项目中（对应上面选择的MSVC2017 64bit编译器，这里选择Windows 64bit的库）。

   - 打开项目的`.pro`文件，添加睿尔曼库的包含路径和库文件。

   本项目二次开发包文件目录及`.pro`文件配置如下（用户根据实际路径调整）：

   ![image-20240723131347642](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20240723131347642.png)

   ```pro
   INCLUDEPATH += $$PWD/include
   LIBS += -L$$PWD/lib -lapi_c
   ```

   - 注意替换`$$PWD/include`和`$$PWD/lib`为实际的头文件和库文件路径。
   - `-lapi_c`中的`api_c`是库文件（不包括前缀和后缀）的名称。

3. **修改项目代码**：

   - 在你的Qt项目中，添加包含睿尔曼头文件的代码。
   - 编写调用睿尔曼库函数的代码，以实现所需的功能。

#### 编译与运行

1. 编译项目：
   - 在Qt Creator中，点击左下角的绿色播放按钮或使用快捷键（通常是F5）来构建并运行项目。
   - 确保没有编译错误，并且项目能够正确链接到睿尔曼库。
2. 运行与测试：
   - 运行应用程序，并测试其是否按预期工作，特别是那些使用睿尔曼库函数的部分。


## **5. 注意事项**

该Demo以RM65-B型号机械臂为例，请根据实际情况修改代码中的数据。


## **7. 许可证信息**

* 本项目遵循MIT许可证。

## **8. 常见问题解答（FAQ）**


- **链接错误**：

  - 检查LIBS和INCLUDEPATH是否正确指向睿尔曼库的头文件和库文件。
  - 检查指向的库文件版本是否与环境相对应。
- **编译器或构建错误**：检查Qt Creator的配置设置，确保选择了正确的Qt版本和编译器。
- **运行时错误**：确保库文件在系统的库路径中，或在应用程序的可执行文件同目录下。
- **机械臂连接不上**：检查机械臂IP是否被修改。
- **机械臂运动失败**：检查机械臂型号，本示例基于RM65_B机械臂编写，其中的运动点位可能不适用于其他型号。
