SUCCESS_CODE = 0
ARM_STATE_ERROR = 1001
ARM_MOVEMENT_ERROR = 1002
GRIPPER_STATE_ERROR = 1003
GRIPPER_CONTROL_ERROR = 1004
ARM_TRAJECTORY_TIMEOUT_ERROR = 1005
NO_DETECTOR_ERROR = 2001
NO_POLE_ERROR = 2002
NO_POLE_HANDLER_ERROR = 2003
NO_DEPTH_INFO_ERROR = 2004
POLE_TIMEOUT_ERROR = 2005
POLE_UNREACHABLE_ERROR = 2006
BUTTON_DETECTION_ERROR = 2011      # Button/knob detection failed
BUTTON_APPROACH_ERROR = 2012       # Button/knob approach failed
BUTTON_OPERATION_ERROR = 2013      # Button click/knob turn failed
FINETUNE_ERROR = 2014              # Finetune operation failed
BUTTON_STATE_ERROR = 2015          # Invalid button state/operation
BUTTON_CONFIG_ERROR = 2016         # Button configuration error
CAMERA_ERROR = 3001
EMERGENCY_STOP_ERROR = 4001
MEMORY_ERROR = 5001
PARSING_ERROR = 6001

UNKNOWN_ERROR = 9999

"""
| Error Code | Error Variable Name | Error Description |
| ---------- | ----------------- |
| 0 | SUCCESS_CODE | Success |
| 1001 | ARM_STATE_ERROR | Failed to get arm state after multiple attempts |
| 1002 | ARM_MOVEMENT_ERROR | General arm movement error |
| 1003 | GRIPPER_STATE_ERROR | Failed to get gripper state |
| 1004 | GRIPPER_CONTROL_ERROR | Failed to control gripper |
| 1005 | ARM_TRAJECTORY_TIMEOUT_ERROR | Timeout when checking arm trajectory |
| 2001 | NO_DETECTOR_ERROR | No smoke-detector found |
| 2002 | NO_POLE_ERROR | No smoking-pole detected |
| 2003 | NO_POLE_HANDLER_ERROR | No smoking-pole handler found |
| 2004 | NO_DEPTH_INFO_ERROR | No information of detector depth |
| 2005 | POLE_TIMEOUT_ERROR | Timeout when chasing detector |
| 2006 | POLE_UNREACHABLE_ERROR | Cannot reach out detector |
| 2011 | BUTTON_DETECTION_ERROR | Button/knob detection failed |
| 2012 | BUTTON_APPROACH_ERROR | Button/knob approach failed |
| 2013 | BUTTON_OPERATION_ERROR | Button click/knob turn failed |
| 2014 | FINETUNE_ERROR | Finetune operation failed |
| 2015 | BUTTON_STATE_ERROR | Invalid button state/operation |
| 2016 | BUTTON_CONFIG_ERROR | Button configuration error |
| 3001 | CAMERA_ERROR | Camera not available or error |
| 4001 | EMERGENCY_STOP_ERROR | Emergency stop command received |
| 5001 | MEMORY_ERROR | No Available Memory for picture storage or RAM |
| 6001 | PARSING_ERROR | Parsing received command json error |
| 9999 | UNKNOWN_ERROR | Unknown error |
"""
