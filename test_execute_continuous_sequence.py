#!/usr/bin/env python3
"""
测试 button_processor.py 的 execute_continuous_sequence 方法
模拟各种场景，检查隐藏的问题
"""

import sys
import os
import time
import traceback
from unittest.mock import Mock, MagicMock, patch
from typing import Dict, Any, List, Tuple

# Add paths
sys.path.append(os.path.join(os.path.dirname(__file__), 'task'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'buttonControl'))

# Import constants
from task.button_processor import (
    SUCCESS_CODE, BUTTON_DETECTION_ERROR, BUTTON_OPERATION_ERROR, 
    BUTTON_STATE_ERROR, ARM_STATE_ERROR, EMERGENCY_STOP_ERROR, UNKNOWN_ERROR
)

class MockButtonProcessor:
    """模拟 ButtonProcessor 类用于测试"""
    
    def __init__(self):
        self.emergency_stopped = False
        self.status = 'uncertain'
        self.states = []
        self._in_reset_process = False
        self._auto_reset_delay = 0.1  # 快速测试
        self.last_coordinate_axes = None
        self.last_coordinate_origin = None
        self.last_observing_frame = None
        
        # Mock robot config
        self.robot_config = Mock()
        self.robot_config.get_distances.return_value = {
            'observation_distance': 0.4,
            'touch_distance': 0.25,
            'click_depth': 0.076,
            'turn_depth': 0.062
        }
        self.robot_config.get_movement_parameters.return_value = {
            'v': 20, 'r': 50, 'connect': 0, 'block': 1
        }
        self.robot_config.get_processing_parameters.return_value = {
            'use_robust_normal': True
        }
        
        # Mock arm
        self.arm = Mock()
        
        # Mock data manager
        self.data_manager = Mock()
        
        # 操作计数器
        self.operation_counts = {}
        self.failed_operations = []
        
    def reset(self) -> int:
        """模拟重置操作"""
        print("MockButtonProcessor: Reset called")
        self.emergency_stopped = False
        self.status = 'uncertain'
        self.states = []
        self._in_reset_process = False
        return SUCCESS_CODE
    
    def collect_data(self, frame_count=5):
        """模拟数据收集"""
        return {
            'button_coords': [(100, 150, 10), (200, 150, 10), (100, 50, 10), (200, 50, 10)],
            'button_labels': ['bottom_left', 'bottom_right', 'top_left', 'top_right'],
            'knob_coord': (150, 100, 8),
            'knob_angle': 45.0,
            'pose': [0, 0, 0, 0, 0, 0],
            'joint': [0, 0, 0, 0, 0, 0],
            'point_cloud': None
        }
    
    def set_status(self, status, message):
        """模拟状态设置"""
        self.status = status
        self.states.append((status, message, time.time()))
    
    def _execute_action(self, action: str, extra_params: Dict = None) -> Tuple[int, str, Dict]:
        """模拟动作执行"""
        if extra_params is None:
            extra_params = {}
            
        # 记录操作次数
        self.operation_counts[action] = self.operation_counts.get(action, 0) + 1
        
        print(f"MockButtonProcessor: Executing action '{action}' (count: {self.operation_counts[action]})")
        
        # 模拟各种场景
        if action in self.failed_operations:
            return (BUTTON_OPERATION_ERROR, f"Simulated failure for {action}", {})
        
        if action == 'find_buttons':
            return (SUCCESS_CODE, "Buttons found", {'checkpoint': [1.0, 2.0, 3.0, 4.0, 5.0, 6.0]})
        elif action == 'observe_buttons':
            return (SUCCESS_CODE, "Observation completed", {})
        elif action in ['touch_left_button', 'touch_right_button', 'touch_knob']:
            return (SUCCESS_CODE, f"{action} completed", {})
        elif action == 'finetune':
            return (SUCCESS_CODE, "Finetune completed", {})
        elif action in ['click_button', 'turn_knob']:
            return (SUCCESS_CODE, f"{action} completed", {})
        elif action == 'restore':
            return (SUCCESS_CODE, "Restore completed", {})
        else:
            return (BUTTON_STATE_ERROR, f"Unknown action: {action}", {})
    
    def _return_error_with_reset(self, error_code: int, error_message: str, extra_data: Dict = None):
        """模拟错误返回"""
        if extra_data is None:
            extra_data = {}
        return (error_code, {
            'error_message': error_message,
            **extra_data
        })

def test_normal_sequence():
    """测试正常的连续序列执行"""
    print("\n" + "="*60)
    print("测试1: 正常连续序列执行")
    print("="*60)
    
    processor = MockButtonProcessor()
    
    # 测试默认序列
    params = {}
    
    try:
        # 模拟 execute_continuous_sequence 的核心逻辑
        reset_code = processor.reset()
        if reset_code != SUCCESS_CODE:
            print(f"❌ 初始重置失败: {reset_code}")
            return False
        
        # 默认操作序列
        operating_sequence = [
            "find_buttons", "observe_buttons", "observe_buttons", "touch_knob", "finetune", "turn_knob", "restore",
            "observe_buttons", "touch_left_button", "finetune", "click_button", "restore",
            "observe_buttons", "touch_right_button", "finetune", "click_button", "restore",
            "observe_buttons", "touch_knob", "finetune", "turn_knob", "restore"
        ]
        
        completed_operations = []
        sequence_checkpoint = None
        
        for i, action in enumerate(operating_sequence):
            if processor.emergency_stopped:
                print(f"❌ 紧急停止在操作 {i}: {action}")
                return False
            
            result_code, result_message, extra_data = processor._execute_action(action)
            
            if action == 'find_buttons' and extra_data and 'checkpoint' in extra_data:
                sequence_checkpoint = extra_data['checkpoint']
            
            if result_code != SUCCESS_CODE:
                print(f"❌ 操作失败 {i}: {action} - {result_message}")
                return False
            
            completed_operations.append(action)
            time.sleep(0.01)  # 快速测试
        
        # 最终重置
        final_reset_code = processor.reset()
        
        print(f"✅ 正常序列测试通过")
        print(f"   完成操作数: {len(completed_operations)}")
        print(f"   操作计数: {processor.operation_counts}")
        print(f"   检查点: {sequence_checkpoint}")
        print(f"   最终重置: {final_reset_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ 正常序列测试异常: {e}")
        print(f"   错误详情: {traceback.format_exc()}")
        return False

def test_emergency_stop_scenarios():
    """测试紧急停止场景"""
    print("\n" + "="*60)
    print("测试2: 紧急停止场景")
    print("="*60)
    
    test_cases = [
        ("序列开始前紧急停止", 0),
        ("序列中间紧急停止", 5),
        ("序列末尾紧急停止", 15)
    ]
    
    for case_name, stop_at_operation in test_cases:
        print(f"\n测试子场景: {case_name}")
        
        processor = MockButtonProcessor()
        
        # 默认操作序列
        operating_sequence = [
            "find_buttons", "observe_buttons", "observe_buttons", "touch_knob", "finetune", "turn_knob", "restore",
            "observe_buttons", "touch_left_button", "finetune", "click_button", "restore",
            "observe_buttons", "touch_right_button", "finetune", "click_button", "restore",
            "observe_buttons", "touch_knob", "finetune", "turn_knob", "restore"
        ]
        
        try:
            reset_code = processor.reset()
            if reset_code != SUCCESS_CODE:
                print(f"❌ 初始重置失败: {reset_code}")
                continue
            
            completed_operations = []
            
            for i, action in enumerate(operating_sequence):
                # 在指定操作处触发紧急停止
                if i == stop_at_operation:
                    processor.emergency_stopped = True
                    print(f"   触发紧急停止在操作 {i}: {action}")
                
                if processor.emergency_stopped:
                    print(f"   检测到紧急停止，序列终止")
                    print(f"   已完成操作: {completed_operations}")
                    print(f"   失败操作: {action}")
                    print(f"   操作索引: {i}")
                    break
                
                result_code, result_message, extra_data = processor._execute_action(action)
                
                if result_code != SUCCESS_CODE:
                    print(f"❌ 操作失败 {i}: {action} - {result_message}")
                    break
                
                completed_operations.append(action)
            
            print(f"✅ 紧急停止场景测试通过: {case_name}")
            
        except Exception as e:
            print(f"❌ 紧急停止测试异常: {e}")

def test_operation_failure_scenarios():
    """测试操作失败场景"""
    print("\n" + "="*60)
    print("测试3: 操作失败场景")
    print("="*60)
    
    failure_scenarios = [
        ("find_buttons失败", ["find_buttons"]),
        ("observe_buttons失败", ["observe_buttons"]),
        ("touch操作失败", ["touch_left_button"]),
        ("finetune失败", ["finetune"]),
        ("click_button失败", ["click_button"]),
        ("turn_knob失败", ["turn_knob"]),
        ("restore失败", ["restore"]),
        ("多个操作失败", ["touch_knob", "finetune", "turn_knob"])
    ]
    
    for scenario_name, failed_ops in failure_scenarios:
        print(f"\n测试子场景: {scenario_name}")
        
        processor = MockButtonProcessor()
        processor.failed_operations = failed_ops
        
        operating_sequence = [
            "find_buttons", "observe_buttons", "touch_knob", "finetune", "turn_knob", "restore",
            "observe_buttons", "touch_left_button", "finetune", "click_button", "restore"
        ]
        
        try:
            reset_code = processor.reset()
            if reset_code != SUCCESS_CODE:
                print(f"❌ 初始重置失败: {reset_code}")
                continue
            
            completed_operations = []
            failed_operation = None
            operation_index = -1
            
            for i, action in enumerate(operating_sequence):
                if processor.emergency_stopped:
                    break
                
                result_code, result_message, extra_data = processor._execute_action(action)
                
                if result_code != SUCCESS_CODE:
                    print(f"   操作失败: {action} - {result_message}")
                    failed_operation = action
                    operation_index = i
                    break
                
                completed_operations.append(action)
            
            print(f"✅ 操作失败场景测试通过: {scenario_name}")
            print(f"   已完成操作: {completed_operations}")
            print(f"   失败操作: {failed_operation}")
            print(f"   失败索引: {operation_index}")
            
        except Exception as e:
            print(f"❌ 操作失败测试异常: {e}")

def test_custom_sequences():
    """测试自定义操作序列"""
    print("\n" + "="*60)
    print("测试4: 自定义操作序列")
    print("="*60)
    
    custom_sequences = [
        ("简单序列", ["find_buttons", "observe_buttons", "restore"]),
        ("只有按钮操作", ["find_buttons", "observe_buttons", "touch_left_button", "click_button", "restore"]),
        ("只有旋钮操作", ["find_buttons", "observe_buttons", "touch_knob", "turn_knob", "restore"]),
        ("重复操作", ["find_buttons", "observe_buttons", "observe_buttons", "observe_buttons"]),
        ("空序列", []),
        ("单个操作", ["find_buttons"])
    ]
    
    for sequence_name, custom_sequence in custom_sequences:
        print(f"\n测试子场景: {sequence_name}")
        
        processor = MockButtonProcessor()
        
        try:
            reset_code = processor.reset()
            if reset_code != SUCCESS_CODE:
                print(f"❌ 初始重置失败: {reset_code}")
                continue
            
            if not custom_sequence:
                print("   空序列，跳过执行")
                print(f"✅ 自定义序列测试通过: {sequence_name}")
                continue
            
            completed_operations = []
            
            for i, action in enumerate(custom_sequence):
                if processor.emergency_stopped:
                    break
                
                result_code, result_message, extra_data = processor._execute_action(action)
                
                if result_code != SUCCESS_CODE:
                    print(f"   操作失败: {action} - {result_message}")
                    break
                
                completed_operations.append(action)
            
            print(f"✅ 自定义序列测试通过: {sequence_name}")
            print(f"   完成操作: {completed_operations}")
            print(f"   操作计数: {processor.operation_counts}")
            
        except Exception as e:
            print(f"❌ 自定义序列测试异常: {e}")

def test_checkpoint_handling():
    """测试检查点处理"""
    print("\n" + "="*60)
    print("测试5: 检查点处理")
    print("="*60)
    
    test_cases = [
        ("checkpoint_bool=True", True),
        ("checkpoint_bool=False", False),
        ("无checkpoint_bool参数", None)
    ]
    
    for case_name, checkpoint_bool in test_cases:
        print(f"\n测试子场景: {case_name}")
        
        processor = MockButtonProcessor()
        
        try:
            reset_code = processor.reset()
            if reset_code != SUCCESS_CODE:
                print(f"❌ 初始重置失败: {reset_code}")
                continue
            
            # 简单序列包含find_buttons以获取checkpoint
            operating_sequence = ["find_buttons", "observe_buttons", "restore"]
            
            completed_operations = []
            sequence_checkpoint = None
            
            for i, action in enumerate(operating_sequence):
                result_code, result_message, extra_data = processor._execute_action(action)
                
                if action == 'find_buttons' and extra_data and 'checkpoint' in extra_data:
                    sequence_checkpoint = extra_data['checkpoint']
                
                if result_code != SUCCESS_CODE:
                    print(f"   操作失败: {action} - {result_message}")
                    break
                
                completed_operations.append(action)
            
            # 构建结果数据
            result_data = {
                'error_message': 'Continuous sequence completed successfully',
                'completed_operations': completed_operations,
                'operation_count': len(completed_operations)
            }
            
            # 处理checkpoint
            if checkpoint_bool is True:
                if sequence_checkpoint is not None:
                    result_data['checkpoint'] = sequence_checkpoint
                    print(f"   添加检查点到响应: {sequence_checkpoint}")
                else:
                    print("   警告: checkpoint_bool为True但未找到检查点")
            elif checkpoint_bool is False:
                print("   checkpoint_bool为False，不添加检查点")
            else:
                print("   未指定checkpoint_bool参数")
            
            print(f"✅ 检查点处理测试通过: {case_name}")
            print(f"   结果数据键: {list(result_data.keys())}")
            
        except Exception as e:
            print(f"❌ 检查点处理测试异常: {e}")

def test_reset_scenarios():
    """测试重置场景"""
    print("\n" + "="*60)
    print("测试6: 重置场景")
    print("="*60)
    
    test_cases = [
        ("正常重置", True, SUCCESS_CODE),
        ("重置失败", False, ARM_STATE_ERROR)
    ]
    
    for case_name, reset_success, expected_code in test_cases:
        print(f"\n测试子场景: {case_name}")
        
        processor = MockButtonProcessor()
        
        # 模拟重置结果
        if not reset_success:
            def failing_reset():
                raise Exception("模拟重置失败")
            processor.reset = failing_reset
        
        try:
            # 测试初始重置
            try:
                reset_code = processor.reset()
                if reset_success:
                    print(f"   初始重置成功: {reset_code}")
                else:
                    print(f"❌ 预期重置失败但成功了")
                    continue
            except Exception as e:
                if not reset_success:
                    print(f"   预期的重置失败: {e}")
                    print(f"✅ 重置失败场景测试通过: {case_name}")
                    continue
                else:
                    print(f"❌ 意外的重置失败: {e}")
                    continue
            
            # 执行简单序列
            operating_sequence = ["find_buttons", "observe_buttons"]
            
            for action in operating_sequence:
                result_code, result_message, extra_data = processor._execute_action(action)
                if result_code != SUCCESS_CODE:
                    break
            
            # 测试最终重置
            try:
                final_reset_code = processor.reset()
                print(f"   最终重置成功: {final_reset_code}")
            except Exception as e:
                print(f"   最终重置失败: {e}")
            
            print(f"✅ 重置场景测试通过: {case_name}")
            
        except Exception as e:
            print(f"❌ 重置场景测试异常: {e}")

def main():
    """主测试函数"""
    print("开始 execute_continuous_sequence 全面测试...")
    print("测试目标: 发现隐藏问题和边界情况")
    
    test_results = []
    
    try:
        # 执行各种测试
        test_results.append(("正常序列执行", test_normal_sequence()))
        test_emergency_stop_scenarios()
        test_operation_failure_scenarios()
        test_custom_sequences()
        test_checkpoint_handling()
        test_reset_scenarios()
        
        # 总结测试结果
        print("\n" + "="*60)
        print("测试总结")
        print("="*60)
        
        passed_tests = sum(1 for name, result in test_results if result)
        total_tests = len(test_results)
        
        print(f"通过测试: {passed_tests}/{total_tests}")
        
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {test_name}: {status}")
        
        # 发现的潜在问题
        print("\n" + "="*60)
        print("发现的潜在问题和建议")
        print("="*60)
        
        print("1. 错误处理:")
        print("   - 需要确保每个操作失败后都能正确清理状态")
        print("   - 紧急停止检查应该在每个操作前后都进行")
        
        print("\n2. 重置机制:")
        print("   - 初始重置失败时应该有更好的错误报告")
        print("   - 最终重置失败不应该影响主要操作的成功状态")
        
        print("\n3. 检查点处理:")
        print("   - 应该验证checkpoint数据的有效性")
        print("   - 当checkpoint_bool为True但没有checkpoint时需要更好的处理")
        
        print("\n4. 操作序列:")
        print("   - 应该验证自定义操作序列的有效性")
        print("   - 空序列或无效操作应该有明确的错误消息")
        
        print("\n5. 状态管理:")
        print("   - 需要确保状态在操作失败后正确恢复")
        print("   - 操作计数和历史记录应该准确维护")
        
        print("\n6. 性能考虑:")
        print("   - 长序列执行时的内存使用")
        print("   - 操作间延迟的合理性")
        
        if passed_tests == total_tests:
            print(f"\n✅ 所有核心测试通过！")
            return 0
        else:
            print(f"\n❌ 发现 {total_tests - passed_tests} 个测试失败")
            return 1
            
    except Exception as e:
        print(f"测试过程中出现严重错误: {e}")
        print(f"错误详情: {traceback.format_exc()}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
