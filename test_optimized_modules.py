#!/usr/bin/env python3
"""
Test script to verify that optimized modules work correctly.
Tests time.sleep, time.time and other functions in the context of the actual modules.
"""

import sys
import os
import time
import traceback
from typing import Dict, Any

# Add paths to import the modules
sys.path.append(os.path.join(os.path.dirname(__file__), 'task'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'buttonControl'))

class OptimizedModuleTester:
    """Test optimized modules without hardware dependencies."""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = time.time()
        print(f"OptimizedModuleTester initialized at {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    def test_button_processor_imports(self) -> Dict[str, Any]:
        """Test that button_processor can be imported and basic functions work."""
        test_name = "button_processor_imports"
        
        try:
            # Test importing the module
            import button_processor
            
            # Test that the module has expected classes
            assert hasattr(button_processor, 'ButtonController'), "ButtonController class not found"
            
            # Test time usage in the module context
            # We'll create a mock test that doesn't require hardware
            test_start = time.time()
            time.sleep(0.01)
            test_end = time.time()
            
            duration = test_end - test_start
            assert 0.005 <= duration <= 0.05, f"Time functions not working properly: {duration}"
            
            result = {
                'success': True,
                'module_imported': True,
                'has_button_controller': True,
                'time_test_duration': duration,
                'error': None
            }
            print(f"✓ {test_name}: button_processor imports and time functions work")
            
        except Exception as e:
            result = {
                'success': False,
                'error': str(e),
                'traceback': traceback.format_exc()
            }
            print(f"✗ {test_name}: Failed with error: {e}")
        
        self.test_results[test_name] = result
        return result
    
    def test_button_action_imports(self) -> Dict[str, Any]:
        """Test that button_action module imports and functions work."""
        test_name = "button_action_imports"
        
        try:
            # Test importing the module
            import button_action
            
            # Test that the module has expected functions
            expected_functions = [
                'search_targets', 'approach_button', 'click_button', 
                'turn_knob', 'restore_pose'
            ]
            
            for func_name in expected_functions:
                assert hasattr(button_action, func_name), f"Function {func_name} not found"
            
            # Test time usage in module context
            test_start = time.time()
            time.sleep(0.01)
            test_end = time.time()
            
            duration = test_end - test_start
            assert 0.005 <= duration <= 0.05, f"Time functions not working properly: {duration}"
            
            result = {
                'success': True,
                'module_imported': True,
                'functions_found': expected_functions,
                'time_test_duration': duration,
                'error': None
            }
            print(f"✓ {test_name}: button_action imports and time functions work")
            
        except Exception as e:
            result = {
                'success': False,
                'error': str(e),
                'traceback': traceback.format_exc()
            }
            print(f"✗ {test_name}: Failed with error: {e}")
        
        self.test_results[test_name] = result
        return result
    
    def test_finetune_imports(self) -> Dict[str, Any]:
        """Test that finetune module imports and functions work."""
        test_name = "finetune_imports"

        try:
            # Test importing the module through buttonControl package
            from buttonControl import finetune

            # Test that the module has expected classes/functions
            expected_items = ['perform_finetune_for_status']

            for item_name in expected_items:
                assert hasattr(finetune, item_name), f"Item {item_name} not found"

            # Test time usage in module context
            test_start = time.time()
            time.sleep(0.01)
            test_end = time.time()

            duration = test_end - test_start
            assert 0.005 <= duration <= 0.05, f"Time functions not working properly: {duration}"

            result = {
                'success': True,
                'module_imported': True,
                'items_found': expected_items,
                'time_test_duration': duration,
                'error': None
            }
            print(f"✓ {test_name}: finetune imports and time functions work")

        except Exception as e:
            result = {
                'success': False,
                'error': str(e),
                'traceback': traceback.format_exc()
            }
            print(f"✗ {test_name}: Failed with error: {e}")

        self.test_results[test_name] = result
        return result
    
    def test_data_manager_imports(self) -> Dict[str, Any]:
        """Test that data_manager module imports and functions work."""
        test_name = "data_manager_imports"

        try:
            # Test importing the module through buttonControl package
            from buttonControl import data_manager

            # Test that the module has expected classes
            assert hasattr(data_manager, 'DataManager'), "DataManager class not found"

            # Test time usage in module context
            test_start = time.time()
            time.sleep(0.01)
            test_end = time.time()

            duration = test_end - test_start
            assert 0.005 <= duration <= 0.05, f"Time functions not working properly: {duration}"

            result = {
                'success': True,
                'module_imported': True,
                'has_data_manager': True,
                'time_test_duration': duration,
                'error': None
            }
            print(f"✓ {test_name}: data_manager imports and time functions work")

        except Exception as e:
            result = {
                'success': False,
                'error': str(e),
                'traceback': traceback.format_exc()
            }
            print(f"✗ {test_name}: Failed with error: {e}")

        self.test_results[test_name] = result
        return result
    
    def test_time_functions_in_context(self) -> Dict[str, Any]:
        """Test time functions in various contexts similar to the actual usage."""
        test_name = "time_functions_in_context"
        
        try:
            # Test 1: time.sleep in try/except (common pattern in the modules)
            try:
                start_time = time.time()
                time.sleep(0.05)
                end_time = time.time()
                sleep_duration = end_time - start_time
                assert 0.04 <= sleep_duration <= 0.1, f"Sleep duration {sleep_duration} not in expected range"
            except Exception as e:
                raise Exception(f"time.sleep in try/except failed: {e}")
            
            # Test 2: time.time for timing operations (common pattern)
            operation_start = time.time()
            # Simulate some operation
            for i in range(1000):
                _ = i * 2
            operation_end = time.time()
            operation_duration = operation_end - operation_start
            assert operation_duration >= 0, "time.time timing failed"
            
            # Test 3: time.strftime for logging (used in modules)
            timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
            assert isinstance(timestamp, str), "time.strftime failed"
            assert len(timestamp) == 19, "time.strftime format incorrect"
            
            # Test 4: Multiple rapid time calls (stress test)
            times = []
            for i in range(10):
                times.append(time.time())
                time.sleep(0.001)  # 1ms sleep
            
            # Verify times are increasing
            for i in range(1, len(times)):
                assert times[i] > times[i-1], f"Time not increasing: {times[i]} <= {times[i-1]}"
            
            result = {
                'success': True,
                'sleep_duration': sleep_duration,
                'operation_duration': operation_duration,
                'timestamp': timestamp,
                'rapid_calls_count': len(times),
                'error': None
            }
            print(f"✓ {test_name}: All time functions work correctly in various contexts")
            
        except Exception as e:
            result = {
                'success': False,
                'error': str(e),
                'traceback': traceback.format_exc()
            }
            print(f"✗ {test_name}: Failed with error: {e}")
        
        self.test_results[test_name] = result
        return result
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all optimization verification tests."""
        print("=" * 60)
        print("Testing Optimized Modules")
        print("=" * 60)
        
        test_methods = [
            self.test_button_processor_imports,
            self.test_button_action_imports,
            self.test_finetune_imports,
            self.test_data_manager_imports,
            self.test_time_functions_in_context
        ]
        
        for test_method in test_methods:
            print(f"\nRunning {test_method.__name__}...")
            test_method()
        
        # Summary
        print("\n" + "=" * 60)
        print("Test Summary")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"Total tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        
        if failed_tests > 0:
            print("\nFailed tests:")
            for test_name, result in self.test_results.items():
                if not result['success']:
                    print(f"  - {test_name}: {result['error']}")
        
        total_duration = time.time() - self.start_time
        print(f"\nTotal test duration: {total_duration:.3f} seconds")
        
        return {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': failed_tests,
            'test_results': self.test_results,
            'total_duration': total_duration
        }

def main():
    """Main test function."""
    tester = OptimizedModuleTester()
    results = tester.run_all_tests()
    
    # Return appropriate exit code
    if results['failed_tests'] == 0:
        print("\n✓ All tests passed! Import optimization was successful.")
        print("✓ time.sleep, time.time and other functions work correctly.")
        return 0
    else:
        print(f"\n✗ {results['failed_tests']} tests failed. Import optimization may have issues.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
