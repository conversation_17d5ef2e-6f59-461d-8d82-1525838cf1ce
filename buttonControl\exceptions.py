#!/usr/bin/env python3
"""
ButtonControl exceptions module.
Defines custom exception classes for better error handling in buttonControl operations.
"""

# Import error codes from utils
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from utils import (
    BUTTON_DETECTION_ERROR, BUTTON_APPROACH_ERROR,
    BUTTON_OPERATION_ERROR, FINETUNE_ERROR,
    BUTTON_STATE_ERROR, BUTTON_CONFIG_ERROR,
    ARM_STATE_ERROR, ARM_MOVEMENT_ERROR,
    GRIPPER_STATE_ERROR, GRIPPER_CONTROL_ERROR,
    ARM_TRAJECTORY_TIMEOUT_ERROR,
    EMERGENCY_STOP_ERROR, CAMERA_ERROR,
    MEMORY_ERROR, UNKNOWN_ERROR
)


class ButtonControlException(Exception):
    """Base exception class for ButtonControl operations."""
    
    def __init__(self, message, error_code=None):
        super().__init__(message)
        self.error_code = error_code
        self.message = message


class ButtonDetectionError(ButtonControlException):
    """Exception raised when button/knob detection fails."""
    
    def __init__(self, message="Button/knob detection failed"):
        super().__init__(message, BUTTON_DETECTION_ERROR)


class ButtonApproachError(ButtonControlException):
    """Exception raised when button/knob approach fails."""
    
    def __init__(self, message="Button/knob approach failed"):
        super().__init__(message, BUTTON_APPROACH_ERROR)


class ButtonOperationError(ButtonControlException):
    """Exception raised when button click/knob turn fails."""
    
    def __init__(self, message="Button click/knob turn failed"):
        super().__init__(message, BUTTON_OPERATION_ERROR)


class FinetuneError(ButtonControlException):
    """Exception raised when finetune operation fails."""
    
    def __init__(self, message="Finetune operation failed"):
        super().__init__(message, FINETUNE_ERROR)


class ButtonStateError(ButtonControlException):
    """Exception raised when invalid button state/operation is attempted."""
    
    def __init__(self, message="Invalid button state/operation"):
        super().__init__(message, BUTTON_STATE_ERROR)


class ButtonConfigError(ButtonControlException):
    """Exception raised when button configuration is invalid."""
    
    def __init__(self, message="Button configuration error"):
        super().__init__(message, BUTTON_CONFIG_ERROR)


class ArmStateError(ButtonControlException):
    """Exception raised when arm state is invalid."""
    
    def __init__(self, message="Arm state error"):
        super().__init__(message, ARM_STATE_ERROR)


class ArmMovementError(ButtonControlException):
    """Exception raised when arm movement fails."""
    
    def __init__(self, message="Arm movement error"):
        super().__init__(message, ARM_MOVEMENT_ERROR)


class EmergencyStopError(ButtonControlException):
    """Exception raised when emergency stop is triggered."""
    
    def __init__(self, message="Emergency stop triggered"):
        super().__init__(message, EMERGENCY_STOP_ERROR)


class CameraError(ButtonControlException):
    """Exception raised when camera operation fails."""

    def __init__(self, message="Camera error"):
        super().__init__(message, CAMERA_ERROR)


class GripperStateError(ButtonControlException):
    """Exception raised when gripper state is invalid."""

    def __init__(self, message="Gripper state error"):
        super().__init__(message, GRIPPER_STATE_ERROR)


class GripperControlError(ButtonControlException):
    """Exception raised when gripper control fails."""

    def __init__(self, message="Gripper control error"):
        super().__init__(message, GRIPPER_CONTROL_ERROR)


class ArmTrajectoryTimeoutError(ButtonControlException):
    """Exception raised when arm trajectory times out."""

    def __init__(self, message="Arm trajectory timeout"):
        super().__init__(message, ARM_TRAJECTORY_TIMEOUT_ERROR)


class MemoryError(ButtonControlException):
    """Exception raised when memory operation fails."""

    def __init__(self, message="Memory error"):
        super().__init__(message, MEMORY_ERROR)


class UnknownError(ButtonControlException):
    """Exception raised for unknown errors."""

    def __init__(self, message="Unknown error"):
        super().__init__(message, UNKNOWN_ERROR)


def convert_error_to_exception(error_message: str) -> ButtonControlException:
    """
    Convert error message to appropriate exception type.
    
    Args:
        error_message: Error message string
        
    Returns:
        Appropriate ButtonControlException subclass
    """
    error_lower = error_message.lower()
    
    if "emergency stop" in error_lower:
        return EmergencyStopError(error_message)
    elif "detection" in error_lower or "detect" in error_lower:
        return ButtonDetectionError(error_message)
    elif "approach" in error_lower:
        return ButtonApproachError(error_message)
    elif "finetune" in error_lower or "fine-tune" in error_lower or "fine tune" in error_lower:
        return FinetuneError(error_message)
    elif "click" in error_lower or "turn" in error_lower or "operation" in error_lower:
        return ButtonOperationError(error_message)
    elif "arm" in error_lower and "trajectory" in error_lower and "timeout" in error_lower:
        return ArmTrajectoryTimeoutError(error_message)
    elif "arm" in error_lower and ("state" in error_lower or "status" in error_lower):
        return ArmStateError(error_message)
    elif "arm" in error_lower and ("movement" in error_lower or "move" in error_lower):
        return ArmMovementError(error_message)
    elif "gripper" in error_lower and ("state" in error_lower or "status" in error_lower):
        return GripperStateError(error_message)
    elif "gripper" in error_lower and ("control" in error_lower or "operation" in error_lower):
        return GripperControlError(error_message)
    elif "state" in error_lower or "status" in error_lower:
        return ButtonStateError(error_message)
    elif "config" in error_lower:
        return ButtonConfigError(error_message)
    elif "camera" in error_lower:
        return CameraError(error_message)
    elif "memory" in error_lower:
        return MemoryError(error_message)
    else:
        return UnknownError(error_message)


def handle_button_control_error(func):
    """
    Decorator to handle ButtonControl errors and convert them to appropriate exceptions.
    
    Args:
        func: Function to wrap
        
    Returns:
        Wrapped function that converts errors to ButtonControlException types
    """
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except ButtonControlException:
            # Re-raise ButtonControl exceptions as-is
            raise
        except Exception as e:
            # Convert other exceptions to ButtonControl exceptions
            raise convert_error_to_exception(str(e))
    
    return wrapper
