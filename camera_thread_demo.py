from task.pole_processor import *


class Camera:
    def __init__(self, visible=False):
        self.visible = visible
        self.arm = RoboticArm(rm_thread_mode_e.RM_TRIPLE_MODE_E)
        self.handle = self.arm.rm_create_robot_arm("************", 8080)
        arm_model = rm_robot_arm_model_e.RM_MODEL_RM_63_III_E  # RML63III-BI
        force_type = rm_force_type_e.RM_MODEL_RM_B_E
        self.algo_handle = Algo(arm_model, force_type)
        self.rob = RM63B()
        self.pipeline = rs.pipeline()
        self.config = rs.config()
        self.config.enable_stream(rs.stream.depth, 640, 480, rs.format.z16, 30)
        self.config.enable_stream(rs.stream.color, 640, 480, rs.format.bgr8, 30)

        self.arm.rm_set_timeout(1000)  # 1000ms = 10s
        self.rotation_matrix = np.array([  # 0701-13-20
            [-0.04198893, -0.99767839, -0.05361675],
            [0.99905672, -0.04252059, 0.00881333],
            [-0.01107268, -0.05319612, 0.99852269]
        ])
        self.translation_vector = np.array([0.08515154, -0.04080627, 0.01190365])
        print('prepare pipeline')
        self.profile = self.pipeline.start(self.config)
        print('ready to convey')
        device = self.profile.get_device()
        device.hardware_reset()
        print('device reset')
        for _ in range(5):
            frames = self.pipeline.wait_for_frames()
        # if self.visible:
        #     cv2.namedWindow('rgb')
        #     cv2.setMouseCallback('rgb', mouse_callback)
        self.depth_image, self.color_image = None, None
        self.display_img = None
        self.intr, self.depth_intrin = None, None
        pass

    def run(self):
        while True:
            time.sleep(0.01)
            frames = self.pipeline.wait_for_frames()
            align = rs.align(rs.stream.color)
            aligned_frames = align.process(frames)
            color_frame = aligned_frames.get_color_frame()
            depth_frame = aligned_frames.get_depth_frame()
            if not color_frame:
                continue
            if self.intr is None:
                self.intr = color_frame.profile.as_video_stream_profile().intrinsics  # 获取相机内参
                self.depth_intrin = depth_frame.profile.as_video_stream_profile(
                ).intrinsics
            self.depth_image = np.asanyarray(depth_frame.get_data())
            self.color_image = np.asanyarray(color_frame.get_data())


print("Initializing camera...")
cm = Camera(visible=True)
thread_cm = threading.Thread(target=cm.run)
thread_cm.daemon = True
thread_cm.start()

# Wait for camera to initialize
time.sleep(2)

# Initialize smoke pole
print("Initializing smoke pole...")
sp = SmokePole(cm, visible=True)

# Start detection thread (initially paused to save resources)
thread_sp = threading.Thread(target=sp.update_frames_and_detection)
thread_sp.daemon = True
thread_sp.start()
