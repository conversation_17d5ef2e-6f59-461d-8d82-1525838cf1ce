#  RMDemo_VisualStudio_Project


## **1. 简介**
本项目是一个使用Microsoft Visual Studio开发的C/C++项目，旨在演示如何将睿尔曼C语言版本的二次开发包集成到Visual Studio项目中。本Readme文档将指导用户如何配置环境、导入库文件、设置项目属性以及编译和运行项目。

## **2. 代码结构**
```
RMDemo_VisualStudio_Project
├── RMDemo_VisualStudio_Project.sln		# 解决方案文件
├── include
│   ├── rm_define.h  # 机械臂二次开发包头文件，包含了定义的数据类型、结构体
│   └── rm_interface.h # 机械臂二次开发包头文件，声明了机械臂所有操作接口
├── lib
│   ├── api_c.dll    # Windows 的 API 库（默认release 64bit）
│   └── api_c.lib    # Windows 的 API 库（默认release 64bit）
├── RMDemo_VisualStudio_Project		#源代码文件夹
└── readme.md            # 项目说明文档
```

## 3. 环境准备

- **Visual Studio**：
  - 安装适合C/C++开发的Visual Studio版本（如Visual Studio Community），推荐最新版本。
  - 在安装时，选择安装使用C++的桌面开发。
  - MSVC编译器要求2015以上
- **睿尔曼二次开发包**：下载链接


## **4. 项目步骤**

#### 项目配置

1. **创建新的C/C++项目**：

   - 打开Visual Studio，选择“创建新项目”。
   - 在“创建新项目”对话框中，选择“控制台应用”或适合你需求的C/C++项目类型，然后点击“下一步”。
   - 填写项目名称、位置等信息，然后点击“创建”。

2. **包含睿尔曼二次开发包头文件**：

   - 将睿尔曼开发包头文件及动态库文件复制到项目目录中。、

     ![image-20240723165905501](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20240723165905501.png)

   - 右键点击项目名称，打开“属性页”对话框。
   - 在左窗格中，选择“配置属性”>“C/C++”>“常规”。
   - 在“附加包含目录”下拉框点击编辑，在编辑控件中添加睿尔曼头文件的路径。可选择省略号 (...) 控件浏览到正确的文件夹，也可输入相对路径。

   ![image-20240723171553107](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20240723171553107.png)

3. **将DLL 导入库添加到项目中**：

   - 在项目属性页中，导航到“链接器” > “输入”。
   - 在“附加依赖项”中添加睿尔曼库的名称`api_c.lib`。
   - 选择“配置属性”>“链接器”>“常规”。选择“附加库目录”旁的下拉控件，然后选择“编辑”。
   - 在编辑控件中，指定指向 `api_c.lib` 文件位置的路径。

   ![image-20240723171455243](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20240723171455243.png)

4. **在生成后事件中复制 DLL**：

   将 DLL 复制到包含客户端可执行文件的目录中，作为生成过程的一部分。

   - 在项目属性页中，选择“配置属性”>“生成事件”>“生成后事件”。
   - 在”命令行“字段中选择编辑打开编辑控件，输入以下命令：

   `xcopy /y /d "..\lib\api_c.dll" "$(OutDir)"`

   ![image-20240723171727749](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20240723171727749.png)

5. **保存并关闭项目属性页**。

#### 编写代码

- 在项目中添加新的源文件或修改现有文件，以包含睿尔曼库的头文件，并编写调用睿尔曼库函数的代码。

#### 编译与运行

- 在Visual Studio中，选择“生成”菜单下的“生成解决方案”来编译项目。
- 确保没有编译错误，并且项目能够正确链接到睿尔曼库。
- 选择“调试”菜单下的“开始调试”或点击工具栏上的绿色播放按钮来运行你的应用程序。


## **5. 注意事项**

该Demo以RM65-B型号机械臂为例，请根据实际情况修改代码中的数据。


## **7. 许可证信息**

* 本项目遵循MIT许可证。

## **8. 常见问题解答（FAQ）**


- **链接错误**：检查是否已正确包含头文件及导入库。
- **运行时错误**：确保dll文件在应用程序的搜索路径中。
- **机械臂连接不上**：检查机械臂IP是否被修改。
- **机械臂运动失败**：检查机械臂型号，本示例基于RM65_B机械臂编写，其中的运动点位可能不适用于其他型号。
