import os.path
import datetime
import numpy as np
import cv2
import threading
import time
import math
from scipy.spatial.transform import Rotation as R
import pyrealsense2 as rs
from RM_API2.Python.Robotic_Arm.rm_robot_interface import RoboticArm, rm_thread_mode_e, rm_robot_arm_model_e, \
    rm_force_type_e, Algo, rm_inverse_kinematics_params_t
from RmRobotic.rm65 import RM63B
from detection.smoker_pole import pole_detection
from detection.detector import detectorDetection
from detection.calib_handler import calibrate_handler_detection
from utils import *
import yaml
import glob
import os
import datetime
import shutil
def delete_old_subdirectories(delta_days=1):
    base_dir = 'data/shot-images-saves'
    today = datetime.datetime.now()
    yesterday = today - datetime.timedelta(days=delta_days)
    print(f'delete the data directories before {yesterday}')

    # Ensure the base directory exists
    if not os.path.exists(base_dir):
        print(f"Directory '{base_dir}' does not exist.")
        return

    # List all sub-directories
    for subdir in os.listdir(base_dir):
        subdir_path = os.path.join(base_dir, subdir)

        # Check if it's a directory
        if os.path.isdir(subdir_path):
            try:
                # Extract the date and time from the directory name
                timestamp_str = subdir.split('_')[0]
                if len(timestamp_str) == 8:
                    subdir_date = datetime.datetime.strptime("2025"+timestamp_str, "%Y%m%d%H%M")
                elif len(timestamp_str) == 12:
                    subdir_date = datetime.datetime.strptime(timestamp_str, "%Y%m%d%H%M")
                else:
                    print(f"Unexpected timestamp format in directory '{subdir_path}'. Skipping.")
                    continue

                # Compare the date with yesterday
                if subdir_date <= yesterday:
                    # Delete the sub-directory
                    shutil.rmtree(subdir_path)
                    print(f"Deleted: {subdir_path}")
            except Exception as e:
                print(f"Error processing directory '{subdir_path}': {e}")


def load_config(config_path="config/pole_params.yaml"):
    """
    Load configuration parameters from YAML file.
    
    Args:
        config_path: Path to the configuration file
        
    Returns:
        Dictionary containing configuration parameters
    """
    try:
        with open(config_path, 'r') as file:
            config = yaml.safe_load(file)
            return config  # .get('pole', {})
    except Exception as e:
        print(f"Error loading configuration: {e}")
        return {}


def get_positions(detector, pole, scale=1.1):
    """
    Calculate target coordinates based on detector and pole positions.
    
    Args:
        detector: Detector coordinates [x, y, radius]
        pole: Pole coordinates [x, y, radius]
        scale: Scale factor for offset calculation
        
    Returns:
        Tuple of (target_coordinates, is_final_target)
    """
    # Extract coordinates and radii
    xd, yd, rd = np.int_(detector[:3])
    xp, yp, rp = pole[:3]
    xp -= rp
    
    # Calculate reference points
    offset = int(rd * scale)
    xm = xd + offset / 2
    Cx, Cy = xd, yd
    Ax, Ay = xd + offset, yd
    Ux, Uy = xd + offset, yd - int(offset * 1.11)
    Dx, Dy = xd + offset, yd + int(offset * 1.11)
    Lx, Ly = xd - offset, yd + int(offset * 1.11)
    target_coord = [Lx, Ly]
    final_target = False
    
    # Determine target position based on pole location
    if xp > xm:
        # Pole is to the right of midpoint - final target
        dx_sight, dy_sight = -1 * rp, int(-0.01 * rp)
        target_coord = [Ax + dx_sight, Ay + dy_sight]
        final_target = True
    elif (xp > Cx and yp > Cy) or (yp > Ay and xp < Ax and abs(yp - Ay) > 1 / 2.5 * abs(xp - Ax)):
        target_coord = [Dx, Dy]
        final_target = False
    elif (yp < Cy) and ((xp >= Cx) or (xp < Cx and abs(yp - Cy) > 101 / 40 * abs(xp - Cx))):
        target_coord = [Ux, Uy]
        final_target = False
    elif (yp < Cy) and (xp < Lx):
        target_coord = [min(xp + 5, Lx), Ly]
        final_target = False
        
    return target_coord, final_target


def circle_to_box(coord, scale=1.2):
    """
    Convert circle coordinates to box coordinates with scaling.
    
    Args:
        coord: Circle coordinates [x, y, radius]
        scale: Scale factor for box size
        
    Returns:
        Box coordinates [x1, y1, x2, y2]
    """
    x, y, r = coord[:3]
    r_enlarged = r * scale
    return [
        x - r_enlarged,  # x1
        y - r_enlarged,  # y1
        x + r_enlarged,  # x2
        y + r_enlarged   # y2
    ]


def calculate_iou(boxA, boxB):
    """
    Calculate Intersection over Union of two boxes.
    
    Args:
        boxA: First box [x1, y1, x2, y2]
        boxB: Second box [x1, y1, x2, y2]
        
    Returns:
        IoU ratio (0-1)
    """
    # Find intersection coordinates
    xA = max(boxA[0], boxB[0])
    yA = max(boxA[1], boxB[1])
    xB = min(boxA[2], boxB[2])
    yB = min(boxA[3], boxB[3])
    
    # Calculate areas
    inter_area = max(0, xB - xA) * max(0, yB - yA)
    boxA_area = (boxA[2] - boxA[0]) * (boxA[3] - boxA[1])
    boxB_area = (boxB[2] - boxB[0]) * (boxB[3] - boxB[1])
    union_area = boxA_area + boxB_area - inter_area

    return inter_area / union_area if union_area > 0 else 0


def abox_to_box(abox):
    """Convert angled box to axis-aligned bounding box."""
    r1, r2, r3, r4 = abox
    return [np.min([r1[0], r2[0], r3[0], r4[0]]), np.min([r1[1], r2[1], r3[1], r4[1]]),
            np.max([r1[0], r2[0], r3[0], r4[0]]), np.max([r1[1], r2[1], r3[1], r4[1]])]


def update_result(det_list, current_result, record_len, circle_flag=True):
    """
    Update detection result based on historical detections.
    
    Args:
        det_list: List of detection results
        current_result: Current detection result
        record_len: Minimum required record length
        circle_flag: Whether detections are circles (True) or rectangles (False)
        
    Returns:
        Updated detection result or None if unreliable
    """
    total = len(det_list)
    non_none_list = [x for x in det_list if x is not None]
    non_none_count = len(non_none_list)
    missing_rate = (total - non_none_count) / total

    # Check if we have enough records
    if total < 0.7 * record_len or non_none_count == 0:
        print(f'total({total}) < 0.7 * record_len({record_len}) or non_none_count({non_none_count})==0')
        return None

    # Condition 1: Missing rate too high
    if missing_rate >= 0.3:
        print(f'None due to missing_rate:{missing_rate} > 0.3')
        return None

    # Find latest non-None candidate
    candidate = next((x for x in reversed(det_list) if x is not None), None)
    if candidate is None:
        return None  # Shouldn't happen due to missing_rate check

    # Check candidate intersection rate with other detections
    if circle_flag:
        candidate_box = circle_to_box(candidate[:3])
    else:
        candidate_box = abox_to_box(candidate[:4])
    
    intersect_count = 0
    for item in non_none_list:
        if circle_flag:
            item_box = circle_to_box(item)
        else:
            item_box = abox_to_box(item[:4])
        if calculate_iou(candidate_box, item_box) > 0.3:
            intersect_count += 1

    # Condition 2: Candidate intersects with majority of detections
    if intersect_count / non_none_count > 0.5:
        return candidate

    # Check if current result is still valid
    if current_result is not None:
        if circle_flag:
            current_box = circle_to_box(current_result)
        else:
            current_box = abox_to_box(current_result[:4])
        
        intersect_count = 0
        for item in non_none_list:
            if circle_flag:
                item_box = circle_to_box(item)
            else:
                item_box = abox_to_box(item[:4])
                
            if calculate_iou(current_box, item_box) > 0.3:
                intersect_count += 1

        # Keep current result if it still intersects with majority
        if intersect_count / non_none_count > 0.5:
            return current_result
    print(f'None due to intersect_count:{intersect_count}/{non_none_count}={intersect_count / non_none_count}<0.5')
    return None


def mouse_callback(event, x, y, flags, param):
    """
    Handle mouse click events for depth camera visualization.
    
    Displays depth information and 3D coordinates when clicking on image.
    """
    if event == cv2.EVENT_LBUTTONDOWN:
        # Get depth if available
        depth_val = None
        depth_image = param['di']
        depth_intrin = param['depth_intrin']
        
        if depth_image is not None:
            depth_val = depth_image[y][x]
            
        # Print the clicked coordinates
        if depth_val is not None and depth_intrin is not None:
            print(f"Clicked at (x={x}, y={y}), depth={depth_val}mm")
            
            # Convert to 3D point using depth intrinsics
            point_3d = rs.rs2_deproject_pixel_to_point(
                depth_intrin,
                (float(x), float(y)),
                depth_val / 1000.0
            )
            print(f"3D coordinates: ({point_3d[0]:.3f}, {point_3d[1]:.3f}, {point_3d[2]:.3f}) meters")
        else:
            print(f"Clicked at (x={x}, y={y})")


class Camera:
    """
    Camera class for handling RealSense camera operations.
    
    Provides camera initialization, frame acquisition, and integration with robot arm.
    """
    def __init__(self, visible=False, config_path="config/camera_params.yaml", linux_mode=None):
        """Initialize camera and robot arm connections."""
        # Load configuration parameters
        try:
            config = load_config(config_path)
        except Exception as e:
            print(f"Error loading configuration: {e}, using default configuration")
            config = {}
            # return
        if linux_mode is None:
            import platform
            os_name = platform.system()
            if os_name == "Linux":
                linux_mode = True
                print(f"Current Platform System: {os_name}")
            else:
                linux_mode = False
                print(f"Current Platform System: {os_name}")
        self.linux_mode = linux_mode
        self.visible = visible
        self.arm = RoboticArm(rm_thread_mode_e.RM_TRIPLE_MODE_E)
        self.handle = self.arm.rm_create_robot_arm("************", 8080)
        arm_model = rm_robot_arm_model_e.RM_MODEL_RM_63_III_E  # RML63III-BI
        force_type = rm_force_type_e.RM_MODEL_RM_B_E
        self.algo_handle = Algo(arm_model, force_type)
        self.rob = RM63B()

        # confirm connection
        software_info = self.arm.rm_get_arm_software_info()
        if software_info[0] == 0:
            print("\n================== Arm Software Information ==================")
            print("Arm Model: ", software_info[1]['product_version'])
            print("Algorithm Library Version: ", software_info[1]['algorithm_info']['version'])
            print("Control Layer Software Version: ", software_info[1]['ctrl_info']['version'])
            print("Dynamics Version: ", software_info[1]['dynamic_info']['model_version'])
            print("Planning Layer Software Version: ", software_info[1]['plan_info']['version'])
            print("==============================================================\n")
        else:
            print("Failed to get arm software information, error code: ", software_info[0])

        self.pipeline = rs.pipeline()
        self.config = rs.config()
        self.config.enable_stream(rs.stream.depth, 640, 480, rs.format.z16, 30)
        self.config.enable_stream(rs.stream.color, 640, 480, rs.format.bgr8, 30)

        self.arm.rm_set_timeout(2000)  # 2000ms = 20s
        
        # Load camera calibration parameters from config or use defaults
        camera_rotation_matrix = config.get('camera_rotation_matrix')
        if camera_rotation_matrix:
            self.rotation_matrix = np.array(camera_rotation_matrix)
        else:
            self.rotation_matrix = np.array([  # Default values
                [-0.********, -0.********, -0.********],
                [0.********, -0.0392032, 0.********],
                [-0.00238501, -0.03280746, 0.99945884]
            ])
            
        camera_translation_vector = config.get('camera_translation_vector')
        if camera_translation_vector:
            self.translation_vector = np.array(camera_translation_vector)
        else:
            self.translation_vector = np.array([0.********, -0.********, 0.********])  # Default values

        # if self.visible:
        #     cv2.namedWindow('camera')
        
        # Initialize frame storage
        self.depth_image, self.color_image = None, None
        self.display_img = None
        self.intr, self.depth_intrin = None, None
        self.color_intrin = None
        self.profile = None
        self.init_camera()
    def init_camera(self):
        wait_count = 0
        if self.linux_mode:
            print('Waiting for camera initialization')
            video_devices = glob.glob(r'/dev/video*')
            while len(video_devices) < 3:
                wait_count += 1
                video_devices = glob.glob(r'/dev/video*')
                time.sleep(1)
                if wait_count % 60 == 0:
                    print(f'Wait for camera initialization: {wait_count//60} minutes. Detected devices: {video_devices}')
            pass
        print('prepare pipeline')
        self.profile = self.pipeline.start(self.config)
        print('ready to convey')
        # device = self.profile.get_device()
        # device.hardware_reset()
        # print('device reset')
        for _ in range(15):
            frames = self.pipeline.wait_for_frames()
        print('camera initialized')
        
    def reload_config(self, config_path="config/pole_params.yaml"):
        """
        Reload camera calibration parameters from the configuration file.
        
        Args:
            config_path: Path to the configuration file
            
        Returns:
            Dictionary of loaded configuration parameters
        """
        try:
            config = load_config(config_path)
        except Exception as e:
            print(f"Error loading configuration: {e}, using default configuration")
            # config = {}
            return
        
        # Update camera calibration parameters
        camera_rotation_matrix = config.get('camera_rotation_matrix')
        if camera_rotation_matrix:
            self.rotation_matrix = np.array(camera_rotation_matrix)
            
        camera_translation_vector = config.get('camera_translation_vector')
        if camera_translation_vector:
            self.translation_vector = np.array(camera_translation_vector)
            
        print(f"Camera configuration reloaded from {config_path}")
        return config

    def run(self):
        while True:
            if self.linux_mode:
                video_devices = glob.glob(r'/dev/video*')
                if len(video_devices) < 3:
                    self.color_image, self.depth_image = None, None
                    assert len(video_devices) != 0, "No camera found"
                    assert len(video_devices) != 1, f"Only camera {video_devices} found"
                    assert len(video_devices) >= 3, f"Only cameras {video_devices} found"
                pass
            time.sleep(0.01)
            frames = self.pipeline.wait_for_frames()
            align = rs.align(rs.stream.color)
            aligned_frames = align.process(frames)
            color_frame = aligned_frames.get_color_frame()
            depth_frame = aligned_frames.get_depth_frame()
            if not color_frame:
                continue
            if self.intr is None:
                self.intr = color_frame.profile.as_video_stream_profile().intrinsics  # 获取相机内参
                self.depth_intrin = depth_frame.profile.as_video_stream_profile(
                ).intrinsics
                self.color_intrin = self.intr
            self.depth_image = np.asanyarray(depth_frame.get_data())
            self.color_image = np.asanyarray(color_frame.get_data())
            if self.visible:
                cv2.imshow('camera', self.color_image)
                cv2.waitKey(1)


class SmokePole:
    def __init__(self, cm_obj, visible=True, _detector=None, len_pole=1150, config_path="config/pole_params.yaml"):
        # Load configuration parameters
        try:
            config = load_config(config_path)
        except Exception as e:
            print(f"Error loading configuration: {e}, using default configuration")
            config = {}
            # return

        self._cm = cm_obj
        self._runtime = ''
        self._detector_activate_flag = False  # False
        self.detector_model_path = config.get('detector_model_path', None)
        self.detector_conf_threshold = config.get('detector_conf_threshold', 0.7)
        if _detector is None:
            self._detector_model = detectorDetection(self.detector_model_path, self.detector_conf_threshold)
        else:
            self._detector_model = _detector
        self._detector_res_list = []
        self._pole_res_list = []
        self._detector_const_result = []
        self._pole_const_result = []
        
        # Load record length from config or use default
        self._record_len = config.get('record_len', 20)
        
        self._calibrate_const_result = None
        self._calibrate_res_list = []
        self._calibrate_control_flag = False
        self._calibrate_detect_flag = False
        self.go_pole_control_flag = False
        
        # Load handler calibration coordinates from config or use default
        calib_coords = config.get('calib_desired_coord', [358, 367])
        self._calib_desired_coord = np.array(calib_coords)
        
        self._offset_text = ''
        self._cmd = None
        self._detector_angle = None
        self._pause_mode = False  # True
        
        # Load deadband error from config or use default
        self._deadband_error = config.get('deadband_error', 1)
        
        self.visible = visible
        self.status = 0
        self.save_count = 0
        self.ci, self.di = None, None
        
        # Load pole length offset from config or use default
        self.pole_length_offset = config.get('pole_length_offset', 160)
        self.len_pole = len_pole + self.pole_length_offset
        self.max_space_distance = config.get('max_space_distance', 1000)
        self.approach_distance_ratio = config.get('approach_distance_ratio', 0.25)  # 0.2
        self.max_joint_distance_limit = config.get('max_joint_distance_limit', [4, 16])
        self.closer_speed_ratio = config.get('closer_speed_ratio', 1.333)
        self.distance_thresh = config.get('distance_thresh', [10, 8.5])
        self.detector_height = config.get('detector_height', 40)
        self.finish_count_max = config.get('finish_count_max', 2.8)

        # Load maximum end effector length from config or use default
        self.ep_max_len = config.get('ep_max_len', 0.965)
        
        # Load calibration rotation bias from config or use default
        self.calib_rotate_bias = config.get('calib_rotate_bias', 1.8435)
        self.detector_coord = None
        # Load detector size thresholds from config or use defaults
        self.min_detector_x = config.get('min_detector_x', 60)
        self.min_detector_y = config.get('min_detector_y', 100)
        self.min_detector_y_single = config.get('min_detector_y_single', 400)
        
        self._emergency_stop = False
        self._detection_paused = False
        self._frame_acquisition_paused = False
        self._task_status = "idle"  # idle, running, error, completed
        self._error_message = ""
        
        # Load wait time from config or use default
        self._wait_time = config.get('wait_time', 11)
        
        self.wait_message = ""
        self.photo_direct_mode = False

        self.detection_path = ""
        self.detection_result = False
        
        # Load joint positions from config or use defaults
        prepare_joints_config = config.get('prepare_joints', {})
        self.prepare_joints = [
            prepare_joints_config.get('zero', [2.103,75.533,-163.534,-7.251,163.381,-188.2]),
            prepare_joints_config.get('mid1', [-68.046,41.14,-88.29,4.985,57.12,-150.1]),
            prepare_joints_config.get('mid2', [-58.083,-6.031,116.647,-7.424,-113.524,2.418]),
            prepare_joints_config.get('mid3', [-41.886,59.685,83.682,0.966,-146.005,-39.81]),
            prepare_joints_config.get('prepared', [86.770, 50.139, 78.789, 0, -128.928, -75.597])
        ]
        
        j6 = self.prepare_joints[-1][-1]
        t_prepared_last = self._cm.rob.fkine(np.radians([self.prepare_joints[-1]])).A
        self._calib_x, self._calib_y, prepared_height = t_prepared_last[:3, 3].tolist()[:3]

        self.prepared_rad = R.from_matrix(t_prepared_last[:3, :3].tolist()).as_euler('xyz', degrees=False)[-1]
        
        # Load gravity offset from config or use default
        self._gravity_offset_l = config.get('gravity_offset_l', 0.001)
        self._gravity_offset_x, self._gravity_offset_y = -math.cos(
            self.prepared_rad) * self._gravity_offset_l, -math.sin(self.prepared_rad) * self._gravity_offset_l
            
        # Load raising heights from config or use defaults
        raising_height_grab = config.get('raising_height_grab', 0.055)
        raising_height_intermediate = config.get('raising_height_intermediate', 0.100)
        raising_height_outside = config.get('raising_height_outside', 0.160)
        raising_height_above = config.get('raising_height_above', 0.235)
        
        self.raising_heights = [
            prepared_height,  # 0, prepared position, ready to calibrate
            prepared_height + raising_height_grab,  # 1, grab position
            prepared_height + raising_height_intermediate,  # 2, raise to intermediate position
            prepared_height + raising_height_outside,  # 3, raised to position just outside the cylinder
            prepared_height + raising_height_above,  # 4, raised to position greatly above cylinder
        ]
        
        # Load safe_700_joint from config or use default
        self.safe_700_joint = config.get('safe_700_joint', [84.8, -32.608, 98.863, 0, -66.255, j6])
        self.safe_700_joint[-1] = j6
        
        self.text1, self.text2, self.text3 = '', '', ''
        
        # Load joint restrictions from config or use defaults
        self.max_joint_restrictions = np.array(config.get('max_joint_restrictions', [178, 178, 145, 178, 178, 358]))
        self.min_joint_restrictions = np.array(config.get('min_joint_restrictions', [-178, -178, -178, -178, -178, -358]))
        
        # Load start positions from config or use defaults
        start_positions_config = config.get('start_positions', {})
        self.start_positions = [
            start_positions_config.get('pos0', [80.377, -6.858, 73.697, -0.001, -66.839, j6]),
            start_positions_config.get('pos1', [121.01, -14.418, 80.15, 0.001, -65.727, j6]),
            start_positions_config.get('pos2', [61.01, -14.418, 80.15, 0.001, -65.727, j6]),
            start_positions_config.get('pos3', [30.432, -14.418, 80.15, 0.001, -65.727, j6]),
            start_positions_config.get('pos4', [0.01, -14.418, 80.15, 0.001, -65.727, j6]),
            start_positions_config.get('pos5', [-30.01, -14.418, 80.15, 0.001, -65.727, j6]),
            start_positions_config.get('pos6', [-60.01, -14.418, 80.15, 0.001, -65.727, j6]),
            start_positions_config.get('pos7', [-90.01, -14.418, 80.15, 0.001, -65.727, j6]),
            start_positions_config.get('pos8', [-120.01, -14.418, 80.15, 0.001, -65.727, j6]),
            start_positions_config.get('pos9', [-150.01, -14.418, 80.15, 0.001, -65.727, j6])
        ]
        for sp in self.start_positions:
            sp[-1] = j6
        default_position_config = config.get('default_observe_idx', 0)
        self.default_position_idx = default_position_config
        
        self.p0_joint = self.start_positions[0]
        
        # Load vertical joint from config or use default
        self.vertical_joint = config.get('vertical_joint', [0.0, -7.0, 17.7, 0.0, -10.6, 0.0])
        
        # Load unreachable action from config or use default
        self.unreachable_action = config.get('unreachable_action', 'continue')  # 'stop', 'continue'
    def joint_to_pose(self, joint):
        t = self._cm.rob.fkine(np.radians([joint])).A
        x, y, z = t[:3, 3].tolist()[:3]
        rx, ry, rz = R.from_matrix(t[:3, :3].tolist()).as_euler('xyz', degrees=False)
        return [x, y, z, rx, ry, rz]
        pass
    def pose_to_joint(self, current_joint, target_pose):
        # Calculate inverse kinematics
        q_out = self._cm.algo_handle.rm_algo_inverse_kinematics(
            rm_inverse_kinematics_params_t(current_joint, target_pose, 1))

        if q_out[0] != 0:
            print(f'Inverse kinematics error: {q_out[0]}')
            return None
        return q_out[1]

    def reset(self):
        """
        Reset the robot to a safe position.
        
        Returns:
            List containing [result_code, error_message_dict]
        """
        self.detection_path = ""
        self.detection_result = False
        try:
            current_state = self._cm.arm.rm_get_current_arm_state()
            self._cm.arm.rm_set_arm_delete_trajectory()
            reset_count, reset_count_max = 0, 50
            while current_state[0] != 0 or current_state[1]['err']['err'] != ['0']:
                if reset_count > reset_count_max:
                    self._error_message = f'[Cannot clear system error: {current_state}]'
                    return [ARM_STATE_ERROR, {'error_message': self._error_message}]
                time.sleep(0.1)
                reset_count += 1
                ret = self._cm.arm.rm_clear_system_err()
                if ret != 0:
                    continue
                time.sleep(0.4)
                current_state = self._cm.arm.rm_get_current_arm_state()
        except Exception as e:
            self._error_message = f'[Cannot clear system error({e})]'
            return [ARM_STATE_ERROR, {'error_message': self._error_message}]
        self._calibrate_detect_flag = False
        ret = self.move_to_zero_pose()
        if ret == 0:
            self._error_message = ""
            self._task_status = 'idle'

        trajectory_count = 0
        trajectory_break_count = 0
        while True:
            if self._emergency_stop:
                print('[[EMERGENCY_STOP--during trajectory check]]')
                self._error_message = f'[EMERGENCY_STOP--during trajectory check]'
                return [EMERGENCY_STOP_ERROR, {'error_message': self._error_message}]
            if trajectory_count > 240:
                self._error_message = "Timeout error in checking arm state"
                return [ARM_TRAJECTORY_TIMEOUT_ERROR, {'error_message': self._error_message}]
            trajectory_count += 1
            current_trajectory_state = self._cm.arm.rm_get_arm_current_trajectory()
            cur_err_count = 0
            while current_trajectory_state['return_code'] != 0:
                if cur_err_count > 160:
                    self._error_message = f'[Failed to get arm state after multiple attempts: {current_trajectory_state}]'
                    return [ARM_STATE_ERROR, {'error_message': self._error_message}]
                cur_err_count += 1
                if cur_err_count > 80:
                    print(f'- [{cur_err_count}]Error in get current state: {current_trajectory_state}')
                time.sleep(0.2)
                current_trajectory_state = self._cm.arm.rm_get_arm_current_trajectory()
            if current_trajectory_state['trajectory_type'] == 0:
                trajectory_break_count += 1
                if trajectory_break_count > 3:
                    break
                time.sleep(0.1)
                continue
            else:
                trajectory_break_count = 0
            time.sleep(0.25)
            if trajectory_count > 100:
                print(f'[{trajectory_count}] trajectory not stopped yet', current_trajectory_state)
        self._cm.arm.rm_get_current_arm_state()
        try:
            delete_old_subdirectories(1)
        except Exception as e:
            print(f'Failed old subdirectories error: {e}')
        return [ret, {'error_message': self._error_message}]

    def move_to_zero_pose(self):
        """
        Move the robot arm to the zero position safely.
        
        Handles different scenarios based on current gripper state:
        - If gripper is holding pole, moves through a sequence of safe positions
        - If gripper is not holding pole, moves directly to zero
        
        Returns:
            0 on success, error code on failure
        """
        # Check gripper state
        gripper_ret = self._cm.arm.rm_get_gripper_state()
        if gripper_ret[0] != 0 or gripper_ret[1]['error'] != 0:
            print("error get gripper state!")
            self._error_message = f'[{gripper_ret}]'
            return GRIPPER_CONTROL_ERROR
            
        if 400 < gripper_ret[1]['actpos'] < 990:
            # Gripper is grabbing pole
            print("gripper is grabbing pole")
            ret = self.back_to_grab_pose(block=True)
            if ret != 0:
                self._error_message += f'<{self.back_to_grab_pose.__name__}'
                return ret
                
            print("move(from grab) to prepared pose")
            ret = self.back_to_prepared_pose()
            if ret != 0:
                self._error_message += f'<{self.back_to_prepared_pose.__name__}'
                return ret
                
            print("move(from prepared) to zero pose")
            ret = self.back_to_zero_pose()
            if ret != 0:
                self._error_message += f'<{self.back_to_zero_pose.__name__}'
                return ret
                
            return 0
        else:
            # Gripper is not grabbing pole or is releasing
            if gripper_ret[1]['actpos'] <= 400:
                print("gripper is not grabbing pole")
                ret = self._cm.arm.rm_set_gripper_release(150, True, 10)
                if ret in [-4, -5]:
                    print(f"[WARNING]: gripper return value as {ret}, regard it as available")
                    time.sleep(1.5)
                elif ret != 0:
                    self._error_message = f'[{ret}]'
                    return GRIPPER_CONTROL_ERROR
            else:
                print("gripper is releasing")
                
            # Get current arm state
            current_state = self._cm.arm.rm_get_current_arm_state()
            cur_err_count = 0
            while current_state[0] != 0:
                if cur_err_count > 20:
                    self._error_message = f'[Failed to get arm state after multiple attempts: {current_state}]'
                    return ARM_STATE_ERROR  # Error code for arm state error
                cur_err_count += 1
                print('error in get current state')
                time.sleep(0.8)
                current_state = self._cm.arm.rm_get_current_arm_state()

            # Determine movement strategy based on current position
            current_pose = current_state[1]['pose']
            if self._calib_x - 150 / 1000 < current_pose[0] < self._calib_x + 150 / 1000 and \
                    self._calib_y - 150 / 1000 < current_pose[1] < self._calib_y + 150 / 1000 and \
                    current_pose[2] < self.raising_heights[1] + 20 / 1000:
                # Near prepared position - use back_to_zero_pose
                print("move(from prepared) to zero pose")
                straight_down = True if current_pose[2] > self.raising_heights[0] - 20 / 1000 else False
                ret = self.back_to_zero_pose(straight_down)
                if ret != 0:
                    self._error_message += f'<{self.back_to_zero_pose.__name__}'
                    return ret
            else:
                # Not near prepared position - use safer_move
                print("move(vertical) to zero pose")
                ret = self.safer_move(self.prepare_joints[0])
                if ret != 0:
                    self._error_message += f'<{self.safer_move.__name__}'
                    return ret

        return 0

    def move_zero_to_prepared_pose(self):
        ret = self.move_arm('rm_movej', (self.prepare_joints[0], 22, 50, 1, 0))
        if ret != 0:
            return ret
        for pj in self.prepare_joints[:-1]:
            ret = self.move_arm('rm_movej', (pj, 18, 50, 1, 0))
            if ret != 0:
                return ret
        self._cm.arm.rm_get_current_arm_state()
        ret = self.move_arm('rm_movej', (self.prepare_joints[-1], 18, 50, 0, 1))
        if ret != 0:
            return ret
        ret = self.move_arm('rm_movel', (
            [self._calib_x, self._calib_y, self.raising_heights[0], 0.00, 0.00, self.prepared_rad], 10, 5, 0, 1))
        if ret != 0:
            return ret
        time.sleep(0.5)
        self._cm.arm.rm_get_current_arm_state()

        # trajectory_count = 0
        # trajectory_break_count = 0
        # while True:
        #     if self._emergency_stop:
        #         print('[[EMERGENCY_STOP--during trajectory check]]')
        #         self._error_message = f'[EMERGENCY_STOP--during trajectory check]'
        #         return EMERGENCY_STOP_ERROR
        #     if trajectory_count > 240:
        #         self._error_message = "Timeout error in checking arm state"
        #         return ARM_TRAJECTORY_TIMEOUT_ERROR
        #     trajectory_count += 1
        #     current_trajectory_state = self._cm.arm.rm_get_arm_current_trajectory()
        #     cur_err_count = 0
        #     while current_trajectory_state['return_code'] != 0:
        #         if cur_err_count > 160:
        #             self._error_message = f'[Failed to get arm state after multiple attempts: {current_trajectory_state}]'
        #             return ARM_STATE_ERROR
        #         cur_err_count += 1
        #         if cur_err_count > 80:
        #             print(f'- [{cur_err_count}]Error in get current state: {current_trajectory_state}')
        #         time.sleep(0.2)
        #         current_trajectory_state = self._cm.arm.rm_get_arm_current_trajectory()
        #     if current_trajectory_state['trajectory_type'] == 0:
        #         trajectory_break_count += 1
        #         if trajectory_break_count > 3:
        #             break
        #         time.sleep(0.1)
        #         continue
        #     time.sleep(0.25)
        #     if trajectory_count > 100:
        #         print(f'[{trajectory_count}] trajectory not stopped yet', current_trajectory_state)
        return 0

    def go_grab(self):
        ret = self.move_arm('rm_movel', (
            [self._calib_x, self._calib_y, self.raising_heights[1], 0.00, 0.00, self.prepared_rad], 20, 0, 0, 1))
        if ret != 0:
            return ret
        time.sleep(1.05)

        trajectory_count = 0
        trajectory_break_count = 0
        while True:
            if self._emergency_stop:
                print('[[EMERGENCY_STOP--during trajectory check]]')
                self._error_message = f'[EMERGENCY_STOP--during trajectory check]'
                return EMERGENCY_STOP_ERROR
            if trajectory_count > 240:
                return ARM_TRAJECTORY_TIMEOUT_ERROR
            trajectory_count += 1
            current_trajectory_state = self._cm.arm.rm_get_arm_current_trajectory()
            cur_err_count = 0
            while current_trajectory_state['return_code'] != 0:
                if cur_err_count > 160:
                    self._error_message = f'[Failed to get arm state after multiple attempts: {current_trajectory_state}]'
                    return ARM_STATE_ERROR
                cur_err_count += 1
                if cur_err_count > 80:
                    print(f'- [{cur_err_count}]Error in get current state: {current_trajectory_state}')
                time.sleep(0.2)
                current_trajectory_state = self._cm.arm.rm_get_arm_current_trajectory()
            if current_trajectory_state['trajectory_type'] == 0:
                trajectory_break_count += 1
                if trajectory_break_count > 3:
                    break
                time.sleep(0.1)
                continue
            else:
                trajectory_break_count = 0
            time.sleep(0.25)
            if trajectory_count > 100:
                print(f'[{trajectory_count}] trajectory not stopped yet', current_trajectory_state)
        self._cm.arm.rm_get_current_arm_state()
        # ret = self._cm.arm.rm_set_gripper_pick_on(300, 800, True, 10)
        ret = self._cm.arm.rm_set_gripper_pick(300, 1000, True, 10)
        if ret in [-4, -5]:
            print(f"[WARNING]: gripper return value as {ret}, regard it as available")
            time.sleep(1.5)
        elif ret != 0:
            self._error_message = f'[{ret}]'
            return GRIPPER_CONTROL_ERROR
        return 0

    def raise_to_work_pose(self):
        ret = self.move_arm('rm_movel', (
            [self._calib_x, self._calib_y, self.raising_heights[1], 0.00, 0.00, self.prepared_rad], 20, 0, 0, 1))
        if ret != 0:
            return ret
        ret = self.move_arm('rm_movel', (
            [self._calib_x + self._gravity_offset_x / 2, self._calib_y + self._gravity_offset_y / 2,
             self.raising_heights[3], 0.00, 0.00, self.prepared_rad], 20, 0, 0, 1))
        if ret != 0:
            return ret
        # ret = self.move_arm('rm_movel', (
        #     [self._calib_x + self._gravity_offset_x, self._calib_y + self._gravity_offset_y, self.raising_heights[3],
        #      0.00, 0.00, self.prepared_rad], 20, 0, 0, 1))
        # if ret != 0:
        #     return ret
        # ret = self.move_arm('rm_movel', (
        #     [self._calib_x + self._gravity_offset_x, self._calib_y + self._gravity_offset_y, self.raising_heights[4],
        #      0.00, 0.00, self.prepared_rad], 20, 0, 0, 1))
        # if ret != 0:
        #     return ret
        # raise to mid, ready to work
        ret = self.move_arm('rm_movej', (self.p0_joint, 15, 50, 0, 1))
        if ret != 0:
            return ret
        return 0

    def safe_back_to_p0(self, block=True, joints=None):
        """
        Safely move the robot arm back to the p0 (home) position.
        
        This function first lowers the arm if it's too high, then calls to_p0()
        to complete the movement to the home position.
        
        Returns:
            0 on success, error code on failure
        """
        # Get current arm state
        block_flag = block or (joints is not None and len(joints) == 0)
        if block_flag:
            current_state = self._cm.arm.rm_get_current_arm_state()
            cur_err_count = 0
            while current_state[0] != 0:
                if cur_err_count > 20:
                    self._error_message = f'[Failed to get arm state after multiple attempts: {current_state}]'
                    return ARM_STATE_ERROR
                cur_err_count += 1
                print('error in get current state')
                time.sleep(0.8)
                current_state = self._cm.arm.rm_get_current_arm_state()

            # Lower the arm if it's too high (above 0.75m)
            end_pose = current_state[1]['pose']
        else:
            end_pose = self.joint_to_pose(joints[-1])

        if end_pose[2] > 0.75:
            # Calculate safe lowering height (max 8cm at a time)
            z_restore = max(0.75, end_pose[2] - 0.1)
            end_pose[2] = z_restore
            # Move to lower height
            if block_flag:
                ret = self.move_arm('rm_movel', (end_pose, 20, 5, 0, 1))
            else:
                ret = self.move_arm('rm_movej_p', (end_pose, 20, 5, 1, 0))
                joint = self.pose_to_joint(joints[-1], end_pose)
                joints.append(joint)
            if ret != 0:
                return ret
                
        # Move to p0 position
        ret = self.to_p0(block, joints)
        if ret != 0:
            self._error_message += f'<{self.to_p0.__name__}'
            return ret
            
        return 0

    def back_to_grab_pose(self, block=True, joints=[]):
        """
        Move the robot arm back to the grab pose from any position.
        
        This function safely returns the robot to the grab position where it can
        release the pole. It handles different starting positions and ensures
        a collision-free path.
        
        Returns:
            0 on success, error code on failure
        """
        # Get current arm state
        current_state = self._cm.arm.rm_get_current_arm_state()
        cur_err_count = 0
        while current_state[0] != 0:
            if cur_err_count > 20:
                self._error_message = f'[Failed to get arm state after multiple attempts: {current_state}]'
                return ARM_STATE_ERROR
            cur_err_count += 1
            print('error in get current state')
            time.sleep(0.8)
            current_state = self._cm.arm.rm_get_current_arm_state()

        # Check if arm is already near the calibration position
        current_pose = current_state[1]['pose']
        current_joint = current_state[1]['joint']
        if not (self._calib_x - 150 / 1000 < current_pose[0] < self._calib_x + 150 / 1000 and
                self._calib_y - 150 / 1000 < current_pose[1] < self._calib_y + 150 / 1000 and
                self.raising_heights[1] < current_pose[2] < self.raising_heights[4] - 2 / 1000):
            # If not near calibration position, first return to p0
            print("move(from prepared) to zero pose")
            joints.append(current_joint)
            print('safe_back_to_p0')
            ret = self.safe_back_to_p0(block, joints)
            if ret != 0:
                self._error_message += f'<{self.safe_back_to_p0.__name__}'
                return ret

            # Move to p0 position
            print('movej to p0')
            if block:
                ret = self.move_arm('rm_movej', (self.p0_joint, 12, 10, 0, 1))
            else:
                ret = self.move_arm('rm_movej', (self.p0_joint, 12, 10, 1, 0))
            if ret != 0:
                return ret
            trajectory_count = 0
            trajectory_break_count = 0
            while True:
                if self._emergency_stop:
                    print('[[EMERGENCY_STOP--during trajectory check]]')
                    self._error_message = f'[EMERGENCY_STOP--during trajectory check]'
                    return EMERGENCY_STOP_ERROR
                if trajectory_count > 600:
                    self._error_message = "Timeout error in checking arm state"
                    return ARM_TRAJECTORY_TIMEOUT_ERROR
                trajectory_count += 1
                current_trajectory_state = self._cm.arm.rm_get_arm_current_trajectory()
                cur_err_count = 0
                while current_trajectory_state['return_code'] != 0:
                    if cur_err_count > 320:
                        self._error_message = f'[Failed to get arm state after multiple attempts: {current_trajectory_state}]'
                        return ARM_STATE_ERROR
                    cur_err_count += 1
                    if cur_err_count > 160:
                        print(f'- [{cur_err_count}]Error in get current state: {current_trajectory_state}')
                    time.sleep(0.1)
                    current_trajectory_state = self._cm.arm.rm_get_arm_current_trajectory()
                if current_trajectory_state['trajectory_type'] == 0:
                    trajectory_break_count += 1
                    if trajectory_break_count > 3:
                        break
                    time.sleep(0.1)
                    continue
                else:
                    trajectory_break_count = 0
                time.sleep(0.15)
                if trajectory_count > 250:
                    print(f'[{trajectory_count}] trajectory not stopped yet', current_trajectory_state)


            print('movej_p to rh4')
            # Move to raised position above cylinder
            ret = self.move_arm('rm_movej_p', (
                [self._calib_x + self._gravity_offset_x, self._calib_y + self._gravity_offset_y,
                 self.raising_heights[4],
                 0.00, 0.00, self.prepared_rad], 12, 10, 0,
                1))
            if ret != 0:
                return ret
            current_state = self._cm.arm.rm_get_current_arm_state()
            # Update current state after movement
            current_state = self._cm.arm.rm_get_current_arm_state()
            cur_err_count = 0
            while current_state[0] != 0:
                if cur_err_count > 20:
                    self._error_message = f'[Failed to get arm state after multiple attempts: {current_state}]'
                    return ARM_STATE_ERROR
                cur_err_count += 1
                print('error in get current state')
                time.sleep(0.8)
                current_state = self._cm.arm.rm_get_current_arm_state()

            current_pose = current_state[1]['pose']
            time.sleep(0.05)  # 0.15
            
        # Lower to position just outside the cylinder
        if current_pose[2] > self.raising_heights[3]:
            print('move to rh3')
            ret = self.move_arm('rm_movel', (
                [self._calib_x + self._gravity_offset_x, self._calib_y + self._gravity_offset_y,
                 self.raising_heights[3],
                 0.00, 0.00, self.prepared_rad], 20, 5, 0,
                1))  # Lower to position outside the cylinder
            if ret != 0:
                return ret
                
            # Update current state after movement
            current_state = self._cm.arm.rm_get_current_arm_state()
            cur_err_count = 0
            while current_state[0] != 0:
                if cur_err_count > 20:
                    self._error_message = f'[Failed to get arm state after multiple attempts: {current_state}]'
                    return ARM_STATE_ERROR
                cur_err_count += 1
                print('error in get current state')
                time.sleep(0.8)
                current_state = self._cm.arm.rm_get_current_arm_state()

            current_pose = current_state[1]['pose']
            time.sleep(0.05)  # 0.1, 0.5
        
        # Lower to intermediate position
        if current_pose[2] > self.raising_heights[2]:
            print('move to rh2')
            ret = self.move_arm('rm_movel', (
                [self._calib_x + self._gravity_offset_x / 2, self._calib_y + self._gravity_offset_y / 2,
                 self.raising_heights[2], 0.00, 0.00, self.prepared_rad], 25, 5, 0,
                1))  # Lower to intermediate position
            if ret != 0:
                return ret

            # Update current state after movement
            current_state = self._cm.arm.rm_get_current_arm_state()
            cur_err_count = 0
            while current_state[0] != 0:
                if cur_err_count > 20:
                    self._error_message = f'[Failed to get arm state after multiple attempts: {current_state}]'
                    return ARM_STATE_ERROR
                cur_err_count += 1
                print('error in get current state')
                time.sleep(0.8)
                current_state = self._cm.arm.rm_get_current_arm_state()

            current_pose = current_state[1]['pose']
            # time.sleep(0.2)

        # Ensure we're at the correct height
        grip_block_count = 0
        print('waif for Z <= rh2')
        while current_pose[2] > self.raising_heights[2] + 5 / 1000:
            grip_block_count += 1
            if grip_block_count > 500:
                self._error_message = f'[Failed to get arm blocked moving command {current_state}]'
                return ARM_STATE_ERROR

            current_state = self._cm.arm.rm_get_current_arm_state()
            current_state = self._cm.arm.rm_get_current_arm_state()
            cur_err_count = 0
            while current_state[0] != 0:
                if cur_err_count > 40:
                    self._error_message = f'[Failed to get arm state after multiple attempts: {current_state}]'
                    return ARM_STATE_ERROR
                cur_err_count += 1
                print('error in get current state')
                time.sleep(0.4)
                current_state = self._cm.arm.rm_get_current_arm_state()
            time.sleep(0.1)
            current_pose = current_state[1]['pose']
        print('confirm Z <= rh2')
        time.sleep(0.1)
        # time.sleep(0.1)
        # Check gripper state and release slightly
        gripper_ret = self._cm.arm.rm_get_gripper_state()
        if gripper_ret[0] != 0 or gripper_ret[1]['error'] != 0:
            print("error get gripper state!")
            self._error_message = f'[{gripper_ret}]'
            return GRIPPER_STATE_ERROR
        print('gripper_release')
        ret = self._cm.arm.rm_set_gripper_release(80, False, 10)  # non-block  # 100
        if ret in [-4, -5]:
            print(f"[WARNING]: gripper return value as {ret}, regard it as available")
            time.sleep(1.5)
        elif ret != 0:
            self._error_message = f'[{ret}]'
            return GRIPPER_CONTROL_ERROR

        # Lower to grab position
        if current_pose[2] > self.raising_heights[1]:
            print('move to rh1')
            ret = self.move_arm('rm_movel', (
                [self._calib_x, self._calib_y, self.raising_heights[1], 0.00, 0.00, self.prepared_rad], 20, 0, 0,
                1))  # Lower to grab position
            if ret != 0:
                return ret
                
        return 0
    def release_gripper(self):
        ret = self._cm.arm.rm_set_gripper_release(80, True, 10)  # non-block  # 100
        if ret in [-4, -5]:
            print(f"[WARNING]: gripper return value as {ret}, regard it as available")
            time.sleep(1.5)
        elif ret != 0:
            self._error_message = f'Error during release gripper: [{ret}]'
            return GRIPPER_CONTROL_ERROR, self._error_message
        return ret, ''
    def back_to_prepared_pose(self):
        ret = self._cm.arm.rm_set_gripper_release(150, False, 10)
        if ret in [-4, -5]:
            print(f"[WARNING]: gripper return value as {ret}, regard it as available")
            time.sleep(1.5)
        elif ret != 0:
            self._error_message = f'[{ret}]'
            return GRIPPER_CONTROL_ERROR
        ret = self.move_arm('rm_movel', (
            [self._calib_x, self._calib_y, self.raising_heights[0], 0.00, 0.00, self.prepared_rad], 20, 5, 0, 1))
        if ret != 0:
            return ret
        return 0

    def back_to_zero_pose(self, to_prepared=True):
        if to_prepared:
            ret = self.move_arm('rm_movel', (
                [self._calib_x, self._calib_y, self.raising_heights[0], 0.00, 0.00, self.prepared_rad], 20, 5, 0, 1))
            if ret != 0:
                return ret
        ret = self.move_arm('rm_movej', (self.prepare_joints[-1], 10, 50, 1, 0))
        if ret != 0:
            return ret
        for pj in self.prepare_joints[::-1]:
            ret = self.move_arm('rm_movej', (pj, 18, 50, 1, 0))
            if ret != 0:
                return ret
        ret = self.move_arm('rm_movej', (self.prepare_joints[0], 20, 50, 0, 1))
        if ret != 0:
            return ret
        self._cm.arm.rm_get_current_arm_state()

        trajectory_count = 0
        trajectory_break_count = 0
        while True:
            if self._emergency_stop:
                print('[[EMERGENCY_STOP--during trajectory check]]')
                self._error_message = f'[EMERGENCY_STOP--during trajectory check]'
                return EMERGENCY_STOP_ERROR
            if trajectory_count > 240:
                self._error_message = "Timeout error in checking arm state"
                return ARM_TRAJECTORY_TIMEOUT_ERROR
            trajectory_count += 1
            current_trajectory_state = self._cm.arm.rm_get_arm_current_trajectory()
            cur_err_count = 0
            while current_trajectory_state['return_code'] != 0:
                if cur_err_count > 160:
                    self._error_message = f'[Failed to get arm state after multiple attempts: {current_trajectory_state}]'
                    return ARM_STATE_ERROR
                cur_err_count += 1
                if cur_err_count > 80:
                    print(f'- [{cur_err_count}]Error in get current state: {current_trajectory_state}')
                time.sleep(0.2)
                current_trajectory_state = self._cm.arm.rm_get_arm_current_trajectory()
            if current_trajectory_state['trajectory_type'] == 0:
                trajectory_break_count += 1
                if trajectory_break_count > 3:
                    break
                time.sleep(0.05)
                continue
            else:
                trajectory_break_count = 0
            time.sleep(0.25)

            if trajectory_count > 100:
                print(f'[{trajectory_count}] trajectory not stopped yet', current_trajectory_state)
        return 0

    def offset_calculate(self, rotaion_mat, _current_feature, _desired_feature, _delta_z, _lambda=-0.003):
        error = _current_feature - _desired_feature
        mean_error = np.linalg.norm(error)
        if np.all((mean_error) <= self._deadband_error):
            error = np.array([0, 0])
        dcxy = _lambda * error.reshape((2, 1))
        z_error = -_delta_z
        if abs(z_error) <= 1e-2:
            z_error = 0.0
        dp = np.array([dcxy[0][0], dcxy[1][0], -_lambda * z_error, 0.0, 0.0, 0.0]).T

        self._offset_text = f'[{error[0]:.1f}, {error[1]:.1f}, {z_error:.1f}]'
        Rv = np.vstack((np.hstack((rotaion_mat, np.zeros((3, 3)))),
                        np.hstack((np.zeros((3, 3)), rotaion_mat))))
        v = np.dot(Rv, dp)
        return v, np.sqrt(np.sum(np.array(error) ** 2) + z_error ** 2)

    def calibrate_at_prepared_pose(self):
        """
        Calibrate the robot position at the prepared pose.
        
        Uses visual feedback to align the robot with the calibration target.
        Continuously adjusts position until the target is centered in the camera view.
        
        Returns:
            0 on success, error code on failure
        """
        # Initialize calibration parameters
        timer_period = 10.0 * 1e-3  # Control period in seconds, 10.0 * 1e-3
        z_desired_value = self.raising_heights[0] * 1000  # Target height in mm
        first_rotation_flag = True  # Flag for initial rotation correction
        finish_count, finish_count_max = 0.0, 2.0  # Counter for confirming calibration completion
        no_result_count, no_result_count_max = 0, 300  # Counter for missing calibration target
        angle_list_len, angle_list = 20, []  # List to current rotation angles
        first_complete = False
        overall_count, overall_count_max = 0, 30000
        while True:
            time.sleep(0.01)
            # Skip if paused
            if self._pause_mode:
                continue
            overall_count += 1
            if overall_count > overall_count_max:
                print('killing -- due to TimeOut of Calibration')
                self._error_message = f'TimeOut of Calibration'
                return NO_POLE_HANDLER_ERROR
                
            # Check if calibration is still active
            if not self._calibrate_control_flag:
                print('killing -- due to _calibrate_control_flag')
                self._error_message = f'killing -- due to _calibrate_control_flag'
                return EMERGENCY_STOP_ERROR
                
            # Wait for sufficient calibration data
            if len(self._calibrate_res_list) < self._record_len:
                print(f'wait for len(calib_res_list)={len(self._calibrate_res_list)} < {self._record_len}')
                continue
                
            # Check if calibration target is detected
            if self._calibrate_const_result is None:
                no_result_count += 1
                if no_result_count % 5 == 0:
                    print(f'No detection result...{no_result_count}/{no_result_count_max}')
                if no_result_count > no_result_count_max:
                    print('killing -- due to no_result_count')
                    self._error_message = f'no result for {no_result_count} times'
                    return NO_POLE_HANDLER_ERROR
                continue
            no_result_count = max(0, no_result_count - 10)
            
            # Extract calibration points
            _calibrate_pts_tmp = [self._calibrate_const_result[-2], self._calibrate_const_result[
                -1]] if self._calibrate_const_result is not None else None
                
            # Skip if calibration points are invalid or too far from target
            if _calibrate_pts_tmp is None or np.linalg.norm(
                    _calibrate_pts_tmp[0][:2] - self._calib_desired_coord) > 200:
                print(f'calib result is too far from roi:{_calibrate_pts_tmp[0][:2]}, {self._calib_desired_coord}')
                continue

            # Get current arm state
            current_state = self._cm.arm.rm_get_current_arm_state()
            cur_err_count = 0
            while current_state[0] != 0:
                if cur_err_count > 20:
                    self._error_message = f'[Failed to get arm state after multiple attempts: {current_state}]'
                    return ARM_STATE_ERROR
                cur_err_count += 1
                print('error in get current state')
                time.sleep(0.8)
                current_state = self._cm.arm.rm_get_current_arm_state()

            # Extract current pose information
            end_pose = current_state[1]['pose']
            end_joint = current_state[1]['joint']
            z_value = end_pose[2] * 1000  # Convert to mm
            
            # Calculate transformation matrices
            joints_rad = [math.radians(item) for item in end_joint]
            mat_end_to_base = np.eye(4)  # base_to_hand
            mat_end_to_base[:3, :3] = R.from_euler('xyz', end_pose[3:], degrees=False).as_matrix()
            mat_end_to_base[:3, 3] = end_pose[:3]
            mat_camera_to_end = np.eye(4)
            mat_camera_to_end[:3, :3] = self._cm.rotation_matrix
            mat_camera_to_end[:3, 3] = self._cm.translation_vector
            camera_to_base = mat_end_to_base.dot(mat_camera_to_end)
            rotaion_mat = camera_to_base[:3, :3]
            
            # Calculate rotation angle for alignment
            rotate_angle = math.degrees(
                math.atan2((_calibrate_pts_tmp[1][1] - _calibrate_pts_tmp[0][1]),
                           (_calibrate_pts_tmp[1][0] - _calibrate_pts_tmp[0][0])))
            rotate_angle += self.calib_rotate_bias
            # Calculate stable rotation angle
            angle_list.append(rotate_angle)
            angle_list = angle_list[-angle_list_len:]
            stable_rotate_angle = np.mean(angle_list) if len(angle_list) >= min(3, angle_list_len) else 0.0
            self.text1 = f'{rotate_angle:.2}/{stable_rotate_angle:.2f}'
            # First correct rotation if needed
            if abs(stable_rotate_angle) > 1.0 or (first_rotation_flag and abs(stable_rotate_angle) > 0.2):
                angle_list = []
                first_rotation_flag = False
                z_angle = np.degrees(end_pose[-1])
                z_angle += stable_rotate_angle
                rotate_end_pose = end_pose.copy()
                rotate_end_pose[-1] = math.radians(z_angle)
                ret = self.move_arm('rm_movej_p', (rotate_end_pose, 8, 50, 0, 1))
                if ret != 0:
                    return ret
                # Reset calibration history after rotation
                self._calibrate_res_list = self._calibrate_res_list[-int(self._record_len * 0.3):]
                print(f'Rotated to angle {z_angle}')
                continue
                
            # Calculate position adjustment
            velocity, d_distance = self.offset_calculate(rotaion_mat, _current_feature=_calibrate_pts_tmp[0][:2],
                                                         _desired_feature=self._calib_desired_coord[:2],
                                                         _delta_z=-(z_value - z_desired_value), _lambda=0.003)
                                                         
            # Check if target position is reached
            if d_distance <= 5 and len(angle_list) >= angle_list_len - 1:
                finish_count += 1
                if finish_count > finish_count_max:
                    if not first_complete:
                        first_complete = True
                        first_rotation_flag = False
                        print('Calibration-1st done')
                        continue
                    self._calibrate_control_flag = False
                    self._calib_x, self._calib_y = end_pose[:2]
                    print('Calibration-2nd done')
                    break
            else:
                finish_count = max(0, finish_count - 0.2)
                
            # Calculate joint velocities using Jacobian
            jacobian = self._cm.rob.jacob0(joints_rad)
            jacobian_inv = np.linalg.pinv(jacobian)
            dq = np.dot(jacobian_inv, velocity)
            vector = np.concatenate([np.array(matrix).ravel() for matrix in dq])

            # Calculate new joint positions
            q = np.degrees(joints_rad + vector.T * timer_period)
            q_calibrated = np.where(q > self.max_joint_restrictions, self.max_joint_restrictions, q)
            q = np.where(q_calibrated < self.min_joint_restrictions, self.min_joint_restrictions, q_calibrated)

            # Scale down movement if too large
            total_joint_distance = np.sqrt(np.sum((np.array(q[:-1]) - np.array(end_joint[:-1])) ** 2))
            tmp_timer_period = timer_period
            while total_joint_distance > 1:
                tmp_timer_period *= 0.8
                q = np.degrees(joints_rad + vector.T * tmp_timer_period)
                q_calibrated = np.where(q > self.max_joint_restrictions, self.max_joint_restrictions, q)
                q = np.where(q_calibrated < self.min_joint_restrictions, self.min_joint_restrictions, q_calibrated)
                total_joint_distance = np.sqrt(np.sum((np.array(q[:-1]) - np.array(end_joint[:-1])) ** 2))
                
            # Execute movement
            ret = self.move_arm('rm_movej_canfd', (q, False, 0, 0, 50))  # True for high-follow, False for low-follow (≥10ms)
            if ret != 0:
                return ret

            print(f'[{overall_count}] calib: {datetime.datetime.now().strftime("%M%S")}')
            self._calibrate_const_result = None
            
        return SUCCESS_CODE

    def move_arm(self, move_type, args):
        if self._emergency_stop:
            print('[[EMERGENCY_STOP--0]]')
            self._error_message = f'[EMERGENCY_STOP]'
            return EMERGENCY_STOP_ERROR
        ret_code = 0
        if move_type == 'rm_movej':
            # ret = self._cm.arm.rm_movej(self.prepare_joints[0], 20, 50, 0, 1)
            # ret = self.move_arm(rm_movej, (self.prepare_joints[0], 20, 50, 0, 1))
            ret = self._cm.arm.rm_movej(args[0], args[1], args[2], args[3], args[4])
            if ret != 0 and ret != -3:
                ret_code = ARM_MOVEMENT_ERROR
                self._error_message = f'[rm_movej{(args[0], args[1], args[2], args[3], args[4])}: {ret}]'
            if ret == -3:
                if self._emergency_stop:
                    print('[[EMERGENCY_STOP]]')
                    self._error_message = f'[EMERGENCY_STOP]'
                    return EMERGENCY_STOP_ERROR
                ret_3_count = 0
                while ret == -3:
                    if ret_3_count > 150:
                        print("-3:", ret_3_count, ret, (args[0], args[1], args[2], args[3], args[4]))
                    if ret_3_count > 500:
                        # ret_code = ARM_MOVEMENT_ERROR
                        # self._error_message = f'[rm_movej{(args[0], args[1], args[2], args[3], args[4])}: {ret}]'
                        break
                    time.sleep(0.1)
                    ret_3_count += 1
                    ret = self._cm.arm.rm_movej(args[0], args[1], args[2], args[3], args[4])

        elif move_type == 'rm_movej_p':
            ret = self._cm.arm.rm_movej_p(args[0], args[1], args[2], args[3], args[4])
            if ret != 0 and ret != -3:
                ret_code = ARM_MOVEMENT_ERROR
                self._error_message = f'[rm_movej_p{(args[0], args[1], args[2], args[3], args[4])}: {ret}]'
            if ret == -3:
                if self._emergency_stop:
                    print('[[EMERGENCY_STOP]]')
                    self._error_message = f'[EMERGENCY_STOP]'
                    return EMERGENCY_STOP_ERROR
                ret_3_count = 0
                while ret == -3:
                    if ret_3_count > 150:
                        print("-3:", ret_3_count, ret, (args[0], args[1], args[2], args[3], args[4]))
                    if ret_3_count > 500:
                        # ret_code = ARM_MOVEMENT_ERROR
                        # self._error_message = f'[rm_movej_p{(args[0], args[1], args[2], args[3], args[4])}: {ret}]'
                        break
                    time.sleep(0.1)
                    ret_3_count += 1
                    ret = self._cm.arm.rm_movej_p(args[0], args[1], args[2], args[3], args[4])
        elif move_type == 'rm_movel':
            ret = self._cm.arm.rm_movel(args[0], args[1], args[2], args[3], args[4])
            if ret != 0 and ret != -3:
                ret_code = ARM_MOVEMENT_ERROR
                self._error_message = f'[rm_movel{(args[0], args[1], args[2], args[3], args[4])}: {ret}]'
            if ret == -3:
                if self._emergency_stop:
                    print('[[EMERGENCY_STOP]]')
                    self._error_message = f'[EMERGENCY_STOP]'
                    return EMERGENCY_STOP_ERROR
                ret_3_count = 0
                while ret == -3:
                    if ret_3_count > 150:
                        print("-3:", ret_3_count, ret, (args[0], args[1], args[2], args[3], args[4]))
                    if ret_3_count > 500:
                        # ret_code = ARM_MOVEMENT_ERROR
                        # self._error_message = f'[rm_movel{(args[0], args[1], args[2], args[3], args[4])}: {ret}]'
                        break
                    time.sleep(0.1)
                    ret_3_count += 1
                    ret = self._cm.arm.rm_movel(args[0], args[1], args[2], args[3], args[4])
        elif move_type == 'rm_movej_canfd':
            ret = self._cm.arm.rm_movej_canfd(args[0], args[1], args[2], args[3], args[4])
            if ret != 0:
                ret_code = ARM_MOVEMENT_ERROR
                self._error_message = f'[rm_movej_canfd{(args[0], args[1], args[2], args[3], args[4])}: {ret}]'
        else:
            ret_code = UNKNOWN_ERROR
            self._error_message = '[Unknown move type]'
        if self._emergency_stop:
            print('[[EMERGENCY_STOP--1]]')
            self._error_message = f'[EMERGENCY_STOP]'
            return EMERGENCY_STOP_ERROR
        if len(args) > 5 and args[5] == True:
            pass
        return ret_code

    def work_procedure(self):
        """
        Execute the complete smoke pole testing procedure.
        
        Sequence:
        1. Move to zero position
        2. Move to prepared position
        3. Calibrate pole position
        4. Grab the pole
        5. Raise to working position
        6. Find suitable starting position
        7. Move to smoke detector
        8. Wait for specified time
        9. Return to grab position
        10. Return to prepared position
        11. Return to zero position
        
        Returns:
            List containing [result_code, error_message_dict]
        """
        # Set initial state
        self._task_status = "running"
        self._error_message, self.wait_message = "", ""
        self._cm.arm.rm_set_timeout(2000)
        self._emergency_stop = False
        self._calibrate_control_flag = False
        self.go_pole_control_flag = False
        self._calibrate_detect_flag = False
        self.detection_path = ""
        self.detection_result = False
        self._detector_activate_flag = False
        self.default_position_idx = 0
        self._cm.arm.rm_get_current_arm_state()

        # 1. Move to zero position
        print('(0) moving to zero')
        ret = self.move_to_zero_pose()
        if ret != 0:
            self._error_message = f"Failed to move to zero pose, error code: {ret}"
            self._task_status = "error"
            return [ret, {'error_message': self._error_message}]

        trajectory_count = 0
        trajectory_break_count = 0
        while True:
            if self._emergency_stop:
                print('[[EMERGENCY_STOP--during trajectory check]]')
                self._error_message = f'[EMERGENCY_STOP--during trajectory check]'
                return [EMERGENCY_STOP_ERROR, {'error_message': self._error_message}]
            if trajectory_count > 240:
                self._error_message = "Timeout error in checking arm state"
                return [ARM_TRAJECTORY_TIMEOUT_ERROR, {'error_message': self._error_message}]
            trajectory_count += 1
            current_trajectory_state = self._cm.arm.rm_get_arm_current_trajectory()
            cur_err_count = 0
            while current_trajectory_state['return_code'] != 0:
                if cur_err_count > 160:
                    self._error_message = f'[Failed to get arm state after multiple attempts: {current_trajectory_state}]'
                    return [ARM_STATE_ERROR, {'error_message': self._error_message}]
                cur_err_count += 1
                if cur_err_count > 80:
                    print(f'- [{cur_err_count}]Error in get current state: {current_trajectory_state}')
                time.sleep(0.2)
                current_trajectory_state = self._cm.arm.rm_get_arm_current_trajectory()
            if current_trajectory_state['trajectory_type'] == 0:
                trajectory_break_count += 1
                if trajectory_break_count > 3:
                    break
                time.sleep(0.15)
                continue
            else:
                trajectory_break_count = 0
            time.sleep(0.25)
        self._cm.arm.rm_get_current_arm_state()

        print('[0] moved to zero')
        # 2. Move to prepared position
        print('(1) moving to prepared pose')
        ret = self.move_zero_to_prepared_pose()
        if ret != 0:
            self._error_message = f"Failed to move to prepared pose, error code: {ret}"
            self._task_status = "error"
            return [ret, {'error_message': self._error_message}]
        self._calibrate_detect_flag = True

        trajectory_count = 0
        trajectory_break_count = 0
        while True:
            if self._emergency_stop:
                print('[[EMERGENCY_STOP--during trajectory check]]')
                self._error_message = f'[EMERGENCY_STOP--during trajectory check]'
                return [EMERGENCY_STOP_ERROR, {'error_message': self._error_message}]
            if trajectory_count > 240:
                self._error_message = "Timeout error in checking arm state"
                return [ARM_TRAJECTORY_TIMEOUT_ERROR, {'error_message': self._error_message}]
            trajectory_count += 1
            current_trajectory_state = self._cm.arm.rm_get_arm_current_trajectory()
            cur_err_count = 0
            while current_trajectory_state['return_code'] != 0:
                if cur_err_count > 160:
                    self._error_message = f'[Failed to get arm state after multiple attempts: {current_trajectory_state}]'
                    return [ARM_STATE_ERROR, {'error_message': self._error_message}]
                cur_err_count += 1
                if cur_err_count > 80:
                    print(f'- [{cur_err_count}]Error in get current state: {current_trajectory_state}')
                time.sleep(0.2)
                current_trajectory_state = self._cm.arm.rm_get_arm_current_trajectory()
            if current_trajectory_state['trajectory_type'] == 0:
                trajectory_break_count += 1
                if trajectory_break_count > 3:
                    break
                time.sleep(0.15)
                continue
            else:
                trajectory_break_count = 0
            time.sleep(0.25)
        self._cm.arm.rm_get_current_arm_state()
        print('[1] moved to prepared pose.')
        time.sleep(0.25)
        
        # 3. Calibrate pole position (two attempts)
        print('(2) start to calibrate-1')
        self._calibrate_control_flag = True
        calib_ret = self.calibrate_at_prepared_pose()
        if calib_ret != 0:
            self._calibrate_detect_flag = False
            self._calibrate_control_flag = False
            self._error_message = "Calibration failed"
            self._task_status = "error"
            return [calib_ret, {'error_message': self._error_message}]

        # print('(2) start to calibrate-2')
        # time.sleep(0.2)
        # self._calibrate_control_flag = True
        # calib_ret2 = self.calibrate_at_prepared_pose()
        # time.sleep(0.1)
        # if calib_ret2 != 0:
        #     self._error_message = "Calibration failed"
        #     self._task_status = "error"
        #     return [calib_ret, {'error_message': self._error_message}]

        trajectory_count = 0
        trajectory_break_count = 0
        while True:
            if self._emergency_stop:
                print('[[EMERGENCY_STOP--during trajectory check]]')
                self._error_message = f'[EMERGENCY_STOP--during trajectory check]'
                return [EMERGENCY_STOP_ERROR, {'error_message': self._error_message}]
            if trajectory_count > 240:
                self._error_message = "Timeout error in checking arm state"
                return [ARM_TRAJECTORY_TIMEOUT_ERROR, {'error_message': self._error_message}]
            trajectory_count += 1
            current_trajectory_state = self._cm.arm.rm_get_arm_current_trajectory()
            cur_err_count = 0
            while current_trajectory_state['return_code'] != 0:
                if cur_err_count > 160:
                    self._error_message = f'[Failed to get arm state after multiple attempts: {current_trajectory_state}]'
                    return [ARM_STATE_ERROR, {'error_message': self._error_message}]
                cur_err_count += 1
                if cur_err_count > 80:
                    print(f'- [{cur_err_count}]Error in get current state: {current_trajectory_state}')
                time.sleep(0.2)
                current_trajectory_state = self._cm.arm.rm_get_arm_current_trajectory()
            if current_trajectory_state['trajectory_type'] == 0:
                trajectory_break_count += 1
                if trajectory_break_count > 3:
                    break
                time.sleep(0.15)
                continue
            else:
                trajectory_break_count = 0
            time.sleep(0.25)
        self._calibrate_control_flag = False
        self._calibrate_detect_flag = False
        self._cm.arm.rm_get_current_arm_state()
        print('[2] calibration done')
            
        # 4. Grab the pole
        print('(3) going to grab')
        ret = self.go_grab()
        if ret is not None and ret != 0:
            self._error_message += f"Failed to grab, error code: {ret}"
            self._task_status = "error"
            return [ret, {'error_message': self._error_message}]

        trajectory_count = 0
        trajectory_break_count = 0
        while True:
            if self._emergency_stop:
                print('[[EMERGENCY_STOP--during trajectory check]]')
                self._error_message = f'[EMERGENCY_STOP--during trajectory check]'
                return [EMERGENCY_STOP_ERROR, {'error_message': self._error_message}]
            if trajectory_count > 240:
                self._error_message = "Timeout error in checking arm state"
                return [ARM_TRAJECTORY_TIMEOUT_ERROR, {'error_message': self._error_message}]
            trajectory_count += 1
            current_trajectory_state = self._cm.arm.rm_get_arm_current_trajectory()
            cur_err_count = 0
            while current_trajectory_state['return_code'] != 0:
                if cur_err_count > 160:
                    self._error_message = f'[Failed to get arm state after multiple attempts: {current_trajectory_state}]'
                    return [ARM_STATE_ERROR, {'error_message': self._error_message}]
                cur_err_count += 1
                if cur_err_count > 80:
                    print(f'- [{cur_err_count}]Error in get current state: {current_trajectory_state}')
                time.sleep(0.2)
                current_trajectory_state = self._cm.arm.rm_get_arm_current_trajectory()
            if current_trajectory_state['trajectory_type'] == 0:
                trajectory_break_count += 1
                if trajectory_break_count > 3:
                    break
                time.sleep(0.15)
                continue
            else:
                trajectory_break_count = 0
            time.sleep(0.25)
        self._cm.arm.rm_get_current_arm_state()
        print('[3] grabbed')
        time.sleep(0.1)
        
        # 5. Raise to working position
        print('(4) raising to work pose')
        ret = self.raise_to_work_pose()
        if ret is not None and ret != 0:
            self._error_message = f"Failed to raise to work pose, error code: {ret}"
            self._task_status = "error"
            return [ret, {'error_message': self._error_message}]

        trajectory_count = 0
        trajectory_break_count = 0
        while True:
            if self._emergency_stop:
                print('[[EMERGENCY_STOP--during trajectory check]]')
                self._error_message = f'[EMERGENCY_STOP--during trajectory check]'
                return [EMERGENCY_STOP_ERROR, {'error_message': self._error_message}]
            if trajectory_count > 240:
                self._error_message = "Timeout error in checking arm state"
                return [ARM_TRAJECTORY_TIMEOUT_ERROR, {'error_message': self._error_message}]
            trajectory_count += 1
            current_trajectory_state = self._cm.arm.rm_get_arm_current_trajectory()
            cur_err_count = 0
            while current_trajectory_state['return_code'] != 0:
                if cur_err_count > 160:
                    self._error_message = f'[Failed to get arm state after multiple attempts: {current_trajectory_state}]'
                    return [ARM_STATE_ERROR, {'error_message': self._error_message}]
                cur_err_count += 1
                if cur_err_count > 80:
                    print(f'- [{cur_err_count}]Error in get current state: {current_trajectory_state}')
                time.sleep(0.2)
                current_trajectory_state = self._cm.arm.rm_get_arm_current_trajectory()
            if current_trajectory_state['trajectory_type'] == 0:
                trajectory_break_count += 1
                if trajectory_break_count > 3:
                    break
                time.sleep(0.15)
                continue
            else:
                trajectory_break_count = 0
            time.sleep(0.25)
        self._cm.arm.rm_get_current_arm_state()
        print('[4] raised to work pose')
        # time.sleep(0.25)

        gripper_ret = self._cm.arm.rm_get_gripper_state()
        if gripper_ret[0] != 0 or gripper_ret[1]['error'] != 0:
            print("Error get gripper state")
            self._error_message = f'Error get gripper state: [{gripper_ret}]'
            return [GRIPPER_CONTROL_ERROR, {'error_message': self._error_message}]
        if gripper_ret[1]['actpos'] < 400:
            print("Gripper didn't grab successfully")
            ret = self._cm.arm.rm_set_gripper_release(150, True, 10)
            if ret in [-4, -5]:
                print(f"[WARNING]: gripper return value as {ret}, regard it as available")
                time.sleep(1.5)
            elif ret != 0:
                self._error_message = f'Gripper didnt grab successfully: [{ret}]'
                return [GRIPPER_CONTROL_ERROR, {'error_message': self._error_message}]
        # time.sleep(0.25)
        self._cm.arm.rm_get_current_arm_state()
        
        # 6. Find suitable starting position
        print('(f) finding suitable start position')
        find_code, find_ret = self.find_suitable_start_position()
        if find_code == POLE_UNREACHABLE_ERROR and self.unreachable_action == 'stop':
            self._error_message += "<Could not find suitable start position"
            self._task_status = "error"
            self._detector_activate_flag = False
            return [find_code, {'error_message': self._error_message, 'data': {"offset": self.detector_coord}}]
        if not find_ret and find_code == 0:
            self._error_message += "<Could not find suitable start position"
            self._task_status = "error"
            self._detector_activate_flag = False
            # ret = self.reset()
            if ret[0] != 0:
                self._error_message += f"<Reset_error:{ret}"
            return [NO_DETECTOR_ERROR, {'error_message': self._error_message, 'data': {"offset": self.detector_coord}}]
        elif find_code != 0:
            self._detector_activate_flag = False
            self._error_message += "<Error in find_suitable_start_position"
            self._task_status = "error"
            return [find_code, {'error_message': self._error_message, 'data': {"offset": self.detector_coord}}]
        print('[f] found suitable start position')
        self._detector_activate_flag = True
        trajectory_count = 0
        trajectory_break_count = 0
        while True:
            if self._emergency_stop:
                print('[[EMERGENCY_STOP--during trajectory check]]')
                self._error_message = f'[EMERGENCY_STOP--during trajectory check]'
                return [EMERGENCY_STOP_ERROR, {'error_message': self._error_message}]
            if trajectory_count > 240:
                self._error_message = "Timeout error in checking arm state"
                return [ARM_TRAJECTORY_TIMEOUT_ERROR, {'error_message': self._error_message}]
            trajectory_count += 1
            current_trajectory_state = self._cm.arm.rm_get_arm_current_trajectory()
            cur_err_count = 0
            while current_trajectory_state['return_code'] != 0:
                if cur_err_count > 160:
                    self._error_message = f'[Failed to get arm state after multiple attempts: {current_trajectory_state}]'
                    return [ARM_STATE_ERROR, {'error_message': self._error_message}]
                cur_err_count += 1
                if cur_err_count > 80:
                    print(f'- [{cur_err_count}]Error in get current state: {current_trajectory_state}')
                time.sleep(0.2)
                current_trajectory_state = self._cm.arm.rm_get_arm_current_trajectory()
            if current_trajectory_state['trajectory_type'] == 0:
                trajectory_break_count += 1
                if trajectory_break_count > 3:
                    break
                time.sleep(0.15)
                continue
            else:
                trajectory_break_count = 0
            time.sleep(0.25)
        self._cm.arm.rm_get_current_arm_state()
        # time.sleep(0.5)
        
        # 7. Move to smoke detector
        print('(s) going to smoke detector')
        self.go_pole_control_flag = True
        pole_ret = self.go_smoke_pole()
        if pole_ret != 0 and not (pole_ret == POLE_UNREACHABLE_ERROR and self.unreachable_action == 'continue'):
            self._detector_activate_flag = False
            self.go_pole_control_flag = False
            self._error_message += "<Failed to reach smoke detector"
            self._task_status = "error"
            if pole_ret == POLE_UNREACHABLE_ERROR:
                print("Cannot reach the smoke detector, ready to reset, params: self.unreachable_action == 'stop'")
                time.sleep(2.5)
            return [pole_ret, {'error_message': self._error_message, 'data': {"offset": self.detector_coord}}]
        print('[s] arrived at smoke detector')
        self._detector_activate_flag = False
        self.go_pole_control_flag = False

        # 8. Wait for specified time
        for time_i in range(self._wait_time):
            self.wait_message = f'{time_i + 1}/{self._wait_time} seconds...'
            if self._emergency_stop:
                self._error_message = "Emergency stop triggered during smoke detector testing"
                self._task_status = "error"
                return [EMERGENCY_STOP_ERROR, {'error_message': self._error_message}]
            time.sleep(1)
            print(f'{time_i + 1}/{self._wait_time} seconds...')

        self.wait_message = ""
        
        # 9. Return to grab position
        print('(5) going back to grab pose')
        ret = self.back_to_grab_pose(block=True)
        if ret != 0:
            self._error_message += f"<Failed to return to grab pose, error code: {ret}"
            self._task_status = "error"
            return [ret, {'error_message': self._error_message}]
        trajectory_count = 0
        trajectory_break_count = 0
        while True:
            if self._emergency_stop:
                print('[[EMERGENCY_STOP--during trajectory check]]')
                self._error_message = f'[EMERGENCY_STOP--during trajectory check]'
                return [EMERGENCY_STOP_ERROR, {'error_message': self._error_message}]
            if trajectory_count > 240:
                self._error_message = "Timeout error in checking arm state"
                return [ARM_TRAJECTORY_TIMEOUT_ERROR, {'error_message': self._error_message}]
            trajectory_count += 1
            current_trajectory_state = self._cm.arm.rm_get_arm_current_trajectory()
            cur_err_count = 0
            while current_trajectory_state['return_code'] != 0:
                if cur_err_count > 160:
                    self._error_message = f'[Failed to get arm state after multiple attempts: {current_trajectory_state}]'
                    return [ARM_STATE_ERROR, {'error_message': self._error_message}]
                cur_err_count += 1
                if cur_err_count > 80:
                    print(f'- [{cur_err_count}]Error in get current state: {current_trajectory_state}')
                time.sleep(0.2)
                current_trajectory_state = self._cm.arm.rm_get_arm_current_trajectory()
            if current_trajectory_state['trajectory_type'] == 0:
                trajectory_break_count += 1
                if trajectory_break_count > 3:
                    break
                time.sleep(0.15)
                continue
            else:
                trajectory_break_count = 0
            time.sleep(0.25)
        self._cm.arm.rm_get_current_arm_state()
        print('[5] back to grab pose')
        
        # 10. Return to prepared position
        print('(6) going back to prepared pose')
        ret = self.back_to_prepared_pose()
        if ret is not None and ret != 0:
            self._error_message = f"Failed to return to prepared pose, error code: {ret}"
            self._task_status = "error"
            return [ret, {'error_message': self._error_message}]
        trajectory_count = 0
        trajectory_break_count = 0
        while True:
            if self._emergency_stop:
                print('[[EMERGENCY_STOP--during trajectory check]]')
                self._error_message = f'[EMERGENCY_STOP--during trajectory check]'
                return [EMERGENCY_STOP_ERROR, {'error_message': self._error_message}]
            if trajectory_count > 240:
                self._error_message = "Timeout error in checking arm state"
                return [ARM_TRAJECTORY_TIMEOUT_ERROR, {'error_message': self._error_message}]
            trajectory_count += 1
            current_trajectory_state = self._cm.arm.rm_get_arm_current_trajectory()
            cur_err_count = 0
            while current_trajectory_state['return_code'] != 0:
                if cur_err_count > 160:
                    self._error_message = f'[Failed to get arm state after multiple attempts: {current_trajectory_state}]'
                    return [ARM_STATE_ERROR, {'error_message': self._error_message}]
                cur_err_count += 1
                if cur_err_count > 80:
                    print(f'- [{cur_err_count}]Error in get current state: {current_trajectory_state}')
                time.sleep(0.2)
                current_trajectory_state = self._cm.arm.rm_get_arm_current_trajectory()
            if current_trajectory_state['trajectory_type'] == 0:
                trajectory_break_count += 1
                if trajectory_break_count > 3:
                    break
                time.sleep(0.15)
                continue
            else:
                trajectory_break_count = 0
            time.sleep(0.25)
        self._cm.arm.rm_get_current_arm_state()
        print('[6] back to prepared pose')
        
        # 11. Return to zero position
        print('(7) going back to zero pose')
        ret = self.back_to_zero_pose()
        if ret is not None and ret != 0:
            self._error_message = f"Failed to return to zero pose, error code: {ret}"
            self._task_status = "error"
            return [ret, {'error_message': self._error_message}]
        print('[7] back to zero pose')
        
        # Task completed successfully
        self._task_status = "completed"
        time.sleep(1.0)
        trajectory_count = 0
        trajectory_break_count = 0
        while True:
            if self._emergency_stop:
                print('[[EMERGENCY_STOP--during trajectory check]]')
                self._error_message = f'[EMERGENCY_STOP--during trajectory check]'
                return [EMERGENCY_STOP_ERROR, {'error_message': self._error_message}]
            if trajectory_count > 240:
                return [ARM_TRAJECTORY_TIMEOUT_ERROR, {'error_message': "Timeout error in checking arm state"}]
            trajectory_count += 1
            current_trajectory_state = self._cm.arm.rm_get_arm_current_trajectory()
            cur_err_count = 0
            while current_trajectory_state['return_code'] != 0:
                if cur_err_count > 160:
                    self._error_message = f'[Failed to get arm state after multiple attempts: {current_trajectory_state}]'
                    return [ARM_STATE_ERROR, {'error_message': self._error_message}]
                cur_err_count += 1
                if cur_err_count > 80:
                    print(f'- [{cur_err_count}]Error in get current state: {current_trajectory_state}')
                time.sleep(0.2)
                current_trajectory_state = self._cm.arm.rm_get_arm_current_trajectory()
            if current_trajectory_state['trajectory_type'] == 0:
                trajectory_break_count += 1
                if trajectory_break_count > 3:
                    break
                time.sleep(0.15)
                continue
            else:
                trajectory_break_count = 0
            time.sleep(0.25)
            if trajectory_count > 100:
                print(f'[{trajectory_count}] trajectory not stopped yet', current_trajectory_state)
        self._cm.arm.rm_set_arm_delete_trajectory()
        try:
            delete_old_subdirectories(1)
        except Exception as e:
            print(f'Failed old subdirectories error: {e}')
        return [0, {'error_message': self._error_message}]

    def emergency_stop(self):
        """
        Trigger emergency stop for all operations.
        
        Immediately stops all movement and disables control flags to prevent further actions.
        """

        self._cm.arm.rm_set_timeout(10)  # 10ms = 0.1s
        self._emergency_stop = True
        self._calibrate_control_flag = False
        self.go_pole_control_flag = False
        print("EMERGENCY STOP TRIGGERED")

    def reset_emergency_stop(self):
        """
        Reset emergency stop flag to allow operations to resume.
        
        Should be called after ensuring it's safe to resume operations.
        """
        self._cm.arm.rm_set_timeout(2000)  # 2000ms = 20s
        self._emergency_stop = False
        print("Emergency stop reset")

    def pause_frame_acquisition(self):
        """
        Pause frame acquisition to save system resources.
        
        Sets a flag that prevents new frames from being processed.
        """
        self._frame_acquisition_paused = True

    def resume_frame_acquisition(self):
        """
        Resume frame acquisition when needed for operations.
        
        Should be called before operations that require camera input.
        """
        self._frame_acquisition_paused = False

    def pause_detection(self):
        """
        Pause detection processing to save computational resources.
        
        Sets a flag that prevents detector from processing frames.
        """
        self._detection_paused = True
        self._detector_activate_flag = False
        self._calibrate_detect_flag = False

    def resume_detection(self):
        """
        Resume detection processing when needed for operations.
        
        Should be called before operations that require object detection.
        """
        self._detection_paused = False

    def set_pole_length(self, length):
        """
        Set the pole length for calculations.
        
        Args:
            length: Pole length in mm (will add 160mm offset internally)
        """
        if length is None:
            print("Pole length is None, using default length")
            return
        self.len_pole = length + self.pole_length_offset  # Add constant offset for end effector

    def set_photo_direct_mode(self, photo_direct_mode):
        """
        Configure photo mode behavior.
        
        Args:
            photo_direct_mode: If True, stops after first detector is found
                               If False, captures photos at all positions
        """
        self.photo_direct_mode = photo_direct_mode

    def set_wait_time(self, wait_time):
        """
        Set wait time after arriving at target smoke detector.
        
        Args:
            wait_time: Wait time in seconds for smoke testing
        """
        if wait_time is None:
            print("Wait time is None, using default wait time")
            return
        self._wait_time = wait_time

    def set_unreachable_action(self, unreachable_action):
        """
        Set action mode when smoke detector is unreachable.
        
        Args:
            unreachable_action: Action mode ('stop' to abort or 'continue' to complete anyway)
        """
        if unreachable_action is None:
            print("Unreachable action is None, using default unreachable action")
            return
        self.unreachable_action = unreachable_action

    def set_default_position_idx(self, idx):
        """
        Set default position idx of memorized detected smoke detector which is unreachable.

        Args:
            idx: integer from 0 to 9
        """
        if idx is None:
            print("Unreachable action is None, using default unreachable action")
            return
        self.default_position_idx = idx

    def reload_config(self, config_path="config/pole_params.yaml"):
        """
        Reload configuration parameters from the YAML file.
        
        This allows updating parameters without restarting the application.
        
        Args:
            config_path: Path to the configuration file
            
        Returns:
            Dictionary of loaded configuration parameters
        """
        try:
            config = load_config(config_path)
        except Exception as e:
            print(f"Error loading configuration: {e}, using default configuration")
            # config = {}
            return
        
        # Update parameters from config
        self._record_len = config.get('record_len', self._record_len)
        
        calib_coords = config.get('calib_desired_coord', [self._calib_desired_coord[0], self._calib_desired_coord[1]])
        self._calib_desired_coord = np.array(calib_coords)
        
        self._deadband_error = config.get('deadband_error', self._deadband_error)
        
        # Calculate pole length with updated offset
        self.pole_length_offset = config.get('pole_length_offset', 160)
        base_len = self.len_pole - 160  # Remove default offset
        self.len_pole = base_len + self.pole_length_offset
        
        self.ep_max_len = config.get('ep_max_len', self.ep_max_len)
        self.calib_rotate_bias = config.get('calib_rotate_bias', self.calib_rotate_bias)
        
        self.min_detector_x = config.get('min_detector_x', self.min_detector_x)
        self.min_detector_y = config.get('min_detector_y', self.min_detector_y)
        self.min_detector_y_single = config.get('min_detector_y_single', self.min_detector_y_single)
        
        self._wait_time = config.get('wait_time', self._wait_time)
        
        # Update gravity offset
        self._gravity_offset_l = config.get('gravity_offset_l', self._gravity_offset_l)
        self._gravity_offset_x, self._gravity_offset_y = -math.cos(
            self.prepared_rad) * self._gravity_offset_l + 0.002, -math.sin(self.prepared_rad) * self._gravity_offset_l
            
        # Update raising heights
        prepared_height = self.raising_heights[0]
        raising_height_grab = config.get('raising_height_grab', 0.055)
        raising_height_intermediate = config.get('raising_height_intermediate', 0.100)
        raising_height_outside = config.get('raising_height_outside', 0.160)
        raising_height_above = config.get('raising_height_above', 0.235)
        
        self.raising_heights = [
            prepared_height,
            prepared_height + raising_height_grab,
            prepared_height + raising_height_intermediate,
            prepared_height + raising_height_outside,
            prepared_height + raising_height_above,
        ]
        
        # Update joint positions
        prepare_joints_config = config.get('prepare_joints', {})
        j6 = self.prepare_joints[-1][-1]  # Keep the last joint value in case it's not in config
        
        if prepare_joints_config:
            self.prepare_joints = [
                prepare_joints_config.get('zero', self.prepare_joints[0]),
                prepare_joints_config.get('mid1', self.prepare_joints[1]),
                prepare_joints_config.get('mid2', self.prepare_joints[2]),
                prepare_joints_config.get('prepared', self.prepare_joints[3])
            ]
            
            # Recalculate calibration position if prepare_joints changed
            t_prepared_last = self._cm.rob.fkine(np.radians([self.prepare_joints[-1]])).A
            self._calib_x, self._calib_y, prepared_height = t_prepared_last[:3, 3].tolist()[:3]
            self.prepared_rad = R.from_matrix(t_prepared_last[:3, :3].tolist()).as_euler('xyz', degrees=False)[-1]
            
            # Update raising heights with new prepared_height
            self.raising_heights = [
                prepared_height,
                prepared_height + raising_height_grab,
                prepared_height + raising_height_intermediate,
                prepared_height + raising_height_outside,
                prepared_height + raising_height_above,
            ]
            
            # Recalculate gravity offsets with new prepared_rad
            self._gravity_offset_x, self._gravity_offset_y = -math.cos(
                self.prepared_rad) * self._gravity_offset_l, -math.sin(self.prepared_rad) * self._gravity_offset_l
        
        # Update safe_700_joint
        self.safe_700_joint = config.get('safe_700_joint', self.safe_700_joint)
        self.safe_700_joint[-1] = j6
        
        # Update joint restrictions
        max_restrictions = config.get('max_joint_restrictions')
        if max_restrictions:
            self.max_joint_restrictions = np.array(max_restrictions)
            
        min_restrictions = config.get('min_joint_restrictions')
        if min_restrictions:
            self.min_joint_restrictions = np.array(min_restrictions)
        
        # Update start positions
        start_positions_config = config.get('start_positions', {})
        j6 = self.prepare_joints[-1][-1]  # Use the last joint value from prepare_joints
        
        if start_positions_config:
            self.start_positions = [
                start_positions_config.get('pos0', self.start_positions[0]),
                start_positions_config.get('pos1', self.start_positions[1]),
                start_positions_config.get('pos2', self.start_positions[2]),
                start_positions_config.get('pos3', self.start_positions[3]),
                start_positions_config.get('pos4', self.start_positions[4]),
                start_positions_config.get('pos5', self.start_positions[5]),
                start_positions_config.get('pos6', self.start_positions[6]),
                start_positions_config.get('pos7', self.start_positions[7]),
                start_positions_config.get('pos8', self.start_positions[8]),
                start_positions_config.get('pos9', self.start_positions[9])
            ]
            
            for sp in self.start_positions:
                sp[-1] = j6

            self.p0_joint = self.start_positions[0]

        default_position_config = config.get('default_observe_idx', 0)
        self.default_position_idx = default_position_config
        # Update vertical joint
        self.vertical_joint = config.get('vertical_joint', self.vertical_joint)
        
        # Update unreachable action
        self.unreachable_action = config.get('unreachable_action', self.unreachable_action)
        
        print(f"Configuration reloaded from {config_path}")
        return config

    def run_cmd(self):
        """
        Process commands from the command queue.
        
        This method runs in a separate thread and executes commands based on the 
        self._cmd value. Each command corresponds to a specific robot operation:
        
        0: Move to zero pose
        1: Move from zero to prepared pose
        2: Perform calibration at prepared pose
        3: Grab the pole
        4: Raise to work pose
        5: Return to grab pose
        6: Return to prepared pose
        7: Return to zero pose
        9: Execute complete work procedure
        'u': Take photos
        'r': Release gripper
        't': Tighten gripper
        'smoke': Execute smoke pole procedure
        'find': Find suitable start position
        'home': Return to home position
        """
        while True:
            time.sleep(0.1)
            # Skip if emergency stop is active
            if self._emergency_stop:
                continue

            # Command 0: Move to zero pose
            if self._cmd == 0:
                print('cmd:', self._cmd)
                self._cmd = None
                ret = self.move_to_zero_pose()
                print('move to zero pose finished!', ret, self._error_message)
                
            # Command 1: Move from zero to prepared pose
            elif self._cmd == 1:
                print('cmd:', self._cmd)
                self._cmd = None
                ret = self.move_zero_to_prepared_pose()
                print('move zero to prepared pose finished!', ret, self._error_message)
                
            # Command 2: Perform calibration at prepared pose
            elif self._cmd == 2:
                print('cmd:', self._cmd)
                self._cmd = None
                self._calibrate_detect_flag = True
                self._calibrate_control_flag = True
                # First calibration attempt
                ret = self.calibrate_at_prepared_pose()
                print('calibrate-1 at prepared pose finished!', ret, self._error_message)
                # time.sleep(0.5)
                # Second calibration attempt for refinement
                self._calibrate_detect_flag = False
                self._calibrate_control_flag = False
                # ret = self.calibrate_at_prepared_pose()
                # print('calibrate-2 at prepared pose finished!', ret, self._error_message)
                
            # Command 3: Grab the pole
            elif self._cmd == 3:
                print('cmd:', self._cmd)
                self._cmd = None
                ret = self.go_grab()
                print('go grab finished!', ret, self._error_message)
                
            # Command 4: Raise to work pose
            elif self._cmd == 4:
                print('cmd:', self._cmd)
                self._cmd = None
                ret = self.raise_to_work_pose()
                print('raise to work pose finished!', ret, self._error_message)
                
            # Command 5: Return to grab pose
            elif self._cmd == 5:
                print('cmd:', self._cmd)
                self._cmd = None
                ret = self.back_to_grab_pose(block=True)
                print('back to grab pose finished!', ret, self._error_message)
                
            # Command 6: Return to prepared pose
            elif self._cmd == 6:
                print('cmd:', self._cmd)
                self._cmd = None
                ret = self.back_to_prepared_pose()
                print('back to prepared pose finished!', ret, self._error_message)
                
            # Command 7: Return to zero pose
            elif self._cmd == 7:
                print('cmd:', self._cmd)
                self._cmd = None
                ret = self.back_to_zero_pose()
                print('back to zero pose finished!', ret, self._error_message)
                
            # Command 8: Reserved/unused
            elif self._cmd == 8:
                print('cmd:', self._cmd)
                self._cmd = None
                
            # Command 9: Execute complete work procedure
            elif self._cmd == 9:
                print('cmd:', self._cmd)
                self._cmd = None
                ret = self.work_procedure()
                print('work procedure finished!', ret, self._error_message)
                
            # Command 'u': Take photos
            elif self._cmd == 'u':
                print('cmd:', self._cmd)
                self._cmd = None
                ret = self.take_photos()
                print('take photos finished!', ret, self._error_message)
                
            # Command 'r': Release gripper
            elif self._cmd == 'r':
                print('cmd:', self._cmd)
                self._cmd = None
                print(self._cm.arm.rm_set_gripper_release(150, True, 10))
                
            # Command 't': Tighten gripper
            elif self._cmd == 't':
                print('cmd:', self._cmd)
                self._cmd = None
                self._cm.arm.rm_set_gripper_pick_on(500, 800, True, 10)
                
            # Command 'smoke': Execute smoke pole procedure
            elif self._cmd == 'smoke':
                print('cmd:', self._cmd)
                self._cmd = None
                self.go_pole_control_flag = True
                ret = self.go_smoke_pole()
                print('smokepole finished!', ret, self._error_message)
                
            # Command 'find': Find suitable start position
            elif self._cmd == 'find':
                print('cmd:', self._cmd)
                self._cmd = None
                print('find suitable start position start')
                ret = self.find_suitable_start_position()
                print('find suitable start position done', ret, self._error_message)
                
            # Command 'home': Return to home position
            elif self._cmd == 'home':
                print('cmd:', self._cmd)
                self._cmd = None
                ret = self.safe_back_to_p0()
                print('home finished!', ret, self._error_message)

    def to_p0(self, block=True, joints=None):
        """
        Move the robot arm to the p0 (home) position safely.
        
        Determines the best path to move to p0 based on current joint positions.
        
        Returns:
            0 on success, error code on failure
        """
        # Get current arm state
        block_flag = block or (joints is not None and len(joints) == 0)
        if block_flag:
            current_state = self._cm.arm.rm_get_current_arm_state()
            cur_err_count = 0
            while current_state[0] != 0:
                if cur_err_count > 20:
                    self._error_message = f'[Failed to get arm state after multiple attempts: {current_state}]'
                    return ARM_STATE_ERROR
                cur_err_count += 1
                print('Error getting current arm state')
                time.sleep(0.8)
                current_state = self._cm.arm.rm_get_current_arm_state()
            current_joint = current_state[1]['joint']
        else:
            current_joint = joints[-1]
            pass
            # Check if we need to move (if current position is close enough to p0, do nothing)
        if np.sum(abs(np.array(current_joint) - np.array(self.p0_joint))) > 5:
            # Choose movement strategy based on current position
            if np.sum(abs(np.array(current_joint[:-1]) - np.array(self.p0_joint[:-1]))) < 5:
                print('move from current(close except j6) to p0')
                # Close enough in first 5 joints - direct move
                ret = self.move_arm('rm_movej', (self.p0_joint, 18, 50, 0, 1))
                # ret = self.safer_move(self.p0_joint, block=block, joints=joints)
                if ret != 0:
                    self._error_message += f'<{self.safer_move.__name__}>'
                    return ret
            else:
                # Far from p0 - move through intermediate position
                print('move from safe700 to p0')
                mid_vertical_joint = self.safe_700_joint.copy()
                mid_vertical_joint[0] = current_joint[0]  # Keep current rotation of Joint1
                ret = self.safer_move(self.p0_joint, mid_vertical_joint, block=block, joints=joints, speed=0.8)
                if ret != 0:
                    self._error_message += f'<{self.safer_move.__name__}>'
                    return ret
        else:
            print(f'no need to move from current{current_joint} to p0{self.p0_joint}')
        return 0

    @staticmethod
    def reorganize_positions(positions, idx):
        # Ensure idx is within the valid range
        if idx < 0 or idx >= len(positions):
            raise ValueError("idx is out of range")
        if idx == 0:
            return positions
        # Reorganize the list
        return [positions[0]] + [positions[idx]] + [pos for i, pos in enumerate(positions) if i not in (0, idx)]
    def take_photos(self):
        """
        Take photos at different robot positions to find smoke detectors.
        
        Moves through a series of predefined positions, takes photos at each position,
        and attempts to detect smoke detectors in the images.
        
        Returns:
            List containing [result_code, result_dict] where result_dict contains:
            - result: Whether a detector was found
            - path: Directory where images were saved
            - error_message: Error message if any
        """
        # Disable calibration detection during photo capture
        self._calibrate_detect_flag = False
        self._cm.arm.rm_get_current_arm_state()
        # Create directory for saving images
        self.save_count += 1
        save_dir = f'data/shot-images-saves/{datetime.datetime.now().strftime("%m%d%H%M")}_{self.save_count:03d}'
        while os.path.exists(save_dir):
            self.save_count += 1
            save_dir = f'data/shot-images-saves/{datetime.datetime.now().strftime("%m%d%H%M")}_{self.save_count:03d}'
        os.makedirs(save_dir)
        
        # Initialize variables
        positions = self.start_positions
        find_detector = False
        re_organized_positions = self.reorganize_positions(positions, self.default_position_idx)
        # Move to first position

        ret = self.safer_move(self.p0_joint, speed=1.0)
        if ret != 0:
            self._error_message += f'<{self.safer_move.__name__}>'
            # ret_reset = self.reset()
            # print(f'Error During photo command, ret_reset:{ret_reset}')
            return [ret, {"result": find_detector, "path": save_dir, "error_message": self._error_message}]
        time.sleep(0.5)

        trajectory_count = 0
        trajectory_break_count = 0
        while True:
            if self._emergency_stop:
                print('[[EMERGENCY_STOP--during trajectory check]]')
                self._error_message = f'[EMERGENCY_STOP--during trajectory check]'
                return [EMERGENCY_STOP_ERROR, {"result": find_detector, "path": save_dir, "error_message": self._error_message}]
            if trajectory_count > 240:
                self._error_message = "Timeout error in checking arm state"
                return [ARM_TRAJECTORY_TIMEOUT_ERROR, {"result": find_detector, "path": save_dir, "error_message": self._error_message}]
            trajectory_count += 1
            current_trajectory_state = self._cm.arm.rm_get_arm_current_trajectory()
            cur_err_count = 0
            while current_trajectory_state['return_code'] != 0:
                if cur_err_count > 160:
                    self._error_message = f'[Failed to get arm state after multiple attempts: {current_trajectory_state}]'
                    return [ARM_STATE_ERROR, {"result": find_detector, "path": save_dir, "error_message": self._error_message}]
                cur_err_count += 1
                if cur_err_count > 80:
                    print(f'- [{cur_err_count}]Error in get current state: {current_trajectory_state}')
                time.sleep(0.2)
                current_trajectory_state = self._cm.arm.rm_get_arm_current_trajectory()
            if current_trajectory_state['trajectory_type'] == 0:
                trajectory_break_count += 1
                if trajectory_break_count > 3:
                    break
                time.sleep(0.1)
                continue
            else:
                trajectory_break_count = 0
            time.sleep(0.15)
        self._cm.arm.rm_get_current_arm_state()
        
        # Move through all positions and take photos
        for position_idx, position_joint in enumerate(re_organized_positions):
            # Move to position
            speed = 20 if position_idx == 0 else 23
            if position_idx != 0:
                delta_joint_angle = abs(position_joint[-1] - re_organized_positions[position_idx - 1][-1])
                if delta_joint_angle > 35:
                    speed = min(70, int(speed * math.pow(delta_joint_angle / 35, 0.8)))
                    pass
            ret = self.move_arm('rm_movej', (position_joint, speed, 50, 0, 1))
            # ret = self.move_arm('rm_movej', (position_joint, 15, 50, 0, 1))
            if ret != 0:
                # ret_reset = self.reset()
                # print(f'Error During photo command, ret_reset:{ret_reset}')
                return [ret, {"result": find_detector, "path": save_dir, "error_message": self._error_message}]
            time.sleep(0.2)

            trajectory_count = 0
            trajectory_break_count = 0
            while True:
                if self._emergency_stop:
                    print('[[EMERGENCY_STOP--during trajectory check]]')
                    self._error_message = f'[EMERGENCY_STOP--during trajectory check]'
                    return [EMERGENCY_STOP_ERROR, {"result": find_detector, "path": save_dir, "error_message": self._error_message}]
                if trajectory_count > 240:
                    self._error_message = "Timeout error in checking arm state"
                    return [ARM_TRAJECTORY_TIMEOUT_ERROR, {"result": find_detector, "path": save_dir, "error_message": self._error_message}]
                trajectory_count += 1
                current_trajectory_state = self._cm.arm.rm_get_arm_current_trajectory()
                cur_err_count = 0
                while current_trajectory_state['return_code'] != 0:
                    if cur_err_count > 160:
                        self._error_message = f'[Failed to get arm state after multiple attempts: {current_trajectory_state}]'
                        return [ARM_STATE_ERROR, {"result": find_detector, "path": save_dir, "error_message": self._error_message}]
                    cur_err_count += 1
                    if cur_err_count > 80:
                        print(f'- [{cur_err_count}]Error in get current state: {current_trajectory_state}')
                    time.sleep(0.2)
                    current_trajectory_state = self._cm.arm.rm_get_arm_current_trajectory()
                if current_trajectory_state['trajectory_type'] == 0:
                    trajectory_break_count += 1
                    if trajectory_break_count > 3:
                        break
                    time.sleep(0.1)
                    continue
                else:
                    trajectory_break_count = 0
                time.sleep(0.15)
            self._cm.arm.rm_get_current_arm_state()
            
            # Get camera image
            ci = self.ci.copy() if self.ci is not None else None
            if ci is None:
                # ret_reset = self.reset()
                # print(f'Error During photo command, ret_reset:{ret_reset}')
                return [CAMERA_ERROR, {"result": find_detector, "path": save_dir, "error_message": "Camera frame error"}]
                
            # Save image
            ret = cv2.imwrite(os.path.join(save_dir, f'{position_idx:03d}.jpg'), ci)
            if not ret:
                # ret_reset = self.reset()
                # print(f'Error During photo command, ret_reset:{ret_reset}')
                return [MEMORY_ERROR, {"result": find_detector, "path": save_dir, "error_message": "Cannot write image"}]
                
            # Try to detect smoke detector in image
            detector_coord, _ = self._detector_model.detect(ci, detect_count=position_idx)
            if detector_coord is not None:
                # Detector found - mark it in the image
                find_detector = True
                display_img = ci.copy()
                cv2.circle(display_img, (int(detector_coord[0]), int(detector_coord[1])), int(detector_coord[2]),
                           (255, 30, 0), 2)
                cv2.putText(display_img, f'{int(detector_coord[-1] * 100)}',
                            (int(detector_coord[0] - detector_coord[2]),
                             int(detector_coord[1] - detector_coord[2])),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.5,
                            (255, 30, 0), 1, cv2.LINE_AA)
                print(f'[P{position_idx}]Find detector at {detector_coord}')
                # Save annotated image
                ret = cv2.imwrite(os.path.join(save_dir, f'{position_idx:03d}_detection.jpg'), display_img)
                if not ret:
                    # ret_reset = self.reset()
                    # print(f'Error During photo command, ret_reset:{ret_reset}')
                    return [MEMORY_ERROR, {"result": find_detector, "path": save_dir, "error_message": "Cannot write image"}]
                    
                # If in direct mode, stop after first detection
                if self.photo_direct_mode:
                    break
                    
        # Reset robot position
        ret_reset = self.reset()
        if ret_reset[0] != 0:
            return [ret_reset[0], {"result": find_detector, "path": save_dir, "error_message": self._error_message}]
            
        return [SUCCESS_CODE, {"result": find_detector, "path": save_dir, "error_message": ""}]

    def find_suitable_start_position(self):
        """
        Find a suitable starting position for the smoke pole operation.
        
        This method performs three main tasks:
        1. Try different positions to find one where the detector is visible
        2. Calculate the angle to the detector
        3. Find the best position to approach the detector
        
        Returns:
            Tuple of (error_code, success_flag)
        """
        # Task 1: Try different positions to find detector
        positions = self.start_positions

        re_organized_positions = self.reorganize_positions(positions, self.default_position_idx)

        current_idx, current_depth = -1, 1000
        detector_position_res = None
        depth = 0
        space_distance = None
        last_space_distance = None
        save_dir = f'data/shot-images-saves/{datetime.datetime.now().strftime("%m%d%H%M")}_{self.save_count:03d}'
        while os.path.exists(save_dir):
            self.save_count += 1
            save_dir = f'data/shot-images-saves/{datetime.datetime.now().strftime("%m%d%H%M")}_{self.save_count:03d}'
        os.makedirs(save_dir)

        for position_idx, position_joint in enumerate(re_organized_positions):
            # Move to position
            speed = 20 if position_idx == 0 else 23
            if position_idx != 0:
                delta_joint_angle = abs(position_joint[-1] - re_organized_positions[position_idx - 1][-1])
                # if delta_joint_angle > 35:
                speed = max(15, min(70, int(speed * math.pow(delta_joint_angle / 35, 0.8))))
                pass
            ret = self.move_arm('rm_movej', (position_joint, speed, 50, 0, 1))
            if ret != 0:
                self._error_message += f"<Failed to move to position {position_idx}"
                return ret, False

            trajectory_count = 0
            trajectory_break_count = 0
            while True:
                if self._emergency_stop:
                    print('[[EMERGENCY_STOP--during trajectory check]]')
                    self._error_message = f'[EMERGENCY_STOP--during trajectory check]'
                    return EMERGENCY_STOP_ERROR, False
                if trajectory_count > 240:
                    self._error_message = "Timeout error in checking arm state"
                    return ARM_TRAJECTORY_TIMEOUT_ERROR, False
                trajectory_count += 1
                current_trajectory_state = self._cm.arm.rm_get_arm_current_trajectory()
                cur_err_count = 0
                while current_trajectory_state['return_code'] != 0:
                    if cur_err_count > 160:
                        self._error_message = f'[Failed to get arm state after multiple attempts: {current_trajectory_state}]'
                        return ARM_STATE_ERROR, False
                    cur_err_count += 1
                    if cur_err_count > 80:
                        print(f'- [{cur_err_count}]Error in get current state: {current_trajectory_state}')
                    time.sleep(0.2)
                    current_trajectory_state = self._cm.arm.rm_get_arm_current_trajectory()
                if current_trajectory_state['trajectory_type'] == 0:
                    trajectory_break_count += 1
                    if trajectory_break_count > 3:
                        break
                    time.sleep(0.1)
                    continue
                else:
                    trajectory_break_count = 0
                time.sleep(0.15)
            self._cm.arm.rm_get_current_arm_state()
            # time.sleep(0.2)
            
            # Try to detect smoke detector
            detector_position_res_tmp, _ = self.detector_detection(5, detect_pole_flag=False)
            ret = cv2.imwrite(os.path.join(save_dir, f'{position_idx:03d}.jpg'), self.ci)
            if not ret:
                return MEMORY_ERROR, True
            if detector_position_res_tmp is None:
                continue
            detector_coord = detector_position_res_tmp
            # Get depth information for the detector
            depth = self.di[int(detector_position_res_tmp[1]), int(detector_position_res_tmp[0])]
            if depth <= 0:
                # Try to get depth from surrounding area if direct depth is invalid
                depth_neighbour, depth_neighbour_flag = self.get_area_depth(
                    self.di, detector_position_res_tmp[0], detector_position_res_tmp[1])
                if depth_neighbour_flag and depth_neighbour > 100:
                    depth = depth_neighbour
            # self.detector_coord = detector_coord
            # Convert detector position from camera coordinates to robot base coordinates
            point_3d1 = rs.rs2_deproject_pixel_to_point(
                self._cm.depth_intrin,
                (float(detector_coord[0]), float(detector_coord[1])),
                depth / 1000.0  # Convert mm to meters
            )
            end_pose1 = self.joint_to_pose(position_joint)
            t_obj_to_base = convert(point_3d1, end_pose1)
            self.detector_coord = [t_obj_to_base[0], t_obj_to_base[1]]
            print(f'now the detector_coord={detector_coord}, p3d={point_3d1}, end_pose1={end_pose1}, self.detector_coord={self.detector_coord}')
            display_img = self.ci.copy()
            cv2.circle(display_img, (int(detector_coord[0]), int(detector_coord[1])), int(detector_coord[2]),
                       (255, 30, 0), 2)

            # Save annotated image
            ret = cv2.imwrite(os.path.join(save_dir, f'{position_idx:03d}_detection.jpg'), display_img)
            if not ret:
                return MEMORY_ERROR, True
            space_distance = np.linalg.norm((self.detector_coord[0] * 1000, self.detector_coord[1] * 1000, depth - self.len_pole))
            cv2.putText(display_img, f'{int(detector_coord[-1] * 100)}[{int(space_distance)}]P{position_idx}]',
                        (int(detector_coord[0]),
                         int(detector_coord[1])),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.3,
                        (255, 30, 0), 1, cv2.LINE_AA)
            print(f'[P{position_idx}]find detector at {self.detector_coord} with depth={depth} and space distance={space_distance:.2f}')
            # If detector found with valid depth, save position index
            if detector_position_res_tmp is not None and depth > 100 and (last_space_distance is None or space_distance < last_space_distance):
                last_space_distance = space_distance
                current_idx = position_idx
                detector_position_res = detector_position_res_tmp
                current_depth = depth
                self.detection_path = os.path.join(save_dir, f'{position_idx:03d}_detection.jpg')
                self.detection_result = True
                if space_distance > self.max_space_distance:  # and self.unreachable_action != 'continue':
                    continue
                break
                
        # If no valid position found, return error
        if current_idx == -1:
            self._error_message = "Could not detect smoke detector in any position"
            return NO_DETECTOR_ERROR, False
        if space_distance > self.max_space_distance:
            if self.unreachable_action == 'stop':
                return POLE_UNREACHABLE_ERROR, False
            print(f'[WARNING] Pole is too far {space_distance:.2f}[{np.int_(1000 * self.detector_coord)}], but try to get closer anyway')
        else:
            self.default_position_idx = current_idx
        connect_speed = 23
        if current_idx != len(positions) - 1:
            delta_joint_angle = abs(positions[current_idx][-1] - positions[-1][-1])
            connect_speed = max(15, min(70, int(connect_speed * math.pow(delta_joint_angle / 35, 0.8))))
            # if delta_joint_angle > 35:
            #     connect_speed = min(70, int(connect_speed * math.pow(delta_joint_angle / 35, 0.8)))
            #     pass
        # ret = self.move_arm('rm_movej', (positions[current_idx], speed, 50, 0, 1))
        # # ret = self.move_arm('rm_movej', (positions[current_idx], 10, 50, 0, 1))
        # if ret != 0:
        #     self._error_message += f"<Failed to move to position {positions[current_idx]}"
        #     return ret, False
        # time.sleep(0.2)

        # Create optimized position list starting from current position
        positions_concat = [position for position in positions[current_idx:]]
        positions_concat.extend([position for position in positions[:current_idx][::-1]])
        print(f'Found suitable position at position {current_idx}， save at {save_dir}')
        # Task 2: Calculate angle to detector
        # current_state = self._cm.arm.rm_get_current_arm_state()
        # cur_err_count = 0
        # while current_state[0] != 0:
        #     if cur_err_count > 20:
        #         self._error_message = f'[Failed to get arm state after multiple attempts: {current_state}]'
        #         return ARM_STATE_ERROR, False
        #     cur_err_count += 1
        #     print('Error getting current arm state')
        #     time.sleep(0.8)
        #     current_state = self._cm.arm.rm_get_current_arm_state()

        # Convert detector position from camera coordinates to robot base coordinates
        point_3d1 = rs.rs2_deproject_pixel_to_point(
            self._cm.depth_intrin,
            (float(detector_position_res[0]), float(detector_position_res[1])),
            current_depth / 1000.0  # Convert mm to meters
        )
        # end_pose1 = current_state[1]['pose']
        end_pose1 = self.joint_to_pose(positions[current_idx])
        t_obj_to_base = convert(point_3d1, end_pose1)
        self.detector_coord = [t_obj_to_base[0], t_obj_to_base[1]]
        # Calculate angle to detector
        z_angle0_rad = math.atan2((t_obj_to_base[1]), (t_obj_to_base[0]))
        _detector_angle = math.degrees(z_angle0_rad) + 90
        # Normalize angle to [-180, 180] range
        _detector_angle = _detector_angle - 360 if _detector_angle >= 180 else _detector_angle + 360 if _detector_angle <= -180 else _detector_angle
        self._detector_angle = _detector_angle
        z_angle0_rad = math.radians(_detector_angle)
        print(f'now the detector_position_res={detector_position_res}, p3d={point_3d1}, end_pose1={end_pose1}, self.detector_coord={self.detector_coord}')
        print(f'Detector at position {self.detector_coord}, rotation_angle={_detector_angle}°')

        # Task 3: Find best position to approach detector
        detector_position_res = None
        for position_idx, position_joint in enumerate(positions_concat):
            # Calculate end effector pose from joint angles
            position_pose = self._cm.rob.fkine(np.radians(position_joint)).A[:3, 3].tolist()
            position_pose.extend([0, 0, z_angle0_rad])
            
            # Calculate inverse kinematics
            q_out = self._cm.algo_handle.rm_algo_inverse_kinematics(
                rm_inverse_kinematics_params_t(position_joint, position_pose, 1))
                
            if q_out[0] != 0:
                print(f'Inverse kinematics error: {q_out[0]}')
                continue
                
            # Normalize joint 6 rotation
            rotation_joint6 = q_out[1][5]
            rotation_joint6 = rotation_joint6 - 360 if rotation_joint6 >= 200 else rotation_joint6 + 360 if rotation_joint6 <= -200 else rotation_joint6

            # Create rotated joint configuration
            rotated_position_joint = [pj for pj in position_joint[:-1]]
            rotated_position_joint.append(rotation_joint6)
            
            # Move to position and check for detector

            speed = 23 if position_idx != 0 else connect_speed
            if position_idx != 0:
                delta_joint_angle = abs(position_joint[-1] - positions[position_idx - 1][-1])
                speed = max(15, min(60, int(speed * math.pow(delta_joint_angle / 35, 0.8))))
                # if delta_joint_angle > 35:
                #     speed = min(60, int(speed * math.pow(delta_joint_angle / 35, 0.8)))
                #     pass
            ret = self.move_arm('rm_movej', (rotated_position_joint, speed, 50, 0, 1))

            # ret = self.move_arm('rm_movej', (rotated_position_joint, 30, 50, 0, 1))
            if ret != 0:
                self._error_message += f"<Failed to move to position {position_idx}"
                return ret, False
            trajectory_count = 0
            trajectory_break_count = 0
            while True:
                if self._emergency_stop:
                    print('[[EMERGENCY_STOP--during trajectory check]]')
                    self._error_message = f'[EMERGENCY_STOP--during trajectory check]'
                    return EMERGENCY_STOP_ERROR, False
                if trajectory_count > 240:
                    self._error_message = "Timeout error in checking arm state"
                    return ARM_TRAJECTORY_TIMEOUT_ERROR, False
                trajectory_count += 1
                current_trajectory_state = self._cm.arm.rm_get_arm_current_trajectory()
                cur_err_count = 0
                while current_trajectory_state['return_code'] != 0:
                    if cur_err_count > 160:
                        self._error_message = f'[Failed to get arm state after multiple attempts: {current_trajectory_state}]'
                        return ARM_STATE_ERROR, False
                    cur_err_count += 1
                    if cur_err_count > 80:
                        print(f'- [{cur_err_count}]Error in get current state: {current_trajectory_state}')
                    time.sleep(0.2)
                    current_trajectory_state = self._cm.arm.rm_get_arm_current_trajectory()
                if current_trajectory_state['trajectory_type'] == 0:
                    trajectory_break_count += 1
                    if trajectory_break_count > 3:
                        break
                    time.sleep(0.1)
                    continue
                else:
                    trajectory_break_count = 0
                time.sleep(0.15)
            self._cm.arm.rm_get_current_arm_state()
            # time.sleep(0.2)
            detector_position_res, pole_position_res = self.detector_detection(5)
            if detector_position_res is not None:
                print(f'Find Detector at position at P[{position_idx}]')
            # Check if detector is visible and not blocked by pole
            if detector_position_res is not None and self.di[int(detector_position_res[1]), int(detector_position_res[0])] > 30:
                if pole_position_res is None:
                    print(f'But no pole_position found... at P[{position_idx}]')
                    continue
                print(f'Find Detector and Pole at position at P[{position_idx}]')
                    
                # Check if pole is not blocking detector
                dx, dy = detector_position_res[:2]
                px, py, pr = pole_position_res[:3]
                px1, py1 = px - pr, py - pr
                
                # Skip if pole is in front of detector
                if ((px1 <= dx <= px1 + max(2 * pr, self.min_detector_x) and
                    py1 <= dy <= py1 + max(2 * pr, self.min_detector_y)) or
                    dy >= self.min_detector_y_single):
                    continue
                current_idx = position_idx
                break
        if detector_position_res is not None:
            print(f'Find suitable position {current_idx} to approach detector')
        # Return success if detector was found
        return 0, detector_position_res is not None

    def get_area_depth(self, di, dx, dy):
        """
        Get average depth from an area around a point.
        
        Used when direct depth measurement at a point is invalid.
        
        Args:
            di: Depth image
            dx: X coordinate of the point
            dy: Y coordinate of the point
            
        Returns:
            Tuple of (average_depth, success_flag)
        """
        neighbour_flag = True
        
        # Get depth values from region above and to the left of the point
        di_roi = di[max(0, dy - 150):dy, max(0, dx - 150):dx]
        di_nums = di_roi[di_roi > 100]  # Filter out invalid depths
        
        # If not enough valid depth values, try larger area
        if len(di_nums) < 1000:
            neighbour_flag = False
            di_roi = di[max(0, dy - 250):dy, max(0, dx - 250):dx]
            di_nums = di_roi[di_roi > 100]
            
            # Return failure if still not enough valid depths
            if len(di_nums) < 1000:
                return None, False
                
            neighbour_flag = True
            
        # Calculate average depth from middle range (30-70 percentile)
        # to avoid outliers
        di_num_sorted = sorted(di_nums)
        di_num_mid = di_num_sorted[int(0.3 * len(di_nums)):int(0.7 * len(di_nums))]
        
        return np.mean(di_num_mid), neighbour_flag

    def safer_move(self, target_joint, init_joint=None, block=True, joints=None, speed=1.0):
        """
        Move the robot arm safely to a target joint configuration.
        
        Ensures safe movement by potentially using an intermediate position
        and moving joints in groups to avoid collisions.
        
        Args:
            target_joint: Target joint configuration
            init_joint: Optional intermediate joint configuration
            
        Returns:
            0 on success, error code on failure
        """
        # Determine initial joint position
        block_flag = block or (joints is not None and len(joints) == 0)
        current_joint = None
        if init_joint is None:
            if block_flag:
                # Get current arm state
                current_state = self._cm.arm.rm_get_current_arm_state()
                current_state = self._cm.arm.rm_get_current_arm_state()
                cur_err_count = 0
                while current_state[0] != 0:
                    if cur_err_count > 20:
                        self._error_message = f'[Failed to get arm state after multiple attempts: {current_state}]'
                        return ARM_STATE_ERROR
                    cur_err_count += 1
                    print('Error getting current arm state')
                    time.sleep(0.8)
                    current_state = self._cm.arm.rm_get_current_arm_state()

                current_joint = current_state[1]['joint']
            else:
                current_joint = joints[-1].copy()

            # Decide whether to use vertical intermediate position
            if abs(np.sum(np.array(current_joint[:]) - np.array(target_joint[:]))) > 5:
                # Large difference - use vertical intermediate position
                init_joint = self.vertical_joint.copy()
                init_joint[0] = current_joint[0]  # Keep current rotation
            else:
                # Small difference - use current position directly
                init_joint = current_joint.copy()

        # Move to initial position
        if block_flag:
            ret = self.move_arm('rm_movej', (init_joint, int(18*speed), 18, 0, 1))  # 45 r
        else:
            ret = self.move_arm('rm_movej', (init_joint, int(18*speed), 18, 1, 0))  # 45 r
        if ret != 0:
            return ret

        # Move joints in groups to target position
        current_joint = init_joint.copy()
        for joint_idxes in [[0, 3], [5], [1, 2, 4]]:
            # Update current joint values with target values
            for joint_idx in joint_idxes:
                current_joint[joint_idx] = target_joint[joint_idx]
                
            # Use different velocity for different joint groups
            if 1 in joint_idxes or 2 in joint_idxes:
                tmp_v = 25  # Slower for main arm joints
            elif joint_idxes == [5]:
                tmp_v = 50  # Faster for wrist joints
            else:
                tmp_v = 28
                
            # Move to updated position
            if block_flag:
                ret = self.move_arm('rm_movej', (current_joint, min(80, int(tmp_v*speed)), 50, 0, 1))
            else:
                ret = self.move_arm('rm_movej', (current_joint, min(80, int(tmp_v*speed)), 50, 1, 0))
            if ret != 0:
                return ret
            # time.sleep(0.2)
            
        return 0

    @staticmethod
    def find_n(x, max_limit):
        n = math.ceil((math.log(max_limit) - math.log(x)) / math.log(0.8))
        return n, x * (0.8 ** n)
    def go_smoke_pole(self):
        """
        Move the smoke pole to the smoke detector.
        
        Uses visual servoing to continuously adjust the pole position
        until it reaches the detector or times out.
        
        Returns:
            0 on success, error code on failure
        """
        # Initialize counters and parameters
        count = 0
        count_max = 3000  # Maximum iterations
        ep_count = 0
        ep_count_max = 10  # Maximum end point length violations  # 30
        q_count = 0
        q_count_max = 300  # Maximum small movement counts
        timer_period = 110.0 * 1e-3  # 130ms control period  # 70.0 * 1e-3  # 70ms control period
        finish_count = 0.0
        finish_count_max = self.finish_count_max  # Iterations to confirm completion
        depth_err_count = 0
        depth_err_count_max = 300  # Maximum depth error counts
        no_sresult_count = 0
        no_sresult_count_max = 2000  # Maximum iterations without detection
        reached_target_ready_to_approach_flag = False
        while True:
            time.sleep(0.005)
            
            # Check for depth errors
            if depth_err_count > depth_err_count_max:
                self._error_message = "Depth error count exceeded maximum"
                return NO_DEPTH_INFO_ERROR

            # Check if pole control is still active
            if not self.go_pole_control_flag:
                self._error_message = "Pole control flag disabled - emergency stop or user interrupt"
                return EMERGENCY_STOP_ERROR
                
            # Skip iteration if paused
            if self._pause_mode:
                continue
                
            # Get current images
            ci = self.ci.copy() if self.ci is not None else None
            di = self.di.copy() if self.di is not None else None
            dc_res = self._detector_const_result.copy() if self._detector_const_result is not None else None
            pc_res = list(self._pole_const_result).copy() if self._pole_const_result is not None else None
            # Check if we have valid detection results
            if ci is None or di is None or dc_res is None or len(dc_res) < 3 or pc_res is None:
                no_sresult_count += 1
                if no_sresult_count % 5 == 0 and no_sresult_count > 35:
                    if ci is None:
                        print(f'ci is None; {no_sresult_count}')
                    if di is None:
                        print(f'di is None; {no_sresult_count}')
                    if dc_res is None:
                        print(f'dc_res is None; {no_sresult_count}')
                    elif len(dc_res) < 3:
                        print(f'len(dc_res) < 3: {dc_res}; {no_sresult_count}')
                    if pc_res is None:
                        print(f'pc_res is None; {no_sresult_count}')
                if no_sresult_count > no_sresult_count_max:
                    self._error_message = "No detection results available after maximum attempts"
                    return NO_DETECTOR_ERROR if dc_res is None else NO_POLE_ERROR
                continue
            no_sresult_count = max(0, no_sresult_count - 100)

            # Get current arm state
            current_state = self._cm.arm.rm_get_current_arm_state()
            cur_err_count = 0
            while current_state[0] != 0:
                if cur_err_count > 20:
                    self._error_message = f'Failed to get arm state after multiple attempts: {current_state}]'
                    return ARM_STATE_ERROR
                cur_err_count += 1
                print('Error getting current arm state')
                time.sleep(0.8)
                current_state = self._cm.arm.rm_get_current_arm_state()

            # Check for timeout
            if count > count_max:
                self._error_message = f"Timeout: count {count} exceeded maximum {count_max}"
                return POLE_TIMEOUT_ERROR
                
            # Get current end effector pose
            end_pose = current_state[1]['pose']
            ep_x, ep_y, ep_z = end_pose[:3]
            ep_len = np.linalg.norm(end_pose[:3])
            len_pole = self.len_pole
            
            # Check if end effector is too far from base
            if ep_len > self.ep_max_len:
                ep_count += 1
                print(f' * WARNING: {(1000 * ep_len):.2f} ARM LENGTH AT {ep_count}/{ep_count_max} TRIES')
            else:
                ep_count = max(0, ep_count - 1)
                
            # Return error if end effector is too far for too long
            if ep_count > ep_count_max:
                self._error_message = f"End point length {ep_len} exceeded maximum {self.ep_max_len} for too long, Cannot reach detector."
                return POLE_UNREACHABLE_ERROR


            # Calculate current orientation
            z_degree_now = np.degrees(end_pose[-1])
            self.text2 = f'[L={ep_len:.4f}]z_now:{int(z_degree_now)}, z_d:{int(self._detector_angle)}, z_delta:{int(abs(z_degree_now - self._detector_angle)) if self._detector_angle is not None else None}'

            # Convert joint angles to radians
            end_joint = current_state[1]['joint']
            joints_rad = [math.radians(item) for item in end_joint]
            
            # Create transformation matrices
            mat_end_to_base = np.eye(4)  # base_to_hand
            mat_end_to_base[:3, :3] = R.from_euler('xyz', end_pose[3:], degrees=False).as_matrix()
            mat_end_to_base[:3, 3] = end_pose[:3]

            mat_camera_to_end = np.eye(4)
            mat_camera_to_end[:3, :3] = self._cm.rotation_matrix
            mat_camera_to_end[:3, 3] = self._cm.translation_vector

            camera_to_base = mat_end_to_base.dot(mat_camera_to_end)
            rotaion_mat = camera_to_base[:3, :3]

            # Get depth information for detector
            depth_detector = dc_res[3] + self.detector_height
            depth_neighbour, depth_neighbour_flag = self.get_area_depth(di, dc_res[0], dc_res[1])
                
            self.text3 = f'd(D):{depth_detector}, d(neigh):{depth_neighbour}({depth_neighbour_flag})'
            print(f'd(D):{depth_detector}, d(neigh):{depth_neighbour}({depth_neighbour_flag})')

            # Use neighboring depth if direct depth is unreliable
            if depth_neighbour_flag:
                if depth_detector <= 200:
                    depth_detector = max(depth_detector, depth_neighbour)
                elif abs(depth_detector - depth_neighbour) > 30:
                    depth_detector = max(depth_detector, depth_neighbour)
                else:
                    depth_detector = max(depth_detector, depth_neighbour)
                depth_err_count = 0
            else:
                if depth_detector <= 200:
                    depth_err_count += 1
                else:
                    depth_err_count = 0

            # Calculate temporary/final target position based on detector and pole positions
            detector_neighbour, final_target_flag = get_positions(dc_res, pc_res)

            if reached_target_ready_to_approach_flag and final_target_flag:
                detector_neighbour[0] = detector_neighbour[0] - dc_res[2] * self.approach_distance_ratio
            # Calculate distance to target
            _delta_z = len_pole - depth_detector
            if depth_detector <= 200:
                _delta_z = 0  # Cannot get correct depth, regard delta z as 0 temporarily
                final_target_flag = False
                
                
            # Set up vectors for visual servoing
            desired_coord = np.array([detector_neighbour[0], detector_neighbour[1], depth_detector])
            current_coord = np.array([pc_res[0] - pc_res[2], pc_res[1], len_pole])
                
            # Calculate velocity command
            velocity, d_distance = self.offset_calculate(
                rotaion_mat, 
                _current_feature=current_coord[:2],
                _desired_feature=desired_coord[:2],
                _delta_z=max(_delta_z, -50.0))
            print(f'go_pole_offset:{self._offset_text};delta_z={_delta_z};(final_flag={final_target_flag})')
            # Convert velocity to joint space
            jacobian = self._cm.rob.jacob0(joints_rad)
            jacobian_inv = np.linalg.pinv(jacobian)
            dq = np.dot(jacobian_inv, velocity)
            vector = np.concatenate([np.array(matrix).ravel() for matrix in dq])
            
            # Check if target is reached
            if (d_distance < self.distance_thresh[0] and not reached_target_ready_to_approach_flag) or (d_distance < self.distance_thresh[1] and reached_target_ready_to_approach_flag) :
                if final_target_flag:
                    if not reached_target_ready_to_approach_flag:
                        ep_count = max(0, ep_count - 5)
                        q_count = max(0, ep_count - 100)
                        depth_err_count = max(0, ep_count - 100)
                        no_sresult_count = max(0, ep_count - 1000)
                        reached_target_ready_to_approach_flag = True
                        print(f'Reached target; Ready to approach! d_distance={d_distance:.4f}')
                        continue
                    finish_count += 1
                    if finish_count > finish_count_max:
                        break
                else:
                    finish_count = max(0, finish_count - 0.3)
                    continue
            else:
                finish_count = max(0, finish_count - 0.3)
            if finish_count != 0:
                print(f'finish_count={finish_count:.1f}, d_distance={d_distance:.4f}')
                
            # Scale velocity if near target
            if ep_count > 1 or reached_target_ready_to_approach_flag:
                max_joint_distance_limit = self.max_joint_distance_limit[0]  # 4  # 2
            else:
                max_joint_distance_limit = self.max_joint_distance_limit[1]  # 16  # 10
            tmp_timer_period = timer_period
            if finish_count != 0 or reached_target_ready_to_approach_flag:
                tmp_timer_period = timer_period / self.closer_speed_ratio
                q = np.degrees(joints_rad + vector.T * timer_period / self.closer_speed_ratio)  # / 5
            else:
                q = np.degrees(joints_rad + vector.T * timer_period)
                
            # Apply joint limits
            q_calibrated = np.where(q > self.max_joint_restrictions, self.max_joint_restrictions, q)
            q = np.where(q_calibrated < self.min_joint_restrictions, self.min_joint_restrictions, q_calibrated)

            # Check if movement is too small
            total_joint_distance = np.sqrt(np.sum((np.array(q[:-1]) - np.array(end_joint[:-1])) ** 2))
            if total_joint_distance < 0.1:
                q_count += 1
            else:
                q_count = max(0, q_count - 1)

            # Return error if movement is too small for too long
            if q_count > q_count_max:
                if d_distance < self.distance_thresh[0] and final_target_flag:
                    print('early stop due to q_count reach the q_count_max')
                    break
                self._error_message = f"Joint movement too small for too long, count: {q_count}"
                return POLE_TIMEOUT_ERROR
            # Limit movement size for stability
            # tmp_timer_period = timer_period
            if total_joint_distance > max_joint_distance_limit:
                init_jd = total_joint_distance
                iter_n, _ = self.find_n(total_joint_distance, max_joint_distance_limit)
                tmp_timer_period *= 0.8 ** iter_n

                q = np.degrees(joints_rad + vector.T * tmp_timer_period)
                q_calibrated = np.where(q > self.max_joint_restrictions, self.max_joint_restrictions, q)
                q = np.where(q_calibrated < self.min_joint_restrictions, self.min_joint_restrictions, q_calibrated)
                total_joint_distance = np.sqrt(np.sum((np.array(q[:-1]) - np.array(end_joint[:-1])) ** 2))
                print(f'>joints may reach maximum speed: {init_jd:.4f} -> {total_joint_distance:.4f}')
            # while total_joint_distance > 10:
            #     tmp_timer_period *= 0.8
            #     q = np.degrees(joints_rad + vector.T * tmp_timer_period)
            #     q_calibrated = np.where(q > self.max_joint_restrictions, self.max_joint_restrictions, q)
            #     q = np.where(q_calibrated < self.min_joint_restrictions, self.min_joint_restrictions, q_calibrated)
            #     total_joint_distance = np.sqrt(np.sum((np.array(q[:-1]) - np.array(end_joint[:-1])) ** 2))
            print(f'[{count}]({reached_target_ready_to_approach_flag}), target_dis: {d_distance:.2f}, joints_move_dis: {total_joint_distance:.2f}, dt:{tmp_timer_period}, q:{np.int_(q)}')
                
            # Execute movement
            ret = self.move_arm('rm_movej_canfd', (q, False, 0, 0, 50))  # True for high-follow, False for low-follow (≥10ms)
            if ret != 0:
                return ret
                
            count += 1
            self._detector_const_result = None
            
        return SUCCESS_CODE

    def detector_detection(self, record_len=10, detect_pole_flag=True):
        """
        Detect smoke detector and pole in camera images.
        
        Args:
            record_len: Number of frames to process
            detect_pole_flag: Whether to detect pole as well
            
        Returns:
            Tuple of (detector_result, pole_result)
        """
        detect_count = 0
        detect_count_max = record_len * 1.5#30
        _detector_res_list, _pole_res_list = [], []
        _detector_result_tmp, _pole_result_tmp = None, None
        
        # Process multiple frames to get stable detection
        while detect_count < detect_count_max:
            detect_count += 1
            time.sleep(0.01)
            
            # Get current camera image
            ci = self.ci.copy() if self.ci is not None else None
            if ci is None:
                continue

            # Detect smoke detector
            detector_coord, _ = self._detector_model.detect(ci, detect_count=detect_count)
            if detector_coord is not None:
                _detector_res_list.append(detector_coord)
            else:
                _detector_res_list.append(None)
                
            # Limit list length
            _detector_res_list = _detector_res_list[-record_len:]
            
            # Update detector result
            _detector_result_tmp = update_result(_detector_res_list, None, record_len)

            # Detect pole if requested
            if detect_pole_flag:
                pole_coord, _, _ = pole_detection(ci)
                if pole_coord is not None:
                    _pole_res_list.append(pole_coord)
                else:
                    _pole_res_list.append(None)
                    
                # Limit list length
                _pole_res_list = _pole_res_list[-record_len:]
                
                # Update pole result
                _pole_result_tmp = update_result(_pole_res_list, None, record_len)
                
            # Break if we have both results (or just detector if pole not needed)
            if _detector_result_tmp is not None and (not detect_pole_flag or _pole_result_tmp is not None):
                break
                
        return _detector_result_tmp, _pole_result_tmp

    def update_frames_and_detection(self):
        """
        Main processing loop for camera frames and object detection.
        
        This function runs continuously in a separate thread and handles:
        1. Frame acquisition from the camera
        2. Detection of poles and smoke detectors
        3. Calibration target detection
        4. Visualization of detected objects
        5. User interface via keyboard input
        
        The function updates class variables with detection results that are used
        by other functions for robot control.
        """
        while True:
            time.sleep(0.01)
            # Only acquire frames if not paused or if in active task
            if not self._frame_acquisition_paused or self._task_status == "running":
                self.ci = self._cm.color_image.copy() if self._cm.color_image is not None else None
                self.di = self._cm.depth_image.copy() if self._cm.depth_image is not None else None
                
            # Skip if no frames available
            if self.ci is None or self.di is None:
                continue
                
            # Skip heavy detection if paused and not in active task
            if self._detection_paused and self._task_status != "running":
                self._detector_res_list = []
                self._pole_res_list = []
                self._detector_const_result = []
                self._pole_const_result = []
                
                if self.visible:
                    # Display paused state
                    display_img = self.ci.copy()
                    cv2.namedWindow('camera_rgb')
                    cv2.setMouseCallback('camera_rgb', mouse_callback,
                                         {'di': self.di, 'depth_intrin': self._cm.depth_intrin})
                    cv2.putText(display_img, "Detection paused", (10, 30),
                                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                    cv2.imshow('camera_rgb', display_img)
                    k = cv2.waitKey(1) & 0xFF
                    self._handle_key_input(k)
                continue

            # Create a copy of the color image for visualization
            display_img = self.ci.copy()
            
            # Detect pole and smoke detector if enabled
            if self._detector_activate_flag:
                # Detect pole
                pole_coord, _, _ = pole_detection(self.ci)
                if pole_coord is not None:
                    self._pole_res_list.append(pole_coord)
                else:
                    self._pole_res_list.append(None)
                    
                # Detect smoke detector
                detector_coord, _ = self._detector_model.detect(self.ci)
                if detector_coord is not None:
                    self._detector_res_list.append(detector_coord)
                else:
                    self._detector_res_list.append(None)
                    
                # Maintain fixed-length detection history
                self._pole_res_list = self._pole_res_list[-self._record_len:]
                self._detector_res_list = self._detector_res_list[-self._record_len:]
                
                # Update pole detection result
                self._pole_const_result = update_result(self._pole_res_list, self._pole_const_result, self._record_len)
                if self._pole_const_result is not None and self.visible:
                    # Visualize pole detection
                    cv2.circle(display_img, self._pole_const_result[:2], 2, (255, 0, 0), -1)
                    cv2.circle(display_img, self._pole_const_result[:2], self._pole_const_result[2], (255, 0, 0), 2)
                    
                # Update detector detection result
                _detector_result_tmp = update_result(self._detector_res_list, self._detector_const_result,
                                                     self._record_len)
                if _detector_result_tmp is not None:
                    # Store detector position and depth
                    self._detector_const_result = [_detector_result_tmp[0], _detector_result_tmp[1],
                                                   _detector_result_tmp[2],
                                                   self.di[int(_detector_result_tmp[1]), int(_detector_result_tmp[0])]]
                    if self.visible:
                        # Visualize detector detection
                        cv2.circle(display_img, _detector_result_tmp[:2], 2, (0, 255, 0), -1)
                        cv2.circle(display_img, _detector_result_tmp[:2], _detector_result_tmp[2], (0, 255, 0), 2)
                else:
                    self._detector_const_result = None
                    
                # Calculate detector angle if detected
                if _detector_result_tmp is not None:
                    # Convert detector position from camera to 3D coordinates
                    point_3d1 = rs.rs2_deproject_pixel_to_point(
                        self._cm.depth_intrin,
                        (float(_detector_result_tmp[0]), float(_detector_result_tmp[1])),
                        self.di[int(_detector_result_tmp[1]), int(_detector_result_tmp[0])] / 1000.0
                    )

                    # Get current arm state
                    current_state = self._cm.arm.rm_get_current_arm_state()
                    cur_err_count = 0
                    while current_state[0] != 0:
                        if cur_err_count > 20:
                            self._error_message = f'[Failed to get arm state after multiple attempts: {current_state}]'
                            raise ValueError("Failed to get arm state", ARM_STATE_ERROR)

                        cur_err_count += 1
                        print('error in get current state')
                        time.sleep(0.8)
                        current_state = self._cm.arm.rm_get_current_arm_state()

                    # Calculate detector angle in robot base coordinates
                    end_pose1 = current_state[1]['pose']
                    t_obj_to_base = convert(point_3d1, end_pose1)
                    z_angle0 = math.degrees(math.atan2((t_obj_to_base[1]), (t_obj_to_base[0]))) + 90

                    # Normalize angle to [-180, 180] range
                    z_angle0 = z_angle0 - 360 if z_angle0 >= 180 else z_angle0 + 360 if z_angle0 <= -180 else z_angle0
                    self._detector_angle = z_angle0
                    
                    if self.visible:
                        # Display detector angle and position
                        cv2.putText(display_img, f"detector:{z_angle0:.1f}`,[{np.int_(t_obj_to_base * 1000)}]",
                                    (10, 80),
                                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, (240, 255, 0), 1,
                                    cv2.LINE_AA)
            else:
                # Clear detection results if detector is disabled
                self._detector_res_list = []
                self._pole_res_list = []
                self._detector_const_result = []
                self._pole_const_result = []
                
            # Handle calibration detection if enabled
            if self._calibrate_detect_flag:
                # Draw calibration target crosshair
                c_x, c_y = self._calib_desired_coord
                if self.visible:
                    cv2.line(display_img, (c_x, 0), (c_x, display_img.shape[0]), (180, 180, 180), 1)
                    cv2.line(display_img, (0, c_y), (display_img.shape[1], c_y), (180, 180, 180), 1)
                    
                # Detect black rectangle for calibration
                rectangle_points = calibrate_handler_detection(self.ci)

                if rectangle_points is not None:
                    # Calculate midpoints of rectangle sides
                    left_mid = (int((rectangle_points[0][0] + rectangle_points[3][0]) / 2),
                                int((rectangle_points[0][1] + rectangle_points[3][1]) / 2))
                    right_mid = (int((rectangle_points[1][0] + rectangle_points[2][0]) / 2),
                                 int((rectangle_points[1][1] + rectangle_points[2][1]) / 2))
                    self._calibrate_res_list.append(
                        [rectangle_points[0], rectangle_points[2], rectangle_points[1], rectangle_points[3],
                         left_mid, right_mid])
                else:
                    self._calibrate_res_list.append(None)

                # Update calibration result
                _calibrate_pts_tmp = update_result(self._calibrate_res_list, self._calibrate_const_result,
                                                   self._record_len, False)
                if _calibrate_pts_tmp is not None:
                    # Extract rectangle points and midpoints
                    r0, r2, r1, r3, left_mid, right_mid = _calibrate_pts_tmp
                    if self.visible:
                        # Visualize calibration points
                        cv2.circle(display_img, left_mid, 4, (0, 255, 255), -1)
                        cv2.circle(display_img, right_mid, 4, (0, 255, 255), -1)
                        cv2.drawContours(display_img, [np.int_([r0, r1, r2, r3])], -1, (0, 180, 180), 2)
                        
                    # Calculate rotation angle
                    rotate_angle = math.degrees(
                        math.atan2((_calibrate_pts_tmp[-1][1] - _calibrate_pts_tmp[-2][1]),
                                   (_calibrate_pts_tmp[-1][0] - _calibrate_pts_tmp[-2][0])))
                    rotate_angle += self.calib_rotate_bias
                    
                    if self.visible:
                        # Display rotation angle
                        cv2.putText(display_img, f"[{rotate_angle:.2f}`", (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.5,
                                    (240, 255, 0), 1, cv2.LINE_AA)
                                    
                self._calibrate_const_result = _calibrate_pts_tmp

                # Maintain fixed-length calibration history
                self._calibrate_res_list = self._calibrate_res_list[-self._record_len:]
            else:
                self._calibrate_const_result = None
                self._calibrate_res_list = []
            if self._calibrate_control_flag:
                print(f'calib_offset:{self._offset_text}')
            # elif self.go_pole_control_flag:
            #     print(f'go_pole_offset:{self._offset_text}')
            if self.visible:
                # Display calibration status and additional information
                cv2.putText(display_img, f"[{self._offset_text}](calib=>{self._calibrate_control_flag})", (10, 40),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.5, (240, 255, 0), 1, cv2.LINE_AA)
                cv2.putText(display_img, f"CHangle={self.text1}", (10, 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5,
                            (240, 255, 0), 1, cv2.LINE_AA)
                cv2.putText(display_img, self.text2, (10, 100), cv2.FONT_HERSHEY_SIMPLEX, 0.5,
                            (240, 255, 0), 1, cv2.LINE_AA)
                cv2.putText(display_img, self.text3, (10, 120), cv2.FONT_HERSHEY_SIMPLEX, 0.5,
                            (240, 255, 0), 1, cv2.LINE_AA)

            # Display task status
            if self._task_status != "idle" and self.visible:
                status_color = (0, 255, 0) if self._task_status == "completed" else (
                    0, 0, 255) if self._task_status == "error" else (255, 165, 0)
                cv2.putText(display_img, f"Status: {self._task_status}", (10, 140), cv2.FONT_HERSHEY_SIMPLEX, 0.5,
                            status_color, 1, cv2.LINE_AA)
                if self._task_status == "error" and self._error_message:
                    cv2.putText(display_img, f"Error: {self._error_message[:50]}...", (10, 160),
                                cv2.FONT_HERSHEY_SIMPLEX, 0.5,
                                (0, 0, 255), 1, cv2.LINE_AA)
                if self._task_status == "running" and len(self.wait_message) > 0:
                    cv2.putText(display_img, self.wait_message, (490, 15),
                                cv2.FONT_HERSHEY_SIMPLEX, 0.5,
                                (0, 255, 0), 1, cv2.LINE_AA)

            # Display emergency stop status
            if self._emergency_stop and self.visible:
                cv2.putText(display_img, "EMERGENCY STOP ACTIVE", (10, 180), cv2.FONT_HERSHEY_SIMPLEX, 0.7,
                            (0, 0, 255), 2, cv2.LINE_AA)

            # Display the image if visualization is enabled
            if self.visible:
                cv2.namedWindow('camera_rgb')
                cv2.setMouseCallback('camera_rgb', mouse_callback,
                                     {'di': self.di, 'depth_intrin': self._cm.depth_intrin})
                cv2.imshow('camera_rgb', display_img)
                k = cv2.waitKey(1) & 0xFF
                self._handle_key_input(k)

    def _handle_key_input(self, k):
        """Handle key input from CV window"""
        if k == 27:  # ESC
            return True  # Signal to exit

        if k == ord('p'):  # Pause/Resume
            if self._pause_mode:
                self._pause_mode = False
                print("p: resume")
            else:
                self._pause_mode = True
                print("p: pause")

        elif k == ord('w'):  # Emergency stop
            self._calibrate_control_flag = False
            self.go_pole_control_flag = False
            self.emergency_stop()
            print(f'w: emergency stop; _calib_control:{self._calibrate_control_flag},self.go_pole_control_flag = False')

        elif k == ord('c'):  # Reload SmokePole configuration
            self.reload_config()
            print("c: SmokePole configuration reloaded")
            
        elif k == ord('C'):  # Reload both SmokePole and Camera configurations
            self.reload_config()
            self._cm.reload_config()
            print("C: SmokePole and Camera configurations reloaded")

        elif k == ord('0'):
            self._cmd = 0
        elif k == ord('1'):
            self._cmd = 1
        elif k == ord('2'):
            self._cmd = 2
        elif k == ord('3'):
            self._cmd = 3
        elif k == ord('4'):
            self._cmd = 4
        elif k == ord('5'):
            self._cmd = 5
        elif k == ord('6'):
            self._cmd = 6
        elif k == ord('7'):
            self._cmd = 7
        elif k == ord('8'):
            self._cmd = None
        elif k == ord('9'):
            self._cmd = 9
        elif k == ord('u'):
            self._cmd = 'u'
        elif k == ord('r'):
            self._cmd = 'r'
        elif k == ord('t'):
            self._cmd = 't'
        elif k == ord('s'):
            self._cmd = 'smoke'
        elif k == ord('f'):
            self._cmd = 'find'
        elif k == ord('h'):
            self._cmd = 'home'
        elif k == ord('z'):  # Save image
            if self._runtime == '':
                self._runtime = datetime.datetime.now().strftime("%m%d%H%M")
            cv2.imwrite(f'data/shot-images/imc_{self._runtime}_{self.save_count:03d}.jpg', self.ci)
            np.save(f'data/shot-images/imd_{self._runtime}_{self.save_count:03d}.npy', self.di)
            print(f'save imc_{self._runtime}_{self.save_count:03d}.jpg, imd_{self._runtime}_{self.save_count:03d}.npy')
            self.save_count += 1

        return False  # Continue execution


def convert(point_camera, end_pose, translation_vector=None, rotation_matrix=None):
    """
    Convert a point from camera coordinates to robot base coordinates.
    
    This function performs coordinate transformation from camera frame to robot base frame,
    taking into account the current robot pose and camera calibration parameters.
    
    Args:
        point_camera: Point coordinates in camera frame [x, y, z] or [x, y, z, rx, ry, rz]
        end_pose: Current end effector pose [x, y, z, rx, ry, rz]
        translation_vector: Optional camera-to-end-effector translation vector
        rotation_matrix: Optional camera-to-end-effector rotation matrix
        
    Returns:
        Point coordinates in robot base frame [x, y, z, rx, ry, rz]
    """
    
    mat_end_to_base = np.eye(4)
    mat_end_to_base[:3, :3] = R.from_euler('xyz', end_pose[3:], degrees=False).as_matrix()
    mat_end_to_base[:3, 3] = end_pose[:3]
    if translation_vector is None:
        translation_vector = np.array([0.********, -0.********, 0.********])
    if rotation_matrix is None:
        rotation_matrix = np.array([[-0.********, -0.********, -0.********],
                                    [0.********, -0.0392032, 0.********],
                                    [-0.00238501, -0.03280746, 0.99945884]])
    mat_camera_to_end = np.eye(4)
    mat_camera_to_end[:3, :3] = rotation_matrix
    mat_camera_to_end[:3, 3] = translation_vector

    if len(point_camera) == 3:
        obj_camera_coordinates = point_camera
        obj_orientation = R.from_euler('xyz', end_pose[3:], degrees=False).as_matrix()
    else:
        obj_camera_coordinates = point_camera[:3]
        obj_orientation = R.from_euler('xyz', point_camera[3:], degrees=False).as_matrix()
    mat_obj_to_camera = np.eye(4)
    mat_obj_to_camera[:3, :3] = obj_orientation
    mat_obj_to_camera[:3, 3] = obj_camera_coordinates
    mat_obj_to_base = mat_end_to_base.dot(mat_camera_to_end).dot(mat_obj_to_camera)
    result = np.concatenate(
        [mat_obj_to_base[:3, 3], R.from_matrix(mat_obj_to_base[:3, :3]).as_euler('xyz', degrees=False)])
    return result


if __name__ == "__main__":
    # Load configuration
    pole_config_path = "config/pole_params.yaml"
    camera_config_path = "config/camera_params.yaml"
    
    # Initialize camera with configuration
    cm = Camera(config_path=camera_config_path)
    thread_c = threading.Thread(target=cm.run)
    thread_c.start()
    
    # Initialize smoke pole with configuration
    sp = SmokePole(cm, config_path=pole_config_path)
    
    # Start processing threads
    thread_sp = threading.Thread(target=sp.update_frames_and_detection)
    thread_sp.start()
    thread_sp_cmd = threading.Thread(target=sp.run_cmd)
    thread_sp_cmd.start()
    
    # Main loop
    while True:
        time.sleep(0.01)
        pass
