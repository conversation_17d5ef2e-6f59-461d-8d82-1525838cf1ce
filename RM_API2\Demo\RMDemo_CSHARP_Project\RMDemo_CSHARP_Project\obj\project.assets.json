{"version": 3, "targets": {"net6.0": {}}, "libraries": {}, "projectFileDependencyGroups": {"net6.0": []}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "H:\\Desktop\\1.0.0\\Demo\\RMDemo_CSHARP_Project\\RMDemo_CSHARP_Project\\RMDemo_CSHARP_Project.csproj", "projectName": "RMDemo_CSHARP_Project", "projectPath": "H:\\Desktop\\1.0.0\\Demo\\RMDemo_CSHARP_Project\\RMDemo_CSHARP_Project\\RMDemo_CSHARP_Project.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "H:\\Desktop\\1.0.0\\Demo\\RMDemo_CSHARP_Project\\RMDemo_CSHARP_Project\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.306\\RuntimeIdentifierGraph.json"}}}}