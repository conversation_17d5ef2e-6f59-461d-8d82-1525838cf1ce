#!/usr/bin/env python3
"""
Script to optimize imports in button_processor.py and buttonControl modules.
Removes redundant local imports of time and other modules.
"""

import os
import re
import sys
from typing import List, Dict, Tuple, Set

class ImportOptimizer:
    """Optimize imports in Python files."""
    
    def __init__(self):
        self.files_to_process = [
            'task/button_processor.py',
            'buttonControl/button_action.py',
            'buttonControl/finetune.py',
            'buttonControl/data_manager.py'
        ]
        self.optimization_results = {}
    
    def analyze_file_imports(self, file_path: str) -> Dict[str, any]:
        """Analyze imports in a file."""
        if not os.path.exists(file_path):
            return {'error': f'File not found: {file_path}'}
        
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        global_imports = set()
        local_imports = []  # (line_number, import_statement, module_name)
        
        # Find global imports (typically at the top of the file)
        in_global_section = True
        for i, line in enumerate(lines):
            stripped = line.strip()
            
            # Skip comments and empty lines
            if not stripped or stripped.startswith('#'):
                continue
            
            # Check if we're still in global section
            if in_global_section:
                if (stripped.startswith('import ') or stripped.startswith('from ')) and not stripped.startswith('from .'):
                    # Extract module name
                    if stripped.startswith('import '):
                        module = stripped.split()[1].split('.')[0]
                        global_imports.add(module)
                    elif stripped.startswith('from ') and ' import ' in stripped:
                        module = stripped.split()[1].split('.')[0]
                        global_imports.add(module)
                elif stripped.startswith(('class ', 'def ', 'if ', 'try:', 'with ')):
                    in_global_section = False
            
            # Find local imports (inside functions/methods)
            if not in_global_section:
                if re.match(r'\s*import\s+(\w+)', stripped) or re.match(r'\s*from\s+(\w+)\s+import', stripped):
                    # Extract module name
                    if stripped.strip().startswith('import '):
                        module = stripped.strip().split()[1].split('.')[0]
                    elif stripped.strip().startswith('from ') and ' import ' in stripped.strip():
                        module = stripped.strip().split()[1].split('.')[0]
                    else:
                        module = 'unknown'
                    
                    local_imports.append((i + 1, stripped, module))
        
        return {
            'global_imports': global_imports,
            'local_imports': local_imports,
            'total_lines': len(lines),
            'lines': lines
        }
    
    def find_redundant_imports(self, analysis: Dict[str, any]) -> List[Tuple[int, str, str]]:
        """Find local imports that are redundant with global imports."""
        redundant = []
        global_imports = analysis['global_imports']
        
        for line_num, import_stmt, module in analysis['local_imports']:
            if module in global_imports:
                redundant.append((line_num, import_stmt, module))
        
        return redundant
    
    def optimize_file(self, file_path: str, dry_run: bool = True) -> Dict[str, any]:
        """Optimize imports in a single file."""
        print(f"\nAnalyzing {file_path}...")
        
        analysis = self.analyze_file_imports(file_path)
        if 'error' in analysis:
            return analysis
        
        redundant_imports = self.find_redundant_imports(analysis)
        
        if not redundant_imports:
            print(f"  ✓ No redundant imports found in {file_path}")
            return {
                'file': file_path,
                'redundant_imports': [],
                'changes_made': 0,
                'status': 'no_changes_needed'
            }
        
        print(f"  Found {len(redundant_imports)} redundant imports:")
        for line_num, import_stmt, module in redundant_imports:
            print(f"    Line {line_num}: {import_stmt.strip()} (module: {module})")
        
        if dry_run:
            print(f"  [DRY RUN] Would remove {len(redundant_imports)} redundant imports")
            return {
                'file': file_path,
                'redundant_imports': redundant_imports,
                'changes_made': 0,
                'status': 'dry_run'
            }
        
        # Actually remove redundant imports
        lines = analysis['lines']
        lines_to_remove = set(line_num - 1 for line_num, _, _ in redundant_imports)  # Convert to 0-based
        
        optimized_lines = []
        removed_count = 0
        
        for i, line in enumerate(lines):
            if i in lines_to_remove:
                # Check if the line only contains the import (no other code)
                if line.strip().startswith(('import ', 'from ')) and line.strip().endswith(('import time', 'import traceback', 'import numpy', 'import os')):
                    # Skip this line (remove it)
                    removed_count += 1
                    continue
                else:
                    # Keep the line but add a comment
                    optimized_lines.append(f"# REMOVED REDUNDANT IMPORT: {line}")
                    removed_count += 1
            else:
                optimized_lines.append(line)
        
        # Write optimized file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.writelines(optimized_lines)
        
        print(f"  ✓ Removed {removed_count} redundant imports from {file_path}")
        
        return {
            'file': file_path,
            'redundant_imports': redundant_imports,
            'changes_made': removed_count,
            'status': 'optimized'
        }
    
    def optimize_all_files(self, dry_run: bool = True) -> Dict[str, any]:
        """Optimize imports in all specified files."""
        print("=" * 60)
        print("Import Optimization Analysis")
        print("=" * 60)
        
        total_redundant = 0
        total_changes = 0
        
        for file_path in self.files_to_process:
            result = self.optimize_file(file_path, dry_run)
            self.optimization_results[file_path] = result
            
            if 'redundant_imports' in result:
                total_redundant += len(result['redundant_imports'])
            if 'changes_made' in result:
                total_changes += result['changes_made']
        
        print("\n" + "=" * 60)
        print("Optimization Summary")
        print("=" * 60)
        print(f"Files processed: {len(self.files_to_process)}")
        print(f"Total redundant imports found: {total_redundant}")
        
        if dry_run:
            print(f"Changes that would be made: {total_redundant}")
            print("\nTo apply changes, run with dry_run=False")
        else:
            print(f"Changes made: {total_changes}")
        
        return {
            'files_processed': len(self.files_to_process),
            'total_redundant': total_redundant,
            'total_changes': total_changes,
            'results': self.optimization_results
        }

def test_optimized_imports():
    """Test that optimized imports still work correctly."""
    print("\n" + "=" * 60)
    print("Testing Optimized Imports")
    print("=" * 60)
    
    try:
        # Test importing the main module
        sys.path.append('task')
        
        # Test basic imports that should still work
        import time
        import traceback
        import numpy as np
        
        # Test time functions
        start_time = time.time()
        time.sleep(0.01)
        end_time = time.time()
        
        assert end_time > start_time, "Time functions not working"
        
        # Test traceback
        try:
            raise ValueError("Test error")
        except ValueError:
            tb = traceback.format_exc()
            assert "Test error" in tb, "Traceback not working"
        
        # Test numpy
        arr = np.array([1, 2, 3])
        assert len(arr) == 3, "Numpy not working"
        
        print("✓ All optimized imports work correctly")
        return True
        
    except Exception as e:
        print(f"✗ Import test failed: {e}")
        return False

def main():
    """Main function."""
    optimizer = ImportOptimizer()

    # First run in dry-run mode to see what would be changed
    print("Running in DRY RUN mode first...")
    dry_results = optimizer.optimize_all_files(dry_run=True)

    if dry_results['total_redundant'] == 0:
        print("\n✓ No redundant imports found. No optimization needed.")
        return 0

    # Automatically apply optimizations
    print(f"\nFound {dry_results['total_redundant']} redundant imports.")
    print("Applying optimizations automatically...")
    real_results = optimizer.optimize_all_files(dry_run=False)

    # Test that everything still works
    if test_optimized_imports():
        print("\n✓ Import optimization completed successfully!")
        return 0
    else:
        print("\n✗ Import optimization may have caused issues!")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
