import socket
import json
import threading
import time
import os
import sys
import datetime
import traceback

# Global variables
server_exit = False
server_socket = None
client_socket = None
client_address = None
stop_event = threading.Event()
last_activity_time = time.time()

# Error code reference dictionary for user-friendly messages
ERROR_CODES = {
    0: "Success",
    1001: "Arm state error - Failed to get arm state after multiple attempts",
    1002: "Arm movement error - General arm movement error",
    1003: "Gripper state error - Failed to get gripper state",
    1004: "Gripper control error - Failed to control gripper",
    1005: "Arm trajectory timeout error - Timeout when checking arm trajectory",
    2001: "No detector error - No smoke-detector found",
    2002: "No pole error - No smoking-pole detected",
    2003: "No pole handler error - No smoking-pole handler found",
    2004: "No depth info error - No information of detector depth",
    2005: "Pole timeout error - Timeout when chasing detector",
    2006: "Pole unreachable error - Cannot reach out detector",
    2011: "Button detection error - Button/knob detection failed",
    2012: "Button approach error - Button/knob approach failed",
    2013: "Button operation error - Button click/knob turn failed",
    2014: "Finetune error - Finetune operation failed",
    2015: "Button state error - Invalid button state/operation",
    2016: "Button config error - Button configuration error",
    3001: "Camera error - Camera not available or error",
    4001: "Emergency stop error - Emergency stop command received",
    5001: "Memory error - No available memory for picture storage or RAM",
    6001: "Parsing error - Parsing received command JSON error",
    9999: "General error - Uncategorized error"
}


def start_server(port=17852):
    """Start the server and listen for connections"""
    global server_socket, client_socket, client_address, server_exit
    
    # Create socket
    server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
    server_socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
    # Bind to port
    server_socket.bind(('', port))
    
    # Listen for connections
    server_socket.listen(1)
    print(f"Server started on port {port}")
    print("Waiting for client connection...")
    
    try:
        # Accept connection
        client_socket, client_address = server_socket.accept()
        print(f"Client connected: {client_address}")
        
        # Start receiver thread
        receiver_thread = threading.Thread(target=receive_responses)
        receiver_thread.daemon = True
        receiver_thread.start()
        
        # Start command menu
        command_menu()
        
    except KeyboardInterrupt:
        server_exit = True
        print("\nServer shutting down...")
    finally:
        stop_event.set()
        if client_socket:
            client_socket.close()
        if server_socket:
            server_socket.close()

def receive_responses():
    """Thread function to receive and display responses from client"""
    global client_socket, server_exit
    
    while not stop_event.is_set():
        if server_exit:
            print('Receive Exit signal, Exiting...')
            break
        try:
            # Wait for response
            data = client_socket.recv(4096)
            if not data:
                print("\nClient disconnected")
                break

            # Parse response
            response_str = data.decode('utf-8')
            try:
                response = json.loads(response_str)
                print(response)
                print("\n--- Received Response ---")
                print(datetime.datetime.now().strftime("%m/%d %H:%M:%S"))
                print(f"Code: {response.get('code', 9999)} - {ERROR_CODES.get(response.get('code', 9999), 'Unknown error')}")
                print(f"status: {response.get('status', 'N/A')}")
                if 'message' in response:
                    print(f"Message: {response.get('message')}")
                if 'data' in response:
                    print(f"data: {response.get('data')}")
                if 'task' in response:
                    print(f"Task: {response.get('task')}")
                if 'command' in response:
                    print(f"Command: {response.get('command')}")
                print("------------------------\n")
                print("Enter command number: ", end="", flush=True)
            except json.JSONDecodeError:
                print(f"\nError parsing response: {response_str}")
                print("Enter command number: ", end="", flush=True)
                
        except Exception as e:
            print(f"\nError receiving response: {e}\n{traceback.format_exc()}")
            break
    
    print("Receiver thread stopped")
    server_exit = True
    print('Set server_exit=True')
    exit(1)

def send_command(command_json):
    """Send a command to the client"""
    global client_socket
    
    try:
        # Convert command to JSON string
        command_str = json.dumps(command_json)
        
        # Send command
        client_socket.send(command_str.encode('utf-8'))
        print(f"Sent command: {command_str}")
        
    except Exception as e:
        print(f"Error sending command: {e}")

def command_menu():
    """Display command menu and handle user input"""
    global server_exit, last_activity_time
    while not stop_event.is_set():
        if server_exit:
            print('Receive Exit signal, Exiting command_menu...')
            break
        print("\n--- Command Menu ---")
        print("1. Start smoke_test Command (Default)")
        print("2. Start smoke_test Command (Custom Length & Wait Time)")
        print("3. Start photo Command")
        print("4. Start control_toggle Command")
        print("5. Emergency Stop Command")
        print("6. Reset Command")
        print("7. Pause Command (not implemented)")
        print("8. Resume Command (not implemented)")
        print("9. Start therm_test Command (not implemented)")
        print("10. Debug: Capture Current View")
        print("0. Exit")
        print("------------------")
        
        try:
            choice = input("Enter command number: ")
            last_activity_time = time.time()
            if choice == "0":
                server_exit = True
                print("Exiting...")
                break
            elif choice == "7":
                print("Pause command not implemented")
                continue
            elif choice == "8":
                print("Resume command not implemented")
                continue
            elif choice == "9":
                print("Start therm_test command not implemented")
                command = {
                    "command": "start",
                    "task": "therm_test",
                    "params": {
                    }
                }
                send_command(command)
                continue
            elif choice == "1":
                # Pole Command (Default)
                command = {
                    "command": "start",
                    "task": "smoke_test",
                    "params": {
                        # "pole_length": 1150,
                        # "wait_time": 10,
                        # "unreachable_action": "continue"
                    }
                }
                send_command(command)
            elif choice == "2":
                try:
                    pole_length = int(input("Enter pole length (default 1150): ") or "1150")
                    wait_time = int(input("Enter wait time in seconds (default 10): ") or "10")
                    unreachable_action = input("Enter unreachable action (default continue): ")
                    unreachable_action = unreachable_action if unreachable_action in ['continue', 'stop'] else 'continue'
                    default_position_idx_bool = input("Enter if return position index or not (0 or 1, default 1): ")
                    default_position_idx_bool = bool(int(default_position_idx_bool)) if default_position_idx_bool in ['0', '1'] else False

                    default_position_idx = int(input("Enter default_position_idx (integer 0~9, default 0): ") or "0")
                    command = {
                        "command": "start",
                        "task": "smoke_test",
                        "params": {
                            "pole_length": pole_length,
                            "wait_time": wait_time,
                            "unreachable_action": unreachable_action,
                            "checkpoint_bool": default_position_idx_bool,
                            "checkpoint": default_position_idx
                        }
                    }
                    send_command(command)
                except ValueError:
                    print("Invalid input. Using default values.")
                    command = {
                        "command": "start",
                        "task": "smoke_test",
                        "params": {
                            # "pole_length": 1150,
                            # "wait_time": 10,
                            # "unreachable_action": "continue"
                        }
                    }
                    send_command(command)
                
            elif choice == "3":
                # Photo Command
                try:
                    # True: end when detect the detector immediately, False: Take photo for all positions
                    photo_direct_mode = input("Enter photo direct mode (0 or 1, default 0): ")
                    photo_direct_mode = bool(int(photo_direct_mode)) if photo_direct_mode in ['0', '1'] else False
                    command = {
                        "command": "start",
                        "task": "photo",
                        "params": {
                            "photo_direct_mode": photo_direct_mode
                        }
                    }
                    send_command(command)
                except ValueError:
                    print("Invalid input. Using default values.")
                    command = {
                        "command": "start",
                        "task": "photo",
                        "params": {
                            "photo_direct_mode": False
                        }
                    }
                    send_command(command)
                # send_command(command)
                
            elif choice == "4":
                # Control Toggle Command
                params_str = input("Enter control toggle parameters as JSON (optional): ")
                try:
                    if params_str:
                        parsed_input = json.loads(params_str)

                        # Check if user provided a complete command JSON
                        if isinstance(parsed_input, dict) and "command" in parsed_input and "task" in parsed_input:
                            # User provided complete command, send as-is
                            command = parsed_input
                        else:
                            raise ValueError('wrong input')
                            # User provided only parameters, wrap in command structure
                            # command = {
                            #     "command": "start",
                            #     "task": "control_toggle",
                            #     "params": parsed_input
                            # }
                    else:
                        # No parameters provided
                        command = {
                            "command": "start",
                            "task": "control_toggle",
                            "params": {}
                        }
                    send_command(command)
                except json.JSONDecodeError:
                    print("Invalid JSON. Sending without parameters.")
                    command = {
                        "command": "start",
                        "task": "control_toggle",
                        "params": {}
                    }
                    send_command(command)
                except Exception:
                    print("Invalid JSON. Sending without parameters.")
                    command = {
                        "command": "start",
                        "task": "control_toggle",
                        "params": {}
                    }
                    send_command(command)
                
            elif choice == "5":
                # Emergency Stop Command
                command = {
                    "command": "emergency_stop"
                }
                send_command(command)
                
            elif choice == "6":
                # Reset Command
                command = {
                    "command": "reset"
                }
                send_command(command)

            elif choice == "10":
                command = {
                    "command": "debug",
                    "task": "capture_current_view",
                    "params": {
                        "save_prefix": "debug_",
                        "detection_type": "button"
                    }
                }
                send_command(command)

            else:
                print("Invalid choice. Please try again.")
                
        except KeyboardInterrupt:
            server_exit = True
            print("\nExiting...")
            break


def resource_monitor_thread():
    """Thread to monitor and optimize resource usage"""
    global last_activity_time, server_exit

    while not stop_event.is_set():
        if server_exit:
            print('Received exit signal in resource monitor thread')
            break
        # If no active task and detection not paused, pause it to save resources
        if last_activity_time is not None and time.time() - last_activity_time > 1200:
            print(f"Exiting to save resources, since no active task for {(time.time() - last_activity_time)/60:.2f} minutes")
            server_exit = True
        time.sleep(5)


if __name__ == "__main__":
    # Get port from command line if provided
    port = 17852
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print(f"Invalid port number: {sys.argv[1]}. Using default port {port}.")

    monitor_thread = threading.Thread(target=resource_monitor_thread)
    monitor_thread.daemon = True
    monitor_thread.start()
    start_server(port)
