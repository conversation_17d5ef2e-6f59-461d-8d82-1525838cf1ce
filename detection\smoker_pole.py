import numpy as np
import cv2
import glob
# import matplotlib.pyplot as plt
# import matplotlib
# matplotlib.use("TkAgg")
import time
def pole_detection(image, display_process=False, verbose=False):
    """
    Detect red pole in the image.
    
    Args:
        image: Input BGR image
        display_process: Whether to display detection process
        verbose: Whether to print additional information
        
    Returns:
        Tuple of (pole_coordinates, display_image, pole_candidates)
    """
    display_img = image.copy()
    time1 = time.time()
    # Convert to HSV for better color filtering
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

    # Define color ranges for red (wraps around 0/180 in HSV)
    lower_red1 = np.array([0, 40, 45])
    upper_red1 = np.array([10, 255, 255])
    lower_red2 = np.array([160, 40, 45])
    upper_red2 = np.array([180, 255, 255])
    
    # Create masks for red color
    red_mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
    red_mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
    red_mask = cv2.bitwise_or(red_mask1, red_mask2)

    # Apply morphological operations to clean up the mask
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
    eroded_image = cv2.dilate(red_mask, kernel, iterations=1)
    thresh_2 = cv2.erode(eroded_image, kernel, iterations=1)
    
    # Find contours in the mask
    contours, _ = cv2.findContours(thresh_2, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
    pole_candidates = []
    
    # Process each contour
    for contour in contours:
        # Filter by contour area
        if cv2.contourArea(contour) < 20 or cv2.contourArea(contour) > 400:
            continue

        # Find enclosing circle
        (x, y), radius = cv2.minEnclosingCircle(contour)
        x, y, radius = int(x), int(y), int(radius)
        
        # Create masks for intersection calculation
        circle_mask = np.zeros_like(red_mask)
        cv2.circle(circle_mask, np.int_([x, y]), int(radius), 255, -1)
        
        contour_mask = np.zeros_like(red_mask)
        cv2.drawContours(contour_mask, [contour], -1, (255), thickness=cv2.FILLED)
        
        # Calculate intersection between contour and circle
        intersection = cv2.countNonZero(cv2.bitwise_and(contour_mask, circle_mask))
        circle_area = cv2.countNonZero(circle_mask)
        
        # Store candidate with metrics
        pole_candidates.append((x, y, radius, cv2.contourArea(contour), intersection / circle_area))

    # Sort candidates by quality score (intersection ratio * 1000 + area)
    pole_candidates.sort(key=lambda x: x[4] * 1000 + x[3], reverse=True)
    
    # Draw best candidate if requested
    if display_process and len(pole_candidates) > 0:
        cv2.circle(display_img, (pole_candidates[0][0], pole_candidates[0][1]), 
                  pole_candidates[0][2], (255, 0, 0), 2)
        if verbose:
            print(pole_candidates[0])

    print(f"pole:{int(1000*(time.time()-time1))}ms-{len(pole_candidates) > 0}")
    # Return best candidate if available
    if len(pole_candidates) > 0:
        return pole_candidates[0][:3], display_img, pole_candidates
    return None, display_img, pole_candidates


if __name__ == "__main__":
    # Test the pole detection on sample images
    paths = glob.glob(r'G:\Windows\PycharmProjects1\bot\data\a_smoker\labeling\poles\*')
    
    for path_idx, path in enumerate(paths[:]):
        print(path_idx, path)
        img = cv2.imread(path)
        pole_coord, _, pole_candidates = pole_detection(img, verbose=True)
        
        # Visualize results
        first_flag = True
        for pole_candidate in pole_candidates:
            if first_flag:
                cv2.circle(img, (pole_candidate[0], pole_candidate[1]), 
                          pole_candidate[2], (255, 0, 0), 2)
                print(pole_candidate)
                first_flag = False
            else:
                cv2.circle(img, (pole_candidate[0], pole_candidate[1]), 
                          pole_candidate[2], (0, 255, 255), 2)
                
        # plt.figure()
        # plt.imshow(img)
        # plt.show()

