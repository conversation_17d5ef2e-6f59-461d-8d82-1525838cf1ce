#!/usr/bin/env python3
"""
Test script to verify time module import optimization.
Tests various scenarios where time module might fail to be accessible.
"""

import sys
import os
import threading
import traceback
from typing import Dict, Any, Optional

# Add paths similar to button_processor.py
sys.path.append(os.path.join(os.path.dirname(__file__), 'buttonControl'))

# Global time import - this should be sufficient for all use cases
import time

class TimeImportTester:
    """Test class to verify time module accessibility in different contexts."""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = time.time()
        print(f"TimeImportTester initialized at {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    def test_basic_time_functions(self) -> Dict[str, Any]:
        """Test basic time functions without local import."""
        test_name = "basic_time_functions"
        try:
            # Test time.time()
            current_time = time.time()
            assert isinstance(current_time, float), "time.time() should return float"
            
            # Test time.sleep()
            sleep_start = time.time()
            time.sleep(0.1)
            sleep_end = time.time()
            sleep_duration = sleep_end - sleep_start
            assert 0.08 <= sleep_duration <= 0.15, f"sleep duration {sleep_duration} not in expected range"
            
            # Test time.strftime()
            formatted_time = time.strftime('%Y-%m-%d %H:%M:%S')
            assert isinstance(formatted_time, str), "time.strftime() should return string"
            assert len(formatted_time) == 19, "formatted time should be 19 characters"
            
            result = {
                'success': True,
                'current_time': current_time,
                'sleep_duration': sleep_duration,
                'formatted_time': formatted_time,
                'error': None
            }
            print(f"✓ {test_name}: All basic time functions work correctly")
            
        except Exception as e:
            result = {
                'success': False,
                'error': str(e),
                'traceback': traceback.format_exc()
            }
            print(f"✗ {test_name}: Failed with error: {e}")
        
        self.test_results[test_name] = result
        return result
    
    def test_time_in_nested_function(self) -> Dict[str, Any]:
        """Test time module access in nested function without local import."""
        test_name = "time_in_nested_function"
        
        def nested_function():
            # Should use global time import
            return time.time(), time.strftime('%H:%M:%S')
        
        try:
            timestamp, time_str = nested_function()
            assert isinstance(timestamp, float), "nested function time.time() should return float"
            assert isinstance(time_str, str), "nested function time.strftime() should return string"
            
            result = {
                'success': True,
                'timestamp': timestamp,
                'time_str': time_str,
                'error': None
            }
            print(f"✓ {test_name}: Time functions work in nested function")
            
        except Exception as e:
            result = {
                'success': False,
                'error': str(e),
                'traceback': traceback.format_exc()
            }
            print(f"✗ {test_name}: Failed with error: {e}")
        
        self.test_results[test_name] = result
        return result
    
    def test_time_in_try_except(self) -> Dict[str, Any]:
        """Test time module access in try/except blocks without local import."""
        test_name = "time_in_try_except"
        
        try:
            # Simulate complex try/except scenario like in button_processor
            try:
                start_time = time.time()
                time.sleep(0.05)
                end_time = time.time()
                duration = end_time - start_time
                
                if duration < 0.04:
                    raise ValueError("Sleep duration too short")
                
                result = {
                    'success': True,
                    'duration': duration,
                    'start_time': start_time,
                    'end_time': end_time,
                    'error': None
                }
                print(f"✓ {test_name}: Time functions work in try/except blocks")
                
            except ValueError as ve:
                # Even in exception handling, time should be accessible
                error_time = time.time()
                result = {
                    'success': False,
                    'error': str(ve),
                    'error_time': error_time,
                    'traceback': traceback.format_exc()
                }
                print(f"✗ {test_name}: ValueError but time still accessible: {ve}")
                
        except Exception as e:
            result = {
                'success': False,
                'error': str(e),
                'traceback': traceback.format_exc()
            }
            print(f"✗ {test_name}: Failed with error: {e}")
        
        self.test_results[test_name] = result
        return result
    
    def test_time_in_thread(self) -> Dict[str, Any]:
        """Test time module access in threading context without local import."""
        test_name = "time_in_thread"
        thread_result = {'success': False, 'error': None}
        
        def thread_function():
            try:
                # Should use global time import
                thread_start = time.time()
                time.sleep(0.1)
                thread_end = time.time()
                thread_duration = thread_end - thread_start
                
                thread_result.update({
                    'success': True,
                    'thread_start': thread_start,
                    'thread_end': thread_end,
                    'thread_duration': thread_duration,
                    'error': None
                })
                
            except Exception as e:
                thread_result.update({
                    'success': False,
                    'error': str(e),
                    'traceback': traceback.format_exc()
                })
        
        try:
            thread = threading.Thread(target=thread_function)
            thread.start()
            thread.join(timeout=2.0)  # Wait max 2 seconds
            
            if thread.is_alive():
                thread_result.update({
                    'success': False,
                    'error': 'Thread timeout'
                })
                print(f"✗ {test_name}: Thread timed out")
            elif thread_result['success']:
                print(f"✓ {test_name}: Time functions work in thread")
            else:
                print(f"✗ {test_name}: Failed in thread: {thread_result['error']}")
                
        except Exception as e:
            thread_result.update({
                'success': False,
                'error': str(e),
                'traceback': traceback.format_exc()
            })
            print(f"✗ {test_name}: Failed with error: {e}")
        
        self.test_results[test_name] = thread_result
        return thread_result
    
    def test_time_with_exec(self) -> Dict[str, Any]:
        """Test time module access with exec() without local import."""
        test_name = "time_with_exec"
        
        try:
            # Create a namespace that includes time
            exec_namespace = {'time': time}
            
            # Execute code that uses time
            exec_code = """
result = {
    'exec_time': time.time(),
    'exec_sleep_test': True
}
time.sleep(0.01)
result['exec_after_sleep'] = time.time()
"""
            
            exec(exec_code, exec_namespace)
            exec_result = exec_namespace['result']
            
            result = {
                'success': True,
                'exec_result': exec_result,
                'error': None
            }
            print(f"✓ {test_name}: Time functions work with exec()")
            
        except Exception as e:
            result = {
                'success': False,
                'error': str(e),
                'traceback': traceback.format_exc()
            }
            print(f"✗ {test_name}: Failed with error: {e}")
        
        self.test_results[test_name] = result
        return result
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all time import tests."""
        print("=" * 60)
        print("Running Time Import Tests")
        print("=" * 60)
        
        test_methods = [
            self.test_basic_time_functions,
            self.test_time_in_nested_function,
            self.test_time_in_try_except,
            self.test_time_in_thread,
            self.test_time_with_exec
        ]
        
        for test_method in test_methods:
            print(f"\nRunning {test_method.__name__}...")
            test_method()
        
        # Summary
        print("\n" + "=" * 60)
        print("Test Summary")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"Total tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        
        if failed_tests > 0:
            print("\nFailed tests:")
            for test_name, result in self.test_results.items():
                if not result['success']:
                    print(f"  - {test_name}: {result['error']}")
        
        total_duration = time.time() - self.start_time
        print(f"\nTotal test duration: {total_duration:.3f} seconds")
        
        return {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': failed_tests,
            'test_results': self.test_results,
            'total_duration': total_duration
        }

def main():
    """Main test function."""
    tester = TimeImportTester()
    results = tester.run_all_tests()
    
    # Return appropriate exit code
    if results['failed_tests'] == 0:
        print("\n✓ All tests passed! Time import optimization is safe.")
        return 0
    else:
        print(f"\n✗ {results['failed_tests']} tests failed. Time import optimization may have issues.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
