# Camera-to-end-effector transformation
camera_rotation_matrix:
  - [-0.03914598, -0.99869253, -0.03287572]
  - [0.99923066, -0.0392032, 0.00109761]
  - [-0.00238501, -0.03280746, 0.99945884]
camera_translation_vector: [0.08695189, -0.04231192, 0.00636331]

# Robot movement parameters
ep_max_len: 0.965  # 0.965
gravity_offset_l: 0.004
deadband_error: 1

# Handler calibration parameters
calib_rotate_bias: 1.8  # 1.8435
calib_desired_coord: [370, 399]

# Detection parameters
record_len: 12
min_detector_x: 60
min_detector_y: 100
min_detector_y_single: 400
detector_model_path: 'model/detector_6.onnx'
detector_conf_threshold: 0.73

# Pole Operation parameters
wait_time: 15
pole_length_offset: 150  # 160
max_space_distance: 955
approach_distance_ratio: 0.25
max_joint_distance_limit: [4, 16]
closer_speed_ratio: 1.333
distance_thresh: [10, 8.5]
detector_height: 40
finish_count_max: 2.8

# Raising heights (relative to prepared_height)
raising_height_grab: 0.055
raising_height_intermediate: 0.09 # 0.100
raising_height_outside: 0.160
raising_height_above: 0.235

# Joint position parameters
prepare_joints:
  zero: [2.103,75.533,-163.534,-7.251,163.381,-188.2]
  mid1: [-68.046,41.14,-88.29,4.985,57.12,-150.1]
  mid2: [-58.083,-6.031,116.647,-7.424,-113.524,2.418]
  mid3: [-41.886,59.685,83.682,0.966,-146.005,-39.81]
  prepared: [-30.767,64.664,63.936,-0.002,-128.582,-57.708]

safe_700_joint: [-30, -32.608, 98.863, 0, -66.255, -55.829]
vertical_joint: [0.0, -7.0, 17.7, 0.0, -10.6, 0.0]

# Joint restrictions
max_joint_restrictions: [178, 178, 145, 178, 178, 358]
min_joint_restrictions: [-178, -178, -178, -178, -178, -358]

# Start positions
default_observe_idx: 0
start_positions:
  pos0: [-30.01, -6.858, 73.697, -0.001, -66.839, -55.829]
  pos1: [-60.01, -14.418, 80.15, 0.001, -65.727, -55.829]
  pos2: [-90.01, -14.418, 80.15, 0.001, -65.727, -55.829]
  pos3: [-120.01, -14.418, 80.15, 0.001, -65.727, -55.829]
  pos4: [-150.01, -14.418, 80.15, 0.001, -65.727, -55.829]
  pos5: [0.01, -14.418, 80.15, 0.001, -65.727, -55.829]
  pos6: [30.432, -14.418, 80.15, 0.001, -65.727, -55.829]
  pos7: [60.01, -14.418, 80.15, 0.001, -65.727, -55.829]
  pos8: [90.01, -14.418, 80.15, 0.001, -65.727, -55.829]
  pos9: [120.377, -14.418, 80.15, 0.001, -65.727, -55.829]

# Default action when detector is unreachable
unreachable_action: 'continue'  # 'stop' or 'continue'

