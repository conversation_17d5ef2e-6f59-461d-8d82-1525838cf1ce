#!/usr/bin/env python3
"""
测试各模块中buttons数据顺序的一致性
验证button_detection.py, data_manager.py, button_action.py, button_processor.py中的顺序是否一致
"""

import sys
import os
import numpy as np
from typing import List, Dict, Any

# Add paths
sys.path.append(os.path.join(os.path.dirname(__file__), 'buttonControl'))

def test_button_order_consistency():
    """测试按钮顺序一致性"""
    
    print("=" * 60)
    print("测试按钮数据顺序一致性")
    print("=" * 60)
    
    # 1. 测试button_detection.py的返回格式
    print("\n1. button_detection.py 返回格式:")
    mock_detection_result = {
        'top_left_button': (100, 50, 10),      # 第0个位置
        'top_right_button': (200, 50, 10),     # 第1个位置
        'bottom_left_button': (100, 150, 10),  # 第2个位置
        'bottom_right_button': (200, 150, 10), # 第3个位置
        'knob': (150, 100, 8),
        'handle_angle': 45.0
    }
    
    print("  标准格式:")
    for key, value in mock_detection_result.items():
        if 'button' in key:
            print(f"    {key}: {value}")
    
    # 2. 测试data_manager.py的button_mappings顺序
    print("\n2. data_manager.py button_mappings 顺序:")
    
    # 模拟data_manager中的button_mappings
    button_mappings = [
        ('bottom_left_button_camera_final', 'bottom_left'),      # 第0个
        ('bottom_right_button_camera_final', 'bottom_right'),    # 第1个  
        ('top_left_button_camera_final', 'top_left'),            # 第2个
        ('top_right_button_camera_final', 'top_right')           # 第3个
    ]
    
    print("  button_mappings顺序:")
    for i, (field_name, label) in enumerate(button_mappings):
        print(f"    索引{i}: {label} <- {field_name}")
    
    # 3. 测试button_action.py的build_object_coordinate_system参数顺序
    print("\n3. button_action.py build_object_coordinate_system 参数顺序:")
    print("  参数顺序: bottom_left, bottom_right, top_left, top_right")
    print("    button_1: bottom_left  (索引0)")
    print("    button_2: bottom_right (索引1)")
    print("    button_3: top_left     (索引2)")
    print("    button_4: top_right    (索引3)")
    
    # 4. 测试button_processor.py的处理顺序
    print("\n4. button_processor.py 处理顺序:")
    print("  button_processor中的顺序:")
    processing_order = [
        ("bottom_left", 0),
        ("bottom_right", 1), 
        ("top_left", 2),
        ("top_right", 3)
    ]
    
    for label, index in processing_order:
        print(f"    索引{index}: {label}")
    
    # 5. 验证一致性
    print("\n5. 一致性验证:")
    
    # 从button_detection.py格式转换为data_manager.py的button_coords列表
    detection_buttons = [
        mock_detection_result['top_left_button'],
        mock_detection_result['top_right_button'],
        mock_detection_result['bottom_left_button'],
        mock_detection_result['bottom_right_button']
    ]
    
    # 按照data_manager.py的button_mappings顺序重新排列
    data_manager_order = [
        mock_detection_result['bottom_left_button'],   # 索引0: bottom_left
        mock_detection_result['bottom_right_button'],  # 索引1: bottom_right
        mock_detection_result['top_left_button'],      # 索引2: top_left
        mock_detection_result['top_right_button']      # 索引3: top_right
    ]
    
    # 按照button_action.py的参数顺序
    button_action_order = [
        mock_detection_result['bottom_left_button'],   # button_1: bottom_left
        mock_detection_result['bottom_right_button'],  # button_2: bottom_right
        mock_detection_result['top_left_button'],      # button_3: top_left
        mock_detection_result['top_right_button']      # button_4: top_right
    ]
    
    print("  data_manager.py顺序与button_action.py顺序比较:")
    consistency_check = True
    for i in range(4):
        dm_button = data_manager_order[i]
        ba_button = button_action_order[i]
        is_same = dm_button == ba_button
        consistency_check = consistency_check and is_same
        
        print(f"    索引{i}: {dm_button} == {ba_button} -> {'✓' if is_same else '✗'}")
    
    print(f"\n  整体一致性: {'✓ 一致' if consistency_check else '✗ 不一致'}")
    
    # 6. 测试实际的数据流转换
    print("\n6. 数据流转换测试:")
    
    def simulate_data_flow():
        """模拟实际的数据流转换"""
        
        # Step 1: button_detection.py返回的格式
        detection_result = mock_detection_result
        print("  Step 1 - button_detection.py返回:")
        print(f"    {detection_result}")
        
        # Step 2: data_manager.py处理后的button_coords
        button_coords = []
        button_labels = []
        
        for field_name, label in button_mappings:
            # 模拟从detection_result中提取对应的按钮
            button_key = field_name.replace('_camera_final', '')
            if button_key in detection_result:
                button_coords.append(detection_result[button_key])
                button_labels.append(label)
        
        print("  Step 2 - data_manager.py处理后:")
        print(f"    button_coords: {button_coords}")
        print(f"    button_labels: {button_labels}")
        
        # Step 3: button_action.py接收的参数
        if len(button_coords) >= 4:
            print("  Step 3 - button_action.py接收参数:")
            print(f"    button_1 (bottom_left):  {button_coords[0]}")
            print(f"    button_2 (bottom_right): {button_coords[1]}")
            print(f"    button_3 (top_left):     {button_coords[2]}")
            print(f"    button_4 (top_right):    {button_coords[3]}")
            
            # 验证是否正确
            expected_order = [
                mock_detection_result['bottom_left_button'],
                mock_detection_result['bottom_right_button'],
                mock_detection_result['top_left_button'],
                mock_detection_result['top_right_button']
            ]
            
            is_correct = button_coords == expected_order
            print(f"    数据流转换正确性: {'✓ 正确' if is_correct else '✗ 错误'}")
            
            return is_correct
        else:
            print("    错误: 按钮数量不足")
            return False
    
    data_flow_correct = simulate_data_flow()
    
    # 7. 总结
    print("\n" + "=" * 60)
    print("总结")
    print("=" * 60)
    
    print("各模块的按钮顺序:")
    print("  button_detection.py:  top_left, top_right, bottom_left, bottom_right (字典格式)")
    print("  data_manager.py:      bottom_left, bottom_right, top_left, top_right (列表格式)")
    print("  button_action.py:     bottom_left, bottom_right, top_left, top_right (参数格式)")
    print("  button_processor.py:  bottom_left, bottom_right, top_left, top_right (处理格式)")
    
    overall_consistency = consistency_check and data_flow_correct
    
    if overall_consistency:
        print("\n✅ 所有模块的按钮顺序已保持一致!")
        print("✅ 数据流转换正确!")
        print("✅ 不存在顺序不一致问题!")
    else:
        print("\n❌ 发现顺序不一致问题!")
        print("❌ 需要进一步检查和修复!")
    
    return overall_consistency

def test_coordinate_system_mapping():
    """测试坐标系映射的正确性"""
    
    print("\n" + "=" * 60)
    print("坐标系映射测试")
    print("=" * 60)
    
    # 模拟一个标准的按钮布局
    print("标准按钮布局 (相机视角):")
    print("  top_left     top_right")
    print("     (100,50)    (200,50)")
    print("       |           |")
    print("  bottom_left  bottom_right") 
    print("     (100,150)   (200,150)")
    
    # 测试build_object_coordinate_system的参数映射
    print("\nbuild_object_coordinate_system参数映射:")
    print("  button_1 (bottom_left):  (100, 150) -> 用于建立X轴")
    print("  button_2 (bottom_right): (200, 150) -> 用于建立X轴")
    print("  button_3 (top_left):     (100, 50)  -> 用于建立Y轴")
    print("  button_4 (top_right):    (200, 50)  -> 用于建立Y轴")
    
    print("\n坐标系建立逻辑:")
    print("  X轴: bottom_right - bottom_left = (200,150) - (100,150) = (100,0)")
    print("  Y轴: top_left - bottom_left = (100,50) - (100,150) = (0,-100)")
    print("  Z轴: X轴 × Y轴 (右手坐标系)")
    
    return True

def main():
    """主测试函数"""
    
    try:
        print("开始按钮顺序一致性测试...")
        
        # 测试顺序一致性
        consistency_result = test_button_order_consistency()
        
        # 测试坐标系映射
        mapping_result = test_coordinate_system_mapping()
        
        # 总体结果
        print("\n" + "=" * 60)
        print("最终测试结果")
        print("=" * 60)
        
        if consistency_result and mapping_result:
            print("✅ 所有测试通过!")
            print("✅ 按钮顺序一致性正确!")
            print("✅ 坐标系映射正确!")
            return 0
        else:
            print("❌ 测试失败!")
            print("❌ 存在顺序不一致或映射错误!")
            return 1
            
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
