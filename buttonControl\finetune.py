"""
Fine-tuning module for RoboArm project.
Handles fine-tuning operations to correct small positioning errors during button/knob operations.
"""

import time
import numpy as np
import pyrealsense2 as rs
from typing import Tuple, Optional, Dict, Any
from scipy.spatial.transform import Rotation as R
from datetime import datetime
from .coord_ops import robust_depth_estimation

# Import custom exceptions
from .exceptions import (
    ButtonDetectionError, FinetuneError, EmergencyStopError,
    ArmStateError, ArmMovementError
)


class FineTuner:
    """
    Fine-tuning class for precise positioning of robot arm
    """
    
    def __init__(self, controller):
        """
        Initialize fine-tuner with controller reference
        
        Args:
            controller: RealTimeButtonControl instance
        """
        self.controller = controller
        self.robot_config = controller.robot_config
        self.arm = controller.arm
        
        # Get template coordinates from config
        processing_settings = self.robot_config.get_processing_settings()
        self.template_coords = {
            'left_button': np.array(processing_settings['left_button_ideal_xyz_cam']),
            'right_button': np.array(processing_settings['right_button_ideal_xyz_cam']),
            'knob_center': np.array(processing_settings['knob_center_ideal_xyz_cam']),
            'knob_left': np.array(processing_settings['knob_left_ideal_xyz_cam']),
            'knob_right': np.array(processing_settings['knob_right_ideal_xyz_cam'])
        }
        self.threshold = processing_settings['finetune_threshold']
        self.max_iterations = processing_settings['finetune_max_iterations']
        
    def get_target_type_from_status(self, status: str) -> Optional[str]:
        """
        Extract target type from touching or finetune status
        
        Args:
            status: Current status string
            
        Returns:
            Target type ('left_button', 'right_button', 'knob_center', 'knob_left', 'knob_right') or None
        """
        if status == 'touching_left_button' or status == 'finetune_left':
            return 'left_button'
        elif status == 'touching_right_button' or status == 'finetune_right':
            return 'right_button'
        elif status.startswith('touching_knob_') or status.startswith('finetune_knob_'):
            # 支持touching_knob_center/left/right
            if 'center' in status:
                return 'knob_center'
            elif 'left' in status:
                return 'knob_left'
            elif 'right' in status:
                return 'knob_right'
            else:
                return 'knob_center'  # 默认center
        elif status == 'touching_knob' or status == 'finetune_knob':
            return 'knob_center'
        else:
            return None
    
    def detect_current_target_position(self, target_type: str) -> Optional[np.ndarray]:
        """
        通过data_manager获取目标位置
        
        Args:
            target_type: 目标类型
            
        Returns:
            目标位置坐标或None
        """
        try:
            # 通过data_manager获取数据
            if not hasattr(self.controller, 'data_manager') or self.controller.data_manager is None:
                print("Warning: No data_manager available for finetune detection")
                return None
            
            data = self.controller.data_manager.collect_data()
            
            if not data:
                return None
            
            # 根据target_type获取对应的检测结果
            target_mapping = {
                'left_button': 'bottom_left_button_camera_final',
                'right_button': 'bottom_right_button_camera_final',
                'knob_center': 'knob_camera_final',
                'knob_left': 'knob_camera_final',
                'knob_right': 'knob_camera_final'
            }
            
            target_key = target_mapping.get(target_type)
            if target_key and data.get(target_key) is not None:
                return np.array(data[target_key])
            
            return None
            
        except Exception as e:
            print(f"Error detecting target position: {e}")
            return None
    
    def calculate_position_error(self, current_pos: np.ndarray, target_type: str) -> Tuple[np.ndarray, float]:
        """
        Calculate position error vector and max error
        
        Args:
            current_pos: Current detected position in camera coordinates [x, y, z]
            target_type: Type of target ('left_button', 'right_button', 'knob_center', 'knob_left', 'knob_right')
            
        Returns:
            Tuple of (error_vector, max_error)
        """
        template_pos = self.template_coords[target_type]
        error_vector = current_pos - template_pos
        max_error = np.max(np.abs(error_vector))
        print('debug | error vector: ', error_vector)
        print('debug | current pose: ', self.controller.arm.rm_get_current_arm_state()[1]['pose'])
        
        return error_vector, max_error
    
    def perform_single_finetune_step(self, target_type: str, multi_frame_observation: Dict[str, Any] = None) -> Tuple[bool, str]:
        """
        Perform one fine-tuning step using saved coordinate axes from observing phase
        
        Args:
            target_type: Type of target ('left_button', 'right_button', 'knob_center', 'knob_left', 'knob_right')
            multi_frame_observation: Multi-frame observation result containing avg_displacement
            
        Returns:
            Tuple of (success, status_message)
        """
        # Check if coordinate axes are available from observing phase
        time.sleep(0.5)
        if self.controller.last_coordinate_axes is None:
            raise FinetuneError("No coordinate axes available from observing phase")
        
        # Get saved coordinate axes from observing phase (相机坐标系下的轴方向)
        x_axis_camera, y_axis_camera, z_axis_camera = self.controller.last_coordinate_axes
        
        print(f"Using saved coordinate axes from observing phase:")
        print(f"  X-axis (camera): {x_axis_camera}")
        print(f"  Y-axis (camera): {y_axis_camera}")
        print(f"  Z-axis (camera): {z_axis_camera}")
        
        # Use average displacement from multi-frame observation if provided
        if multi_frame_observation and 'avg_displacement' in multi_frame_observation:
            avg_displacement_camera = multi_frame_observation['avg_displacement']
            displacement_magnitude = multi_frame_observation['avg_displacement_magnitude']
            print(f"Using average displacement from multi-frame observation:")
            print(f"  Average displacement (camera): {avg_displacement_camera}")
            print(f"  Displacement magnitude: {displacement_magnitude:.4f} m ({displacement_magnitude*1000:.2f} mm)")
            
            # The displacement vector is already from current to ideal position
            # We need the target movement vector (what the target needs to move)
            target_movement_camera = avg_displacement_camera
            
        else:
            # Fallback: detect current position and calculate displacement
            current_pos_camera = self.detect_current_target_position(target_type)
            if current_pos_camera is None:
                raise ButtonDetectionError(f"Failed to detect {target_type} target position")
            
            # Calculate position error in camera coordinates
            template_pos_camera = self.template_coords[target_type]
            error_vector, max_error = self.calculate_position_error(current_pos_camera, target_type)
            
            print(f"Current position (camera): {current_pos_camera}")
            print(f"Template position (camera): {template_pos_camera}")
            print(f"Error vector (camera): {error_vector}")
            print(f"Max error: {max_error:.4f} m ({max_error*1000:.2f} mm)")
            
            # Calculate target movement vector (from current to ideal)
            target_movement_camera = template_pos_camera - current_pos_camera
        
        # Get current robot pose
        ret_msg = self.arm.rm_get_current_arm_state()
        ret_msg = self.arm.rm_get_current_arm_state()
        if ret_msg[0] != 0:
            raise ArmStateError("Failed to get current arm state")
        
        current_pose = ret_msg[1]['pose']
        current_position_base = np.array(current_pose[:3])  # 当前基坐标系位置
        current_orientation = current_pose[3:]  # 当前姿态
        
        print(f"Current robot pose: {current_pose}")
        print(f"Current position (base): {current_position_base}")
        
        # 新方法：直接将相机坐标系下的位移向量变换到基坐标系
        from coord_ops import coordinate_transformer
        
        # Build camera to base transformation matrix for vector transformation
        position = current_pose[:3]
        orientation = R.from_euler('xyz', current_pose[3:], degrees=False).as_matrix()
        T_effector_to_base = np.eye(4)
        T_effector_to_base[:3, :3] = orientation
        T_effector_to_base[:3, 3] = position
        
        # Get transformation matrix from coordinate_transformer
        T_camera_to_base = T_effector_to_base @ coordinate_transformer.T_camera_to_effector
        
        # Extract rotation matrix for vector transformation (no translation needed for vectors)
        rotation_camera_to_base = T_camera_to_base[:3, :3]
        
        # Transform target movement vector from camera to base coordinates
        target_movement_base = rotation_camera_to_base @ target_movement_camera
        
        # 机械臂需要移动的向量 (相反方向，因为机械臂移动和目标移动方向相反)
        robot_movement_base = -target_movement_base
        
        print(f"Target movement vector (camera): {target_movement_camera}")
        print(f"Target movement vector (base): {target_movement_base}")
        print(f"Robot movement vector (base): {robot_movement_base}")
        print(f"Movement magnitude (base): {np.linalg.norm(robot_movement_base)*1000:.2f} mm")
        
        # Step 4: 在基坐标系下应用修正
        # 使用增量修正：机械臂当前位置 + 机械臂需要移动的向量
        target_position_base = current_position_base + robot_movement_base
        
        print(f"Target robot position (base): {target_position_base}")
        
        # 验证修正幅度是否合理
        movement_magnitude = np.linalg.norm(robot_movement_base)
        print(f"Movement magnitude: {movement_magnitude*1000:.2f} mm")
        
        # 安全检查：如果修正幅度太大，可能有问题
        if movement_magnitude > 0.05:  # 超过50mm
            print(f"Warning: Large correction movement ({movement_magnitude*1000:.1f} mm). This might indicate a problem.")
            print("Possible issues:")
            print("  - Coordinate system mismatch")
            print("  - Template position defined incorrectly") 
            print("  - Detection error")
            # 仍然继续，但给出警告
        
        # Step 5: 构建目标姿态 (保持当前姿态，只改变位置)
        target_pose = np.concatenate([target_position_base, current_orientation])

        print(f"Target pose for movement: {target_pose}")

        # Execute movement using movel (小幅度移动)
        movement = self.robot_config.get_movement_parameters()
        print(f"Executing fine-tune movement with parameters: v={movement['v']}, r=0")

        # --- 与 button_action.py 一致的安全包装 ---
        try:
            if hasattr(self.controller, 'move_arm') and callable(getattr(self.controller, 'move_arm')):
                print('using wrapped move_arm to move to target_pose')
                result = self.controller.move_arm(
                    'rm_movel',
                    (target_pose, int(movement['v']/4), 0, movement['connect'], movement['block'])
                )
                if result == 4001:  # EMERGENCY_STOP_ERROR
                    raise EmergencyStopError("Emergency stop triggered during fine-tune movement")
                elif result != 0:
                    raise ArmMovementError(f"Fine-tune movement failed, error code: {result}")
            else:
                print('using direct robot call to move to target_pose')
                result = self.arm.rm_movel(
                    target_pose,
                    v=int(movement['v']/4),
                    r=0,
                    connect=movement['connect'],
                    block=movement['block']
                )
                if result != 0:
                    raise ArmMovementError(f"Fine-tune movement failed, error code: {result}")
            print(f'Result robot position: {self.controller.arm.rm_get_current_arm_state()[1]["pose"]}')

        except Exception as e:
            return False, str(e)

        print(f"Fine-tune step completed successfully for {target_type}")
        return True, f"Movement completed, magnitude: {movement_magnitude*1000:.2f}mm"
    
    def verify_finetune_result(self, target_type: str) -> Dict[str, Any]:
        """
        验证微调结果：检测当前位置与理想位置的差异
        
        Args:
            target_type: 目标类型 ('left_button', 'right_button', 'knob_center', 'knob_left', 'knob_right')
            
        Returns:
            验证结果字典
        """
        print(f"\n=== 验证微调结果 for {target_type} ===")
        
        # 等待机械臂稳定 - 优化：减少等待时间
        time.sleep(0.5)  # 从1.0秒减少到0.5秒
        
        # 检测当前位置
        current_pos = self.detect_current_target_position(target_type)
        if current_pos is None:
            return {
                'success': False,
                'error': 'Cannot detect target position for verification'
            }
        
        # 计算误差
        error_vector, max_error = self.calculate_position_error(current_pos, target_type)
        within_threshold = max_error < self.threshold
        
        print(f"验证结果:")
        print(f"  当前位置 (camera): {current_pos}")
        print(f"  理想位置 (camera): {self.template_coords[target_type]}")
        print(f"  误差向量 (camera): {error_vector}")
        print(f"  最大误差: {max_error:.4f} m ({max_error*1000:.2f} mm)")
        print(f"  是否达标: {'[Perfect]' if within_threshold else '[Not perfect]'}")
        print(f"  阈值: {self.threshold:.4f} m ({self.threshold*1000:.2f} mm)")
        
        return {
            'success': True,
            'current_position': current_pos,
            'template_position': self.template_coords[target_type],
            'error_vector': error_vector,
            'max_error': max_error,
            'max_error_mm': max_error * 1000,
            'within_threshold': within_threshold,
            'threshold': self.threshold,
            'threshold_mm': self.threshold * 1000
        }
    
    def multi_frame_observation(self, target_type: str, frame_count: int = None) -> Dict[str, Any]:
        """
        多帧观察微调结果，参考multiframe_processor的逻辑

        Args:
            target_type: 目标类型 ('left_button', 'right_button', 'knob_center', 'knob_left', 'knob_right')
            frame_count: 观察帧数，如果为None则使用配置文件中的值

        Returns:
            观察结果字典包含统计信息和最坏情况数据
        """
        # Get frame_count from config if not provided
        if frame_count is None:
            processing_settings = self.controller.robot_config.get_processing_settings()
            frame_count = processing_settings.get('frame_count', 10)
        if 'button' in target_type:
            frame_count//=2
        print(f"\n=== 多帧观察 {target_type} 目标 ({frame_count} 帧) ===")

        # 等待机械臂稳定 - 优化：减少等待时间
        time.sleep(0.3)  # 从0.5秒减少到0.3秒

        # 优化：尝试使用批量数据收集减少重复的帧获取
        try:
            # 使用DataManager的multi_frame模式一次性收集所有帧
            print(f"  Using optimized batch data collection for {frame_count} frames...")
            batch_data = self.controller.data_manager.collect_data(frame_count=frame_count)
            print(f"debug | batch_data: {batch_data}")
            if batch_data and batch_data.get('valid_frames', 0) > 0:
                # 使用批量处理的结果
                return self._process_batch_observation_data(batch_data, target_type, frame_count)
            else:
                print("  Batch processing not available, using frame-by-frame collection...")
        except Exception as e:
            print(f"  Batch processing failed ({e}), falling back to frame-by-frame...")

        # 回退到逐帧处理
        # 存储每帧的检测结果
        frame_results = []
        ready_count = 0
        far_count = 0
        failed_count = 0

        for frame_idx in range(frame_count):
            try:
                # 获取当前帧的检测结果
                current_pos = self.detect_current_target_position(target_type)
                
                if current_pos is not None:
                    error_vector, max_error = self.calculate_position_error(current_pos, target_type)
                    within_threshold = max_error < self.threshold
                    
                    # 分类状态
                    if within_threshold:
                        status = "Ready"
                        ready_count += 1
                    elif max_error < self.threshold * 2:
                        status = "Adjust"  # 中间状态，不计入Ready或Far
                    else:
                        status = "Far"
                        far_count += 1
                    
                    frame_result = {
                        'frame_idx': frame_idx,
                        'success': True,
                        'current_position': current_pos,
                        'max_error': max_error,
                        'max_error_mm': max_error * 1000,
                        'within_threshold': within_threshold,
                        'status': status
                    }
                    
                    print(f"  Frame {frame_idx:2d}: [{status:6s}] Error: {max_error*1000:.2f}mm")
                
                else:
                    failed_count += 1
                    frame_result = {
                        'frame_idx': frame_idx,
                        'success': False,
                        'status': 'Failed'
                    }
                    print(f"  Frame {frame_idx:2d}: [Failed] Detection failed")
                
                frame_results.append(frame_result)
                
                # 短暂延迟以确保帧间独立性 - 优化：减少延迟
                time.sleep(0.1)  # 从0.1秒减少到0.05秒
                
            except Exception as e:
                failed_count += 1
                frame_result = {
                    'frame_idx': frame_idx,
                    'success': False,
                    'status': 'Error',
                    'error': str(e)
                }
                frame_results.append(frame_result)
                print(f"  Frame {frame_idx:2d}: [Error ] {str(e)[:50]}")
        
        # 计算统计信息
        successful_frames = [f for f in frame_results if f['success']]
        
        if len(successful_frames) > 0:
            # 找到最大误差的帧（用于决定下一步finetune）
            max_error_frame = max(successful_frames, key=lambda f: f.get('max_error', 0))
            
            # 计算平均误差和标准差
            errors = [f['max_error'] for f in successful_frames]
            avg_error = np.mean(errors)
            std_error = np.std(errors)
            
            # 计算每帧的位移向量，用于平均位移计算
            template_pos = self.template_coords[target_type]
            displacement_vectors = []
            for frame in successful_frames:
                current_pos = frame['current_position']
                # 计算当前位置到理想位置的位移向量（相机坐标系）
                displacement = template_pos - current_pos
                displacement_vectors.append(displacement)
            
            # 计算平均位移向量
            if len(displacement_vectors) > 0:
                avg_displacement = np.mean(displacement_vectors, axis=0)
                avg_displacement_magnitude = np.linalg.norm(avg_displacement)
            else:
                avg_displacement = np.array([0, 0, 0])
                avg_displacement_magnitude = 0
            
            # 决定是否停止微调
            should_stop = ready_count > far_count
            
        else:
            max_error_frame = None
            avg_error = float('inf')
            std_error = float('inf')
            avg_displacement = np.array([0, 0, 0])
            avg_displacement_magnitude = 0
            should_stop = False
        
        # 输出统计结果
        print(f"\n多帧观察统计结果:")
        print(f"  Ready 帧数: {ready_count}")
        print(f"  Far 帧数: {far_count}")
        print(f"  Failed 帧数: {failed_count}")
        print(f"  成功帧数: {len(successful_frames)}/{frame_count}")
        if len(successful_frames) > 0:
            print(f"  平均误差: {avg_error*1000:.2f}mm")
            print(f"  误差标准差: {std_error*1000:.2f}mm")
            print(f"  最大误差: {max_error_frame['max_error']*1000:.2f}mm (Frame {max_error_frame['frame_idx']})")
            print(f"  平均位移向量: [{avg_displacement[0]*1000:.2f}, {avg_displacement[1]*1000:.2f}, {avg_displacement[2]*1000:.2f}]mm")
            print(f"  平均位移幅度: {avg_displacement_magnitude*1000:.2f}mm")
        print(f"  建议停止微调: {'[Perfect]' if should_stop else '[Not perfect]'}")
        
        return {
            'should_stop': should_stop,
            'ready_count': ready_count,
            'far_count': far_count,
            'failed_count': failed_count,
            'successful_frames': len(successful_frames),
            'total_frames': frame_count,
            'avg_error': avg_error,
            'std_error': std_error,
            'max_error_frame': max_error_frame,
            'avg_displacement': avg_displacement,
            'avg_displacement_magnitude': avg_displacement_magnitude,
            'frame_results': frame_results
        }

    def _process_batch_observation_data(self, batch_data: Dict[str, Any], target_type: str, frame_count: int) -> Dict[str, Any]:
        """
        处理批量观察数据，优化版本避免重复的帧获取

        Args:
            batch_data: 从DataManager获取的批量数据
            target_type: 目标类型
            frame_count: 观察帧数

        Returns:
            观察结果字典
        """
        print(f"  Processing batch observation data for {target_type}...")

        # 从批量数据中提取目标位置
        current_pos = self._extract_target_position_from_batch(batch_data, target_type)

        if current_pos is not None:
            # 计算误差
            error_vector, max_error = self.calculate_position_error(current_pos, target_type)
            within_threshold = max_error < self.threshold

            # 分类状态
            if within_threshold:
                status = "Ready"
                ready_count = frame_count  # 批量处理认为所有帧都是相同状态
                far_count = 0
            elif max_error < self.threshold * 2:
                status = "Adjust"
                ready_count = 0
                far_count = 0
            else:
                status = "Far"
                ready_count = 0
                far_count = frame_count

            print(f"  Batch result: [{status:6s}] Error: {max_error*1000:.2f}mm (averaged over {frame_count} frames)")

            # 创建统一的帧结果（所有帧使用相同的平均结果）
            frame_results = []
            for frame_idx in range(frame_count):
                frame_result = {
                    'frame_idx': frame_idx,
                    'success': True,
                    'current_position': current_pos,
                    'max_error': max_error,
                    'max_error_mm': max_error * 1000,
                    'within_threshold': within_threshold,
                    'status': status
                }
                frame_results.append(frame_result)

            # 计算平均位移（用于下一步finetune）
            template_pos = self.template_coords[target_type]
            displacement = template_pos - current_pos

            return {
                'should_stop': within_threshold,
                'ready_count': ready_count,
                'far_count': far_count,
                'failed_count': 0,
                'successful_frames': frame_count,
                'total_frames': frame_count,
                'avg_error': max_error,
                'std_error': 0.0,  # 批量处理没有标准差
                'max_error_frame': frame_results[0] if frame_results else None,
                'avg_displacement': displacement,
                'avg_displacement_magnitude': np.linalg.norm(displacement),
                'frame_results': frame_results
            }
        else:
            # 检测失败
            print(f"  Batch result: [Failed] Detection failed for {target_type}")
            frame_results = []
            for frame_idx in range(frame_count):
                frame_result = {
                    'frame_idx': frame_idx,
                    'success': False,
                    'status': 'Failed'
                }
                frame_results.append(frame_result)

            return {
                'should_stop': False,
                'ready_count': 0,
                'far_count': 0,
                'failed_count': frame_count,
                'successful_frames': 0,
                'total_frames': frame_count,
                'avg_error': float('inf'),
                'std_error': 0.0,
                'max_error_frame': None,
                'avg_displacement': np.array([0.0, 0.0, 0.0]),
                'avg_displacement_magnitude': 0.0,
                'frame_results': frame_results
            }

    def _extract_target_position_from_batch(self, batch_data: Dict[str, Any], target_type: str) -> Optional[np.ndarray]:
        """
        从批量数据中提取目标位置

        Args:
            batch_data: 批量数据
            target_type: 目标类型

        Returns:
            目标位置坐标或None
        """
        try:
            button_coords = batch_data.get('button_coords_camera', [])
            knob_coord = batch_data.get('knob_coord_camera')

            print(f"debug | button_coords from batch: {button_coords}")
            print(f"debug | knob_coord from batch: {knob_coord}")
            print(f"debug | target_type: {target_type}")

            if target_type == 'left_button' and len(button_coords) >= 2:
                return np.array(button_coords[0])  # 左按钮
            elif target_type == 'right_button' and len(button_coords) >= 2:
                return np.array(button_coords[1])  # 右按钮
            elif target_type.startswith('knob_') and knob_coord is not None:
                return np.array(knob_coord)  # 旋钮（所有knob类型使用相同坐标）
            else:
                print(f"debug | Cannot extract target position: target_type={target_type}, button_coords_len={len(button_coords)}, knob_coord={knob_coord}")
                return None
        except Exception as e:
            print(f"Error extracting target position: {e}")
            return None
    
    def perform_finetune(self, target_type: str) -> Tuple[bool, str]:
        """
        Perform complete fine-tuning for specified target using multi-frame observation

        Args:
            target_type: Type of target ('left_button', 'right_button', 'knob_center', 'knob_left', 'knob_right')

        Returns:
            Tuple of (success, status_message)
        """
        print(f"Starting multi-frame fine-tuning for {target_type} target")
        print(f"Template coordinates: {self.template_coords[target_type]}")
        print(f"Threshold: {self.threshold:.4f} m ({self.threshold*1000:.2f} mm)")
        print(f"Max iterations: {self.max_iterations}")

        # # Auto-save at start of finetune
        # if hasattr(self.controller, '_auto_save_debug_data'):
        #     self.controller._auto_save_debug_data(f"finetune_{target_type}_start_", f"finetune_start_{target_type}")

        iteration = 0
        
        # Initial multi-frame observation to check if we already meet the requirements
        print(f"\n=== Initial multi-frame observation ===")
        observation_result = self.multi_frame_observation(target_type)
        
        if observation_result['should_stop']:
            message = f"Target already meets requirements! Ready: {observation_result['ready_count']}, Far: {observation_result['far_count']}"
            print(f"\n[OK] {message}")
            
            # 最终验证
            # verification = self.verify_finetune_result(target_type)
            # if verification.get('success', False):
            #     if verification['within_threshold']:
            #         print("[OK] 验证通过：目标位置已达到理想精度")
            #     else:
            #         print(f"[Warning] 验证警告：目标位置未完全达到理想精度 (实际误差: {verification['max_error_mm']:.2f}mm)")
            
            return True, message
        
        # Main fine-tuning loop
        while iteration < self.max_iterations:
            iteration += 1
            print(f"\n--- Fine-tune iteration {iteration} ---")
            # if hasattr(self.controller, '_auto_save_debug_data'):
            #     self.controller._auto_save_debug_data(f"finetune_{target_type}_iteration_{iteration}_start_", f"finetune_{target_type}_iteration_{iteration}_start")
            # Check if we have sufficient data for adjustment
            if observation_result.get('successful_frames', 0) < 1:
                return False, f"Fine-tuning failed at iteration {iteration}: Insufficient detection data ({observation_result.get('successful_frames', 0)} successful frames)"
            
            # Perform single fine-tuning step using average displacement from multi-frame observation
            
            # self.arm.rm_set_arm_run_mode(0)
            # time.sleep(1)

            success, status_message = self.perform_single_finetune_step(target_type, observation_result)

            # self.arm.rm_set_arm_run_mode(1)
            # time.sleep(1)
            
            if not success:
                return False, f"Fine-tuning failed at iteration {iteration}: {status_message}"
            
            print(f"Movement completed: {status_message}")
            
            # Multi-frame observation after movement
            print(f"\n=== Post-movement observation (iteration {iteration}) ===")
            observation_result = self.multi_frame_observation(target_type)
            
            # Check if we should stop based on Ready vs Far count
            if observation_result['should_stop']:
                message = f"Fine-tuning completed successfully in {iteration} iterations. Ready: {observation_result['ready_count']}, Far: {observation_result['far_count']}"
                print(f"\n[OK] {message}")
                
                # 最终验证结果
                # verification = self.verify_finetune_result(target_type)
                # if verification.get('success', False):
                #     if verification['within_threshold']:
                #         print("[OK] 验证通过：目标位置已达到理想精度")
                #     else:
                #         print(f"[Warning] 验证警告：目标位置未完全达到理想精度 (实际误差: {verification['max_error_mm']:.2f}mm)")
                
                return True, message
            
            # Continue if Ready count is not greater than Far count
            print(f"Iteration {iteration} completed. Ready: {observation_result['ready_count']}, Far: {observation_result['far_count']}, continuing...")
            
            # Check if we have enough successful frames to continue
            if observation_result['successful_frames'] < 1:
                print(f"Warning: Low detection success rate ({observation_result['successful_frames']}/{observation_result['total_frames']})")
        
        # Max iterations reached
        # final_observation = self.multi_frame_observation(target_type)
        message = f"Fine-tuning stopped after {self.max_iterations} iterations. Final Ready: {observation_result['ready_count']}, Far: {observation_result['far_count']}"
        
        # 最终验证结果
        # verification = self.verify_finetune_result(target_type)
        
        # 修改：即使误差大于阈值，也认为finetune成功，只显示warning
        if observation_result['ready_count'] >= observation_result['far_count'] and observation_result['ready_count'] >= 3:
            print(f"\n[OK] {message} (Acceptable result)")
            # if verification.get('success', False) and not verification['within_threshold']:
            #     print(f"[Warning] 警告：目标位置未完全达到理想精度 (实际误差: {verification['max_error_mm']:.2f}mm，阈值: {verification['threshold_mm']:.2f}mm)")
            return True, message + " (Acceptable result)"
        else:
            # 即使条件不满足，也认为成功，但给出更明确的警告
            print(f"\n[Warning] {message} (Proceeding despite suboptimal results)")
            # if verification.get('success', False) and not verification['within_threshold']:
            #     print(f"[Warning] 警告：目标位置未达到理想精度 (实际误差: {verification['max_error_mm']:.2f}mm，阈值: {verification['threshold_mm']:.2f}mm)")
            return True, message + " (Proceeding despite suboptimal results)"
    
    def get_realtime_error_info(self, target_type: str) -> Dict[str, Any]:
        """
        Get real-time error information for display
        
        Args:
            target_type: Type of target ('left_button', 'right_button', 'knob_center', 'knob_left', 'knob_right')
            
        Returns:
            Dictionary with error information
        """
        try:
            current_pos = self.detect_current_target_position(target_type)
            if current_pos is None:
                return {
                    'success': False,
                    'error': 'Detection failed',
                    'max_error': float('inf'),
                    'error_vector': np.array([0, 0, 0]),
                    'within_threshold': False
                }
            
            error_vector, max_error = self.calculate_position_error(current_pos, target_type)
            within_threshold = max_error < self.threshold
            
            return {
                'success': True,
                'current_position': current_pos,
                'template_position': self.template_coords[target_type],
                'error_vector': error_vector,
                'max_error': max_error,
                'max_error_mm': max_error * 1000,
                'threshold': self.threshold,
                'threshold_mm': self.threshold * 1000,
                'within_threshold': within_threshold
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'max_error': float('inf'),
                'error_vector': np.array([0, 0, 0]),
                'within_threshold': False
            }


# Global fine-tuner instance (will be initialized by controller)
global_finetuner = None


def initialize_finetuner(controller):
    """Initialize global fine-tuner instance"""
    global global_finetuner
    global_finetuner = FineTuner(controller)


def force_reinitialize_finetuner(controller):
    """Force reinitialize global fine-tuner instance (for configuration reload)"""
    global global_finetuner
    print("Force reinitializing fine-tuner with updated configuration...")
    global_finetuner = FineTuner(controller)
    print("[OK] Fine-tuner reinitialized successfully")


def perform_finetune_for_status(controller) -> Tuple[bool, str]:
    """
    Perform fine-tuning based on current controller status

    Args:
        controller: RealTimeButtonControl instance

    Returns:
        Tuple of (success, status_message)
    """
    global global_finetuner

    # Initialize only if needed (first time or after configuration reload)
    if global_finetuner is None:
        initialize_finetuner(controller)
    
    # Get target type from current status
    target_type = global_finetuner.get_target_type_from_status(controller.status)
    if target_type is None:
        return False, f"Fine-tuning not supported for status: {controller.status}"
    
    # Check if coordinate axes are available
    if controller.last_coordinate_axes is None:
        return False, "No coordinate axes available. Please execute 'a' (observation approach) first."
    
    # Perform fine-tuning
    return global_finetuner.perform_finetune(target_type)


def get_realtime_error_for_status(controller) -> Dict[str, Any]:
    """
    Get real-time error information for current status
    
    Args:
        controller: RealTimeButtonControl instance
        
    Returns:
        Dictionary with error information
    """
    global global_finetuner
    
    # Initialize if needed
    if global_finetuner is None:
        initialize_finetuner(controller)
    
    # Get target type from current status
    target_type = global_finetuner.get_target_type_from_status(controller.status)
    if target_type is None:
        return {
            'success': False,
            'error': f'Unsupported status: {controller.status}',
            'max_error': float('inf'),
            'within_threshold': False
        }
    
    return global_finetuner.get_realtime_error_info(target_type)


def print_finetune_status():
    """Print fine-tuning status information"""
    print("Fine-tuning module loaded and ready")
    if global_finetuner is not None:
        print(f"Template coordinates:")
        for target_type, coords in global_finetuner.template_coords.items():
            print(f"  {target_type}: {coords}")
        print(f"Threshold: {global_finetuner.threshold:.4f} m ({global_finetuner.threshold*1000:.2f} mm)")
        print(f"Max iterations: {global_finetuner.max_iterations}")
    else:
        print("Fine-tuner not yet initialized")
