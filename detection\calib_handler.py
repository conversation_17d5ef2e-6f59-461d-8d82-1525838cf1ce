import numpy as np
import cv2
import glob
# import matplotlib.pyplot as plt
# import matplotlib
# matplotlib.use("TkAgg")


def order_points(pts):
    """
    Order the points of a rectangle in top-left, top-right, bottom-right, bottom-left order.
    
    Args:
        pts: Array of 4 points representing rectangle corners
        
    Returns:
        Ordered rectangle points
    """
    # Initialize ordered coordinates
    rect = np.zeros((4, 2), dtype="float32")

    # Top-left: point with smallest sum of coordinates
    # Bottom-right: point with largest sum of coordinates
    s = pts.sum(axis=1)
    rect[0] = pts[np.argmin(s)]  # top-left
    rect[2] = pts[np.argmax(s)]  # bottom-right
    
    # Get the remaining two points
    new_pts = []
    for pt in pts:
        if (pt[0] == rect[0][0] and pt[1] == rect[0][1]) or (pt[0] == rect[2][0] and pt[1] == rect[2][1]):
            continue
        new_pts.append(pt)
        
    # Top-right: point with smallest difference of coordinates
    # Bottom-left: point with largest difference of coordinates
    diff = np.diff(new_pts, axis=1)
    idx1 = np.argmin(diff)
    idx3 = 1 if idx1 == 0 else 0
    rect[1] = new_pts[idx1]  # top-right
    rect[3] = new_pts[idx3]  # bottom-left

    return rect

import time
def calibrate_handler_detection(image, thr_init=25, verbose=False, ret_ar=False):  # , thr_init=80
    """
    Detect calibrate_handler(black rectangle) in the image.
    
    Args:
        image: Input BGR image
        thr_init: Initial threshold value
        verbose: Whether to print additional information
        ret_ar: Whether to return aspect ratio
        
    Returns:
        Rectangle points or (Rectangle points, aspect ratio) if ret_ar is True
    """
    # 1. Define ROI mask
    time1 = time.time()
    roi_mask = np.zeros(image.shape[:2], dtype=np.uint8)
    cv2.rectangle(roi_mask, (300, 220), (640, 480), 255, -1)
    thr = thr_init
    # 2. Create mask for the ROI
    green_mask = np.ones(image.shape[:2], dtype=np.uint8) * 255
    green_mask = cv2.bitwise_and(green_mask, green_mask, mask=roi_mask)
    green_area_mask = green_mask

    # 3. Extract ROI and get value channel
    green_roi = cv2.bitwise_and(image, image, mask=green_area_mask)
    green_roi[green_area_mask == 0] = 255
    v_channel = cv2.cvtColor(green_roi, cv2.COLOR_BGR2HSV)[:, :, 2]
    
    # 4. Create threshold values to try
    # thr_try_list = np.array([i // 2 if i % 2 == 0 else -i // 2 for i in range(0, 21)])*3
    thr_try_list = np.array([i // 2 if i % 2 == 0 else i//2 + 15 for i in range(0, 21)]) * 3
    thr_try_idx = -1
    target_contour = None
    target_aspect_ratio = None
    
    # 5. Try different thresholds until a suitable contour is found
    while target_contour is None and thr_try_idx < len(thr_try_list) - 1:
        thr_try_idx += 1
        thr = thr_init + thr_try_list[thr_try_idx]
        if verbose:
            print('thr', thr)
            
        # Create binary mask
        black_mask = cv2.threshold(v_channel, thr, 255, cv2.THRESH_BINARY_INV)[1]
        
        # Apply morphological operations to remove noise
        kernel = np.ones((3, 3), np.uint8)
        black_mask = cv2.morphologyEx(black_mask, cv2.MORPH_OPEN, kernel, iterations=4)
        black_mask = cv2.morphologyEx(black_mask, cv2.MORPH_CLOSE, kernel, iterations=2)

        # 6. Find contours in the mask
        black_contours, _ = cv2.findContours(black_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if not black_contours:
            continue

        # 7. Filter contours by area and aspect ratio
        target_contours = []
        min_area = 800
        max_area = 6000
        ideal_area = 4500#3000
        aspect_ratio_range = (7, 10)

        for cnt in black_contours:
            area = cv2.contourArea(cnt)
            if area < min_area or area > max_area:
                continue

            # Calculate contour properties
            contour_mask = np.zeros_like(v_channel)
            cv2.drawContours(contour_mask, [cnt], -1, (255), thickness=cv2.FILLED)
            rect = cv2.minAreaRect(cnt)
            box = cv2.boxPoints(rect)
            box = np.int0(box)
            box_mask = np.zeros_like(v_channel)
            cv2.drawContours(box_mask, [box], -1, (255), thickness=cv2.FILLED)
            
            # Calculate intersection ratio
            intersection_area = cv2.countNonZero(cv2.bitwise_and(contour_mask, box_mask))
            total_area = cv2.countNonZero(box_mask)
            area_ratio = intersection_area/total_area
            
            # Calculate aspect ratio
            (w, h) = rect[1]
            aspect_ratio = max(w, h) / min(w, h) if min(w, h) > 0 else 0

            # Add candidate if aspect ratio is in range
            if aspect_ratio_range[0] < aspect_ratio < aspect_ratio_range[1]:
                target_contours.append([cnt, abs(area - ideal_area), area_ratio, aspect_ratio])

                if verbose:
                    print( abs(area), area_ratio, aspect_ratio)
                
        # Sort candidates by quality score
        target_contours.sort(key=lambda x: x[1]-x[2]*1000, reverse=False)
        if len(target_contours) > 0:
            target_contour = target_contours[0][0]
            target_aspect_ratio = target_contours[0][3]
            if verbose:
                print('at v_channel:', target_contours[0][1:])

    # 8. If no contour found, try using green channel
    if target_contour is None:
        if verbose:
            print('try green channel')
        g_channel = image[:, :, 1]
        thr_try_idx = -1
        target_aspect_ratio = None
        
        while target_contour is None and thr_try_idx < len(thr_try_list) - 1:
            thr_try_idx += 1
            thr = thr_init + thr_try_list[thr_try_idx]
            if verbose:
                print('thr', thr)
                
            # Create binary mask from green channel
            black_mask = cv2.threshold(g_channel, thr, 255, cv2.THRESH_BINARY_INV)[1]
            kernel = np.ones((3, 3), np.uint8)
            black_mask = cv2.morphologyEx(black_mask, cv2.MORPH_OPEN, kernel, iterations=4)
            black_mask = cv2.morphologyEx(black_mask, cv2.MORPH_CLOSE, kernel, iterations=2)

            # Find contours
            black_contours, _ = cv2.findContours(black_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            if not black_contours:
                continue

            # Filter contours
            target_contours = []
            min_area = 800
            max_area = 6000
            ideal_area = 4500#3000
            aspect_ratio_range = (7, 10)

            for cnt in black_contours:
                area = cv2.contourArea(cnt)
                if area < min_area or area > max_area:
                    continue
                    
                # Calculate contour properties
                contour_mask = np.zeros_like(g_channel)
                cv2.drawContours(contour_mask, [cnt], -1, (255), thickness=cv2.FILLED)
                rect = cv2.minAreaRect(cnt)
                box = cv2.boxPoints(rect)
                box = np.int0(box)
                box_mask = np.zeros_like(g_channel)
                cv2.drawContours(box_mask, [box], -1, (255), thickness=cv2.FILLED)
                
                # Calculate intersection ratio
                intersection_area = cv2.countNonZero(cv2.bitwise_and(contour_mask, box_mask))
                total_area = cv2.countNonZero(box_mask)
                area_ratio = intersection_area / total_area
                
                # Calculate aspect ratio
                (w, h) = rect[1]
                aspect_ratio = max(w, h) / min(w, h) if min(w, h) > 0 else 0

                # Add candidate if aspect ratio is in range
                if aspect_ratio_range[0] < aspect_ratio < aspect_ratio_range[1]:
                    target_contours.append([cnt, abs(area - ideal_area), area_ratio, aspect_ratio])
                    if verbose:
                        print( abs(area - ideal_area), area_ratio, aspect_ratio)
                    
            # Sort candidates by quality score
            target_contours.sort(key=lambda x: x[1] - x[2] * 1000, reverse=False)
            if len(target_contours) > 0:
                target_contour = target_contours[0][0]
                target_aspect_ratio = target_contours[0][3]
                if verbose:
                    print('at g_channel:', target_contours[0][1:])

    # 9. Return None if no suitable contour found
    if target_contour is None:
        print(f"calib_h:{1000*(time.time()-time1):.2f}ms, False, None...")
        if ret_ar:
            return None, None
        else:
            return None
            
    # 10. Get rectangle points from contour
    rect = cv2.minAreaRect(target_contour)
    box = cv2.boxPoints(rect)
    box = np.int0(box)
    approx = box

    print(f"calib_h:{1000*(time.time()-time1):.2f}ms, {len(approx) == 4} ({thr})")
    if len(approx) != 4:
        if ret_ar:
            return None, None
        else:
            return None

    # 11. Order rectangle points
    rect_points = approx.reshape(4, 2)
    rect_points = order_points(rect_points)

    if ret_ar:
        return rect_points, target_aspect_ratio
    else:
        return rect_points


if __name__ == "__main__":
    # Test the rectangle detection on sample images
    # paths = glob.glob(r'G:\Windows\PycharmProjects1\bot\data\0710\p3\*')
    paths = glob.glob(r'G:\Windows\PycharmProjects1\robotArm\data\quick-saves\0722*')
    # paths = glob.glob(r'G:\Windows\PycharmProjects1\robotArm\data\quick-saves\072302*')

    for path_idx, path in enumerate(paths[::-1][:]):
        print(path_idx, path)
        if path[-4:] == '.npy':
            continue
            
        image = cv2.imread(path)
        rectangle_points, ar = calibrate_handler_detection(image, verbose=True, ret_ar=True)

        if rectangle_points is not None:
            print("Black rectangle coordinates:")
            print("Top-left:", rectangle_points[0])
            print("Top-right:", rectangle_points[1])
            print("Bottom-right:", rectangle_points[2])
            print("Bottom-left:", rectangle_points[3])
            print("Aspect ratio:", ar)

            # Visualize results
            result = image.copy()
            cv2.drawContours(result, [rectangle_points.astype(int)], -1, (0, 0, 255), 3)
            
            plt.figure()
            plt.imshow(result)
            plt.show()
        else:
            plt.figure()
            plt.imshow(image)
            plt.show()
            pass
