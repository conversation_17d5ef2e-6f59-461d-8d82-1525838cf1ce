#!/usr/bin/env python3

import sys
import os
import threading
import time
import traceback
import numpy as np
import pyrealsense2 as rs
from typing import Tuple, Dict, Any, Optional
import glob
from datetime import datetime
from buttonControl.exceptions import *


# Add buttonControl to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'buttonControl'))
# Add RM_API2 to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# Import robotArm utilities
from utils import *


# Import RM_API2 and pyrealsense2
from RM_API2.Python.Robotic_Arm.rm_robot_interface import RoboticArm, rm_thread_mode_e, rm_robot_arm_model_e, rm_force_type_e

# Import buttonControl.button_action
from buttonControl.button_action import set_observation_checkpoint, set_knob_checkpoint, get_knob_checkpoint
from buttonControl.data_manager import DataManager
from buttonControl.config import get_config



class ButtonController:
    def __init__(self, camera_instance=None):

        self.camera_instance = camera_instance
        self.emergency_stopped = False
        self._error_message = ""
        self._in_reset_process = False

        self.status = 'uncertain'  # Current status
        self.states = []  # Status history
        self.states.append(self.status)

        self.last_coordinate_axes = None  # Store (x_axis, y_axis, z_axis) from observing state
        self.last_observing_frame = None  # Frame number when coordinate system was saved

        # Initialize hardware components
        self.arm = None  # RoboticArm instance
        self.robot_config = None  # Robot configuration

        self._initialize_button_controller()
        print("ButtonController initialized successfully")
    
        self._last_search_checkpoint = None  # Store optimal pose from automatic search

        # Define error codes that should trigger automatic reset
        # All errors except EMERGENCY_STOP_ERROR and SUCCESS_CODE trigger reset
        self._RESET_TRIGGER_ERRORS = {
            ARM_STATE_ERROR,
            ARM_MOVEMENT_ERROR,
            GRIPPER_STATE_ERROR,
            GRIPPER_CONTROL_ERROR,
            ARM_TRAJECTORY_TIMEOUT_ERROR,
            NO_DETECTOR_ERROR,
            BUTTON_DETECTION_ERROR,
            BUTTON_APPROACH_ERROR,
            BUTTON_OPERATION_ERROR,
            FINETUNE_ERROR,
            BUTTON_STATE_ERROR,
            BUTTON_CONFIG_ERROR,
            CAMERA_ERROR,
            MEMORY_ERROR,
            PARSING_ERROR,
            UNKNOWN_ERROR
        }

        # Error codes that should NOT trigger reset
        self._NO_RESET_ERRORS = {
            EMERGENCY_STOP_ERROR,  # Emergency stop should not trigger reset
            SUCCESS_CODE  # Success (including finetune warnings) should not trigger reset
        }

        # Auto-reset delay configuration (in seconds)
        self._auto_reset_delay = self.robot_config.auto_reset_delay
    
        self.data_manager = None
        self._initialize_data_manager()

    def _initialize_button_controller(self):
        """Initialize hardware components directly."""
        try:
            # Initialize configuration
            self.robot_config = get_config()
            print("ButtonController: Configuration loaded")

            # Initialize robotic arm - check if we can share from camera instance
            if self.camera_instance is not None and hasattr(self.camera_instance, 'arm'):
                print("ButtonController: Using shared robotic arm from camera instance")
                self.arm = self.camera_instance.arm
                self.handle = self.camera_instance.handle
            else:
                error_message = "No camera instance provided"
                print(f"ButtonController: {error_message}")
                return self._return_error_with_reset(CAMERA_ERROR, error_message)
            
            self.arm_model = getattr(rm_robot_arm_model_e, self.robot_config.arm_model)
            self.force_type = getattr(rm_force_type_e, self.robot_config.force_type)
            
            # Check arm connection
            arm_state = self.arm.rm_get_arm_all_state()
            print(f"ButtonController: Arm connection status: {arm_state}")
            current_state = self.arm.rm_get_current_arm_state()
            print(f"ButtonController: Current arm state: {current_state}")
            self.arm.rm_set_arm_run_mode(1)
            
            # Print configuration parameters
            distances = self.robot_config.get_distances()
            movement = self.robot_config.get_movement_parameters()
            offsets = self.robot_config.get_offsets()
            print(f"ButtonController: Distances initialized - Observation: {distances['observation_distance']*1000:.1f}mm, Touch: {distances['touch_distance']*1000:.1f}mm")
            print(f"ButtonController: Search distances - Min: {distances['smallest_working_distance']*1000:.1f}mm, Max: {distances['largest_working_distance']*1000:.1f}mm")
            print(f"ButtonController: Movement parameters - v: {movement['v']}, r: {movement['r']}")
            print(f"ButtonController: Button offsets - Left: {offsets['left_button_offsets']}, Right: {offsets['right_button_offsets']}")
            print(f"ButtonController: Zero pose configured: {self.robot_config.zero_pose}")

            print("ButtonController: Hardware initialization completed successfully")

        except Exception as e:
            print(f"Error initializing hardware: {e}")
            print(f"Error details: {traceback.format_exc()}")
            self.arm = None
            self.camera_instance = None

    def _initialize_data_manager(self):
        """初始化DataManager"""
        try:
            from buttonControl.data_manager import DataManager
            self.data_manager = DataManager(self.robot_config, self.arm, self.camera_instance, self.status)
        except ImportError as e:
            print(f"Warning: Could not initialize DataManager: {e}")
            self.data_manager = None
        except Exception as e:
            print(f"Warning: DataManager initialization failed: {e}")
            self.data_manager = None

    def set_status(self, new_status: str, reason: str = None):
        """
        Set the current status and add it to the status history.

        Args:
            new_status: New status to set
            reason: Optional reason for the status change
        """
        last_status = self.status
        self.status = new_status
        self.states.append(new_status)

        print(f"ButtonController Status changed: {last_status} -> {new_status}")
        if reason:
            print(f"Reason: {reason}")

        # Limit status history to prevent memory issues
        if len(self.states) > 100:
            self.states = self.states[-50:]  # Keep last 50 states
            print(f"ButtonController: Status history trimmed to {len(self.states)} entries")

    

    def _auto_reset(self, error_code: int, error_message: str) -> None:
        """
        Perform automatic reset when an error occurs.

        Args:
            error_code: The error code that triggered the reset
            error_message: The error message
        """
        if self._in_reset_process:
            print(f"ButtonController: in reset process, skipping additional auto-reset for error {error_code}: {error_message}")
            return

        if error_code not in self._RESET_TRIGGER_ERRORS:
            print(f"ButtonController: skipping auto-reset for error {error_code} (not in trigger list)")
            return
        
        if self.emergency_stopped:
            print(f"ButtonHandler: Skipping auto-reset for emergency stop")
            return

        print(f"ButtonController: Auto-reset triggered by error {error_code}: {error_message}")

        # Add delay before reset to allow user to see the error message
        if self._auto_reset_delay > 0:
            print(f"ButtonController: Waiting {self._auto_reset_delay} seconds before reset...")
            import time
            time.sleep(self._auto_reset_delay)

        try:
            # Set flag to prevent infinite loops
            self._in_reset_process = True

            # Perform reset
            reset_result = self.reset()

            if reset_result == SUCCESS_CODE:
                print("ButtonController: Auto-reset completed successfully")
            else:
                print(f"ButtonController: Auto-reset failed with code {reset_result}")

        except Exception as reset_error:
            print(f"ButtonController: Auto-reset failed with exception: {reset_error}")
            print(f"Reset error details: {traceback.format_exc()}")
        finally:
            # Always clear the flag
            self._in_reset_process = False

    def _return_error_with_reset(self, error_code: int, error_message: str, extra_data: dict = None) -> tuple:
        """
        Unified error return method that handles reset triggering automatically.

        Args:
            error_code: The error code to return
            error_message: The error message
            extra_data: Additional data to include in the return tuple

        Returns:
            Tuple of (error_code, error_message, extra_data)
        """
        # Trigger reset if appropriate
        self._auto_reset(error_code, error_message)

        # Return error tuple
        if extra_data is None:
            extra_data = {}
        extra_data['error_message'] = error_message

        return (error_code, extra_data)

    def _save_data_with_info(self, data, prefix=""):
        """
        Save data and return information about what was actually saved.

        Args:
            data: Collected data dictionary
            prefix: Optional prefix for saved files

        Returns:
            Tuple of (saved_data, save_directory, saved_files_dict)
        """
        try:
            # Use DataManager to save data - it handles directory creation internally
            saved_data = self.save_collected_data(data, prefix)

            # Get actual timestamp from data (DataManager uses data['timestamp'])
            timestamp = data.get('timestamp', 'unknown')
            # DataManager creates directory with this pattern
            save_directory = f'./data/button-debug-saves/{datetime.now().strftime("%Y%m%d_%H%M%S")}'

            # Build comprehensive saved files list for DataManager
            saved_files = self._build_datamanager_file_list(prefix, timestamp, data)

            return saved_data, save_directory, saved_files


        except Exception as e:
            print(f"ButtonController: Error in _save_data_with_info: {e}")
            return None, None, {}

    def _auto_save_debug_data(self, prefix="", context=""):
        """
        Auto-save debug data for analysis using DataManager

        Args:
            prefix: Filename prefix for saved files
            context: Context description for logging

        Returns:
            bool: True if save successful, False otherwise
        """
        try:
            print(f"ButtonController: Auto-saving debug data with prefix '{prefix}' (context: {context})")

            # Collect current data
            data = self.collect_data(frame_count=1)
            if data is None:
                print(f"ButtonController: Failed to collect data for auto-save (context: {context})")
                return False

            # 使用 DataManager 的保存功能
            if self.data_manager is not None:
                try:
                    # 添加上下文信息到数据中
                    data['debug_context'] = context
                    data['debug_prefix'] = prefix

                    # 使用 DataManager 保存数据
                    saved_data = self.data_manager.save_collected_data(data, prefix)
                    if saved_data is not None:
                        print(f"ButtonController: Auto-save completed successfully for context: {context}")
                        return True
                    else:
                        print(f"ButtonController: DataManager save failed for context: {context}")
                        return False
                except Exception as e:
                    print(f"ButtonController: DataManager save error: {e}")
                    return False
            else:
                print(f"ButtonController: No DataManager available for auto-save")
                return False

        except Exception as e:
            print(f"ButtonController: Error in auto-save debug data at {context}: {e}")
            print(f"ButtonController: Error details: {traceback.format_exc()}")
            return False

    def _build_datamanager_file_list(self, prefix, timestamp, data):
        """Build file list for DataManager saved files"""
        saved_files = {}

        # Images (if available)
        if data.get('color_image') is not None:
            saved_files['color_image'] = f"{prefix}colorImage_{timestamp}.png"
        if data.get('depth_image') is not None:
            saved_files['depth_image'] = f"{prefix}depthImage_{timestamp}.png"

        # Button coordinates (always saved, even if empty)
        saved_files['button_coords_camera'] = f"{prefix}buttonCoords_camera_{timestamp}.csv"
        saved_files['button_coords_base'] = f"{prefix}buttonCoords_base_{timestamp}.csv"

        # Knob data (always saved, even if empty)
        saved_files['knob_coord_camera'] = f"{prefix}knobCoord_camera_{timestamp}.csv"
        saved_files['knob_coord_base'] = f"{prefix}knobCoord_base_{timestamp}.csv"
        saved_files['knob_angle'] = f"{prefix}knobAngle_{timestamp}.txt"

        # Arm state
        if data.get('pose') is not None:
            saved_files['pose'] = f"{prefix}pose_{timestamp}.txt"
        if data.get('joint') is not None:
            saved_files['joint'] = f"{prefix}joint_{timestamp}.txt"

        # Metadata
        saved_files['coordinate_system_info'] = f"{prefix}coordinate_system_info_{timestamp}.txt"
        if data.get('stability_metrics'):
            saved_files['stability_metrics'] = f"{prefix}stability_{timestamp}.txt"

        return saved_files

    def _build_fallback_file_list(self, prefix, timestamp, data):
        """Build file list for fallback saved files"""
        saved_files = {}
        if data.get('joint') is not None:
            saved_files['joint'] = f"{prefix}joint_{timestamp}.txt"
        if data.get('pose') is not None:
            saved_files['pose'] = f"{prefix}pose_{timestamp}.txt"
        if data.get('knob_angle') is not None:
            saved_files['knob_angle'] = f"{prefix}knobAngle_{timestamp}.txt"

        return saved_files

    def debug_capture_current_view(self, params=None):
        """
        Debug function to capture current view and save detection results.
        This function collects data from current camera view without moving the arm,
        performs button/knob detection, and saves the results.

        Returns:
            Tuple of (result_code, result_data) where result_data contains:
            - save_directory: Directory where files were saved
            - timestamp: Timestamp of the capture
            - detection_results: Detection results (buttons, knob, etc.)
            - saved_files: List of saved file names
            - arm_state: Current arm pose and joint angles
        """
        try:
            save_prefix = params.get("save_prefix", "debug_") if params else "debug_"
            print(f"ButtonController: Starting debug capture with prefix '{save_prefix}'")

            # Collect current view data (single frame, no multi-frame for debug)
            data = self.collect_data(frame_count=1)
            if data is None:
                error_message = "Failed to collect current view data - check camera and arm connections"
                print(f"ButtonController: {error_message}")
                return self._return_error_with_reset(BUTTON_DETECTION_ERROR, error_message, {
                    "save_directory": None,
                    "timestamp": None,
                    "detection_results": {},
                    "saved_files": {},
                    "arm_state": {}
                })

            # Save the collected data and get actual save info
            saved_data, actual_save_dir, actual_saved_files = self._save_data_with_info(data, save_prefix)
            if saved_data is None:
                error_message = "Failed to save collected data"
                print(f"ButtonController: {error_message}")
                return (MEMORY_ERROR, {
                    "error_message": error_message,
                    "save_directory": None,
                    "timestamp": data.get('timestamp'),
                    "detection_results": {},
                    "saved_files": {},
                    "arm_state": {}
                })

            # Format detection results
            detection_results = {
                "buttons_detected": len(data.get('button_coords', [])),
                "button_coords": data.get('button_coords', []),
                "button_labels": data.get('button_labels', []),
                "knob_detected": data.get('knob_coord') is not None,
                "knob_coord": data.get('knob_coord'),
                "knob_angle": data.get('knob_angle')
            }

            # Format arm state
            arm_state = {
                "pose": data.get('pose'),
                "joint": data.get('joint')
            }
            import time
            # Use actual save directory and files from save operation
            timestamp = data.get('timestamp', int(time.time()))

            result_data = {
                "save_directory": actual_save_dir,
                "timestamp": timestamp,
                "detection_results": detection_results,
                "saved_files": actual_saved_files,
                "arm_state": arm_state
            }

            print(f"ButtonController: Debug capture completed successfully")
            print(f"ButtonController: Detected {detection_results['buttons_detected']} buttons")
            print(f"ButtonController: Knob detected: {detection_results['knob_detected']}")
            if detection_results['knob_detected'] and detection_results['knob_angle'] is not None:
                print(f"ButtonController: Knob angle: {detection_results['knob_angle']:.1f}°")

            return (SUCCESS_CODE, result_data)

        except Exception as e:
            import traceback
            error_message = f"Exception during debug capture: {str(e)}"
            print(f"ButtonController: {error_message}")
            print(f"ButtonController: Error details: {traceback.format_exc()}")
            return self._return_error_with_reset(UNKNOWN_ERROR, error_message, {
                "save_directory": None,
                "timestamp": None,
                "detection_results": {},
                "saved_files": {},
                "arm_state": {}
            })

    def _execute_action(self, action: str, extra_params: dict = None) -> tuple:
        """
        Execute the specific action by calling bottom-level functions directly.

        Args:
            action: Action to execute
            extra_params: dict, additional parameters (such as checkpoint, checkpoint_bool)

        Returns:
            Tuple of (error_code, error_message, extra_data). SUCCESS_CODE if successful.
        """
        if extra_params is None:
            extra_params = {}
        try:
            print(f"ButtonController: Executing action '{action}'")

            if action == 'find_buttons':
                from buttonControl.button_action import search_targets
                print("ButtonController: Starting target search...")

                distances = self.robot_config.get_distances()
                search_params = self.robot_config.get_search_parameters()

                # Handle known_orientation based on checkpoint_bool logic
                if extra_params.get('force_search', False):
                    # When checkpoint_bool is True, force re-search by setting known_orientation to None
                    known_orientation = None
                    print("ButtonController: Force search mode - setting known_orientation to None")
                else:
                    # When checkpoint_bool is False, use checkpoint if provided, otherwise use config
                    known_orientation = extra_params.get('checkpoint', None)
                    if known_orientation is None:
                        known_orientation = self.robot_config.get_known_orientation()
                    if known_orientation is not None:
                        print(f"ButtonController: Using provided/config known_orientation: {known_orientation}")
                    else:
                        print("ButtonController: No known_orientation available, will perform search")
                # 调用search_targets
                success, result_dict = search_targets(
                    button_handler=self,
                    smallest_working_distance=distances['smallest_working_distance'],
                    largest_working_distance=distances['largest_working_distance'],
                    joint5_angle=search_params['joint5_angle'],
                    known_orientation=known_orientation
                )
                if success:

                    self.set_status('visible', 'Target search completed successfully')
                    confidence = result_dict.get('detection_confidence', 0.0)
                    distance_camera = result_dict.get('mean_distance_camera', 0.0)
                    distance_base = result_dict.get('mean_distance_base', 0.0)

                    print(f"ButtonController: Search successful! Confidence: {confidence:.3f}, Distance to base: {distance_base:.3f}m, Distance to camera: {distance_camera:.3f}m")
                    # 记录自动搜索得到的最优位姿
                    best_joint = result_dict.get('best_joint')
                    if best_joint is not None:
                        self._last_search_checkpoint = list(best_joint)
                    else:
                        self._last_search_checkpoint = None
                    return (SUCCESS_CODE, "Target search completed successfully", {'checkpoint': self._last_search_checkpoint})
                else:
                    self._auto_save_debug_data("find_buttons_failed_", "find_button_failed")
                    self._last_search_checkpoint = None
                    error_message = "Search failed - no targets found"
                    return self._return_error_with_reset(BUTTON_DETECTION_ERROR, error_message)

            elif action == 'observe_buttons':
                # Import approach_button from button_action
                from buttonControl.button_action import approach_button
                import time
                time.sleep(1)
                print("ButtonController: Executing observation approach...")

                # Collect data using configured multi_frame_count
                data = self.collect_data(frame_count=5)
                if not data:
                    self._auto_save_debug_data("observe_buttons_data_collection_failed_", "observe_buttons_data_collection_failed")
                    error_message = "Failed to collect data for observation approach"
                    return self._return_error_with_reset(BUTTON_DETECTION_ERROR, error_message)

                # Check button count
                if len(data['button_coords']) < 4:
                    self._auto_save_debug_data("observe_buttons_insufficient_buttons_", f"observe_buttons_insufficient_buttons_{len(data['button_coords'])}")
                    error_message = f"Insufficient buttons detected ({len(data['button_coords'])})"
                    return self._return_error_with_reset(BUTTON_DETECTION_ERROR, error_message)
                
                self._auto_save_debug_data("observe_buttons_success_", "observe_buttons_success")

                # Get configuration parameters
                distances = self.robot_config.get_distances()
                movement = self.robot_config.get_movement_parameters()
                processing = self.robot_config.get_processing_settings()
                
                # Call approach_button with safe error handling
                try:
                    approach_button(
                        self.arm,
                        data['pose'],
                        data['joint'],
                        data['button_coords'],
                        distances['observation_distance'],
                        camera_up=True,
                        robust_normal=processing['use_robust_normal'],
                        point_cloud=data['point_cloud'],
                        method='movej',
                        v=movement['v'],
                        r=movement['r'],
                        connect=movement['connect'],
                        block=movement['block'],
                        target='centers',
                        offset_x=0.0,
                        offset_y=0.0,
                        knob_coord=data['knob_coord'],
                        estimate_knob=True,
                        button_handler=self
                    )
                except Exception as e:
                    # Safe error conversion - maintain original behavior while providing better error codes
                    error_message = str(e)
                    if "Emergency stop" in error_message or "emergency" in error_message.lower():
                        error_code = EMERGENCY_STOP_ERROR
                    elif "detection" in error_message.lower() or "detect" in error_message.lower() or "insufficient" in error_message.lower():
                        error_code = BUTTON_DETECTION_ERROR
                    elif "approach" in error_message.lower() or "movement" in error_message.lower() or "move" in error_message.lower():
                        error_code = BUTTON_APPROACH_ERROR
                    else:
                        # Default to approach error for observation action
                        error_code = BUTTON_APPROACH_ERROR
                        error_message = f"Observation approach failed: {error_message}"

                    return self._return_error_with_reset(error_code, error_message)
                
                # Update status and save coordinate axes
                self.set_status('observing', 'Observation approach completed successfully')
                self._save_coordinate_axes_from_observation()

                return (SUCCESS_CODE, "Observation approach completed successfully", {})

            elif action == 'touch_left_button':
                # Import approach_button from button_action
                from buttonControl.button_action import approach_button

                print("ButtonController: Executing touch approach to left button...")

                # Collect data using configured multi_frame_count
                data = self.collect_data(frame_count=5)
                if not data:
                    self._auto_save_debug_data("touch_left_button_failed_", "touch_left_button_failed")
                    error_message = "Failed to collect data for touch approach"
                    return self._return_error_with_reset(BUTTON_DETECTION_ERROR, error_message)
                
                self._auto_save_debug_data("touch_left_button_success_", "touch_left_button_success")

                # Get configuration parameters
                distances = self.robot_config.get_distances()
                movement = self.robot_config.get_movement_parameters()
                processing = self.robot_config.get_processing_settings()
                offsets = self.robot_config.get_offsets()
                
                # Get left button offsets
                offset_x, offset_y = offsets['left_button_offsets']
                
                # Call approach_button
                approach_button(
                    self.arm,
                    data['pose'],
                    data['joint'],
                    data['button_coords'],
                    distances['touch_distance'],
                    camera_up=True,
                    robust_normal=processing['use_robust_normal'],
                    point_cloud=data['point_cloud'],
                    method='movej',
                    v=movement['v'],
                    r=movement['r'],
                    connect=movement['connect'],
                    block=movement['block'],
                    target='left',
                    offset_x=offset_x,
                    offset_y=offset_y,
                    knob_coord=data['knob_coord'],
                    button_handler=self
                )
                
                # Update status
                self.set_status('touching_left_button', 'Touch approach to left button completed')

                # 设置observation_checkpoint
                set_observation_checkpoint(data['joint'])

                return (SUCCESS_CODE, "Touch approach to left button completed", {})

            elif action == 'touch_right_button':
                # Import approach_button from button_action
                from buttonControl.button_action import approach_button

                print("ButtonController: Executing touch approach to right button...")

                # Collect data using configured multi_frame_count
                data = self.collect_data(frame_count=5)
                if not data:
                    self._auto_save_debug_data("touch_right_button_failed_", "touch_right_button_failed")
                    error_message = "Failed to collect data for touch approach"
                    return self._return_error_with_reset(BUTTON_DETECTION_ERROR, error_message)
                
                self._auto_save_debug_data("touch_right_button_success_", "touch_right_button_success")
                # Get configuration parameters
                distances = self.robot_config.get_distances()
                movement = self.robot_config.get_movement_parameters()
                processing = self.robot_config.get_processing_settings()
                offsets = self.robot_config.get_offsets()
                
                # Get right button offsets
                offset_x, offset_y = offsets['right_button_offsets']
                
                # Call approach_button
                approach_button(
                    self.arm,
                    data['pose'],
                    data['joint'],
                    data['button_coords'],
                    distances['touch_distance'],
                    camera_up=True,
                    robust_normal=processing['use_robust_normal'],
                    point_cloud=data['point_cloud'],
                    method='movej',
                    v=movement['v'],
                    r=movement['r'],
                    connect=movement['connect'],
                    block=movement['block'],
                    target='right',
                    offset_x=offset_x,
                    offset_y=offset_y,
                    knob_coord=data['knob_coord'],
                    button_handler=self
                )
                
                # Update status
                self.set_status('touching_right_button', 'Touch approach to right button completed')

                # 设置observation_checkpoint
                set_observation_checkpoint(data['joint'])

                return (SUCCESS_CODE, "Touch approach to right button completed", {})

            elif action == 'touch_knob':
                # Import approach_button from button_action
                from buttonControl.button_action import approach_button

                print("ButtonController: Executing touch approach to knob...")

                # Collect data using configured multi_frame_count
                data = self.collect_data(frame_count=5)
                if not data:
                    self._auto_save_debug_data("touch_knob_data_collection_failed_", "touch_knob_data_collection_failed")
                    error_message = "Failed to collect data for knob approach"
                    return self._return_error_with_reset(BUTTON_DETECTION_ERROR, error_message)

                self._auto_save_debug_data("touch_knob_success_", "touch_knob_success")

                # Check if knob is detected
                if data['knob_coord'] is None:
                    error_message = "No knob detected"
                    return self._return_error_with_reset(BUTTON_DETECTION_ERROR, error_message)
                
                # Get configuration parameters
                distances = self.robot_config.get_distances()
                movement = self.robot_config.get_movement_parameters()
                processing = self.robot_config.get_processing_settings()
                offsets = self.robot_config.get_offsets()
                
                # Get knob offsets
                offset_x, offset_y = offsets['knob_offsets']
                
                # Call approach_button
                approach_button(
                    self.arm,
                    data['pose'],
                    data['joint'],
                    data['button_coords'],
                    distances['touch_distance'],
                    camera_up=True,
                    robust_normal=processing['use_robust_normal'],
                    point_cloud=data['point_cloud'],
                    method='movej',
                    v=movement['v'],
                    r=movement['r'],
                    connect=movement['connect'],
                    block=movement['block'],
                    target='knob',
                    offset_x=offset_x,
                    offset_y=offset_y,
                    knob_coord=data['knob_coord'],
                    button_handler=self
                )
                
                # Determine knob position based on angle
                knob_angle = data.get('knob_angle')
                knob_position = self._determine_knob_position_from_angle(knob_angle)
                
                # Update status based on knob position
                self.set_status(f'touching_knob_{knob_position}', f'Touch approach to knob ({knob_position}) completed')

                # 设置observation_checkpoint
                set_observation_checkpoint(data['joint'])
                set_knob_checkpoint(data.get('knob_angle'))  # 新增

                return (SUCCESS_CODE, f"Touch approach to knob ({knob_position}) completed", {})

            elif action == 'finetune':
                from buttonControl.finetune import perform_finetune_for_status
                
                print("ButtonController: Executing finetune operation...")
                
                # Check if coordinate axes are available
                if self.last_coordinate_axes is None:
                    error_message = "No coordinate axes available for finetune"
                    return self._return_error_with_reset(FINETUNE_ERROR, error_message)
                
                import time
                finetune_start_time = time.time()
                try:
                    success, message = perform_finetune_for_status(self)
                    finetune_end_time = time.time()
                    finetune_duration = finetune_end_time - finetune_start_time
                    print(f"ButtonController: Finetune completed in {finetune_duration:.2f} seconds")
                except Exception as e:
                    finetune_end_time = time.time()
                    finetune_duration = finetune_end_time - finetune_start_time
                    print(f"ButtonController: Finetune failed after {finetune_duration:.2f} seconds")
                    print(f"ButtonController: Exception during operation 'finetune': {e}")
                    error_message = f"Finetune failed with exception: {e}"
                    return self._return_error_with_reset(FINETUNE_ERROR, error_message)

            elif action == 'click_button':
                # Import click_button from button_action
                from buttonControl.button_action import click_button
                
                print("ButtonController: Executing button click...")
                
                # # Collect data before clicking
                # data = self.collect_data(frame_count=10)
                # if data:
                #     # Save collected data
                #     self.save_collected_data(data, 'click_')
                
                # Get click depth from config
                distances = self.robot_config.get_distances()
                
                # Call click_button
                click_button(
                    self.arm,
                    distances['click_depth'],
                    button_handler=self
                )
                
                print("ButtonController: Button click completed")
                return (SUCCESS_CODE, "Button click completed", {})

            elif action == 'turn_knob':
                # Import turn_knob from button_action
                from buttonControl.button_action import turn_knob
                
                print("ButtonController: Executing knob turn...")
                
                # # Collect data before turning
                # data = self.collect_data(frame_count=10)
                # if data:
                #     # Save collected data
                #     self.save_collected_data(data, 'turn_')
                
                # Get knob angle from latest data
                knob_angle = self._get_latest_knob_angle()
                if knob_angle is None:
                    error_message = "No knob angle data available"
                    return self._return_error_with_reset(BUTTON_DETECTION_ERROR, error_message)
                
                print(f"ButtonController: Got knob angle: {knob_angle:.1f}°")
                
                # Get configuration parameters
                distances = self.robot_config.get_distances()
                movement = self.robot_config.get_movement_parameters()
                
                # Call turn_knob
                turn_knob(
                    self.arm,
                    knob_angle,
                    distances['turn_depth'],
                    v=movement['v'],
                    r=movement['r'],
                    connect=movement['connect'],
                    block=movement['block'],
                    button_handler=self
                )
                
                print("ButtonController: Knob turn completed")
                return (SUCCESS_CODE, "Knob turn completed", {})

            elif action == 'restore':
                # Import restore_pose from button_action
                from buttonControl.button_action import restore_pose
                
                print("ButtonController: Executing restore to initial position...")
                
                # # Collect data before restoring
                # data = self.collect_data(frame_count=10)
                # if data:
                #     # Save collected data
                #     self.save_collected_data(data, 'restore_')
                
                try:
                    # Call restore_pose
                    restore_pose(self.arm)
                    
                    # Update status to uncertain
                    self.set_status('uncertain', 'Restored to initial position')
                    
                    print("ButtonController: Restore completed successfully")
                    return (SUCCESS_CODE, "Restore completed successfully", {})

                except ValueError as e:
                    if "observation joint" in str(e):
                        error_message = "No historical position file found"
                        return self._return_error_with_reset(BUTTON_STATE_ERROR, error_message)
                    else:
                        error_message = f"Restore failed - {e}"
                        return self._return_error_with_reset(BUTTON_OPERATION_ERROR, error_message)

            else:
                error_message = f"Unknown action '{action}'"
                return self._return_error_with_reset(BUTTON_STATE_ERROR, error_message)

        except Exception as e:
            print(f"ButtonController: Error executing action '{action}': {e}")
            print(f"Error details: {traceback.format_exc()}")
            error_message = str(e)
            error_lower = error_message.lower()

            # Determine error code based on error message
            if "emergency stop" in error_lower or "emergency" in error_lower:
                error_code = EMERGENCY_STOP_ERROR
            elif "detection" in error_lower or "detect" in error_lower or "insufficient" in error_lower:
                error_code = BUTTON_DETECTION_ERROR
            elif "approach" in error_lower or "movement" in error_lower or "move" in error_lower:
                error_code = BUTTON_APPROACH_ERROR
            elif "click" in error_lower or "turn" in error_lower:
                error_code = BUTTON_OPERATION_ERROR
            elif "finetune" in error_lower:
                error_code = FINETUNE_ERROR
            elif "state" in error_lower or "status" in error_lower or "invalid" in error_lower:
                error_code = BUTTON_STATE_ERROR
            elif "config" in error_lower:
                error_code = BUTTON_CONFIG_ERROR
            else:
                error_code = BUTTON_OPERATION_ERROR
                error_message = f"Error executing action '{action}': {error_message}"

            # Use unified error return method
            return self._return_error_with_reset(error_code, error_message)

    def _get_current_knob_angle(self) -> float:
        """
        Get the current knob angle from real-time detection.
        Returns None if no angle data is available.
        """
        try:
            if self.camera_instance is None or self.camera_instance.color_image is None:
                print("Warning: No camera pipeline available for knob angle detection")
                return None
            
            # Get current frames
            color_image = self.camera_instance.color_image.copy() if self.camera_instance.color_image is not None else None
            
            # 使用5次快速检测循环提高实时角度检测可靠性
            try:
                from buttonControl.button_detection import detect_buttons_by_status
                import numpy as np

                max_detection_attempts = 5
                detected_angles = []

                for attempt in range(max_detection_attempts):
                    # 获取新的图像帧
                    color_image = self.camera_instance.color_image.copy() if self.camera_instance.color_image is not None else None

                    if color_image is None:
                        continue

                    button_data = detect_buttons_by_status(color_image, status=self.status, verbose=False, display_process=False)

                    if button_data and isinstance(button_data, dict) and button_data.get('success'):
                        targets = button_data.get('targets', {})
                        handle_angle = targets.get('handle_angle')

                        if handle_angle is not None:
                            detected_angles.append(handle_angle)

                    # 快速延迟以保持实时性
                    if attempt < max_detection_attempts - 1:
                        import time
                        time.sleep(0.05)  # 20ms延迟，保持实时性

                if len(detected_angles) >= 3:  # At least 3 out of 5 successful
                    # Outlier filtering: Remove values that differ too much from median
                    median_angle = np.median(detected_angles)
                    filtered_angles = [angle for angle in detected_angles
                                     if abs(angle - median_angle) <= 30]  # 30 degree threshold

                    if filtered_angles:
                        # Use average as final result
                        final_angle = np.mean(filtered_angles)
                        print(f"ButtonController: Real-time knob angle detected: {final_angle:.1f}° (based on {len(filtered_angles)}/{len(detected_angles)} valid detections)")
                        return final_angle
                    else:
                        print(f"Warning: All detected angles were outliers (median: {median_angle:.1f}°)")
                        return median_angle  # Return median as fallback
                else:
                    print(f"Warning: Real-time angle detection failed, only {len(detected_angles)}/5 successful")
                    return None

            except Exception as detection_error:
                print(f"Warning: Could not detect buttons for knob angle: {detection_error}")
                return None
                
        except Exception as e:
            print(f"Warning: Could not get current knob angle: {e}")
            return None

    def _get_latest_knob_angle(self) -> float:
        """
        Get the latest knob angle from global variable.
        Returns None if no angle data is available.
        """
        knob_angle = get_knob_checkpoint()
        if knob_angle is None:
            print("Warning: No knob angle data in global variable")
        else:
            print(f"Read knob angle from global variable: {knob_angle:.1f}°")
        return knob_angle

    def _determine_knob_position_from_angle(self, knob_angle: float) -> str:
        """
        Determine knob position based on angle detection results.
        Uses the same logic as rtBC.py for consistency.
        """
        if knob_angle is None:
            # Default to center if no angle data
            return 'center'

        # Use same logic as rtBC.py
        if knob_angle < -30:
            return 'left'
        elif knob_angle > 30:
            return 'right'
        elif -10 <= knob_angle <= 10:
            return 'center'
        else:
            # For angles between -30 to -10 or 10 to 30, default to center
            return 'center'

    def _determine_knob_position(self) -> str:
        """
        Determine knob position based on detection results.
        This is a fallback method that defaults to center.
        """
        return 'center'

    def _save_coordinate_axes_from_observation(self):
        """
        Save coordinate axes after successful observation approach.
        This simulates the coordinate axes saving that happens in rtBC's main loop.
        """
        try:
            # Get current frames from camera
            if self.camera_instance is not None and self.camera_instance.color_image is not None:
                # frames = self.pipeline.wait_for_frames()
                # depth_frame = frames.get_depth_frame()
                # color_frame = frames.get_color_frame()

                # if depth_frame and color_frame:
                    # Convert to numpy arrays
                depth_image = self.camera_instance.depth_image.copy() if self.camera_instance.depth_image is not None else None # np.asanyarray(depth_frame.get_data())
                color_image = self.camera_instance.color_image.copy() if self.camera_instance.color_image is not None else None # np.asanyarray(color_frame.get_data())

                # Get intrinsics
                color_intrin = self.camera_instance.color_intrin if self.camera_instance.color_intrin is not None else None # color_frame.profile.as_video_stream_profile().intrinsics

                # Detect buttons for coordinate system calculation
                try:
                    # Import button detection
                    from buttonControl.button_detection import detect_buttons_by_status

                    # Detect buttons
                    button_data = detect_buttons_by_status(color_image, status=self.status, verbose=False, display_process=False)

                    if button_data and isinstance(button_data, dict) and button_data.get('success'):
                        targets = button_data.get('targets', {})

                        # 构建 top_row 和 bottom_row 用于坐标系计算
                        top_left = targets.get('top_left_button')
                        top_right = targets.get('top_right_button')
                        bottom_left = targets.get('bottom_left_button')
                        bottom_right = targets.get('bottom_right_button')

                        top_row = [top_left, top_right] if top_left and top_right else None
                        bottom_row = [bottom_left, bottom_right] if bottom_left and bottom_right else None

                        # Check if we have sufficient buttons
                        if (top_row is not None and bottom_row is not None and
                            len(top_row) >= 2 and len(bottom_row) >= 2):

                            # Calculate coordinate system using the same logic as rtBC
                            self._calculate_coordinate_system(
                                top_row, bottom_row, depth_image, color_intrin
                            )

                            print("ButtonController: Coordinate axes saved from observation")
                        else:
                            print("Warning: Insufficient buttons detected for coordinate system calculation")
                    else:
                        print("Warning: Button detection failed during observation")

                except Exception as detection_error:
                    print(f"Warning: Could not detect buttons for coordinate system: {detection_error}")
                # else:
                #     print("Warning: Could not get frames for coordinate system calculation")
            else:
                print("Warning: No camera pipeline available for coordinate system calculation")

        except Exception as e:
            print(f"Warning: Could not save coordinate axes from observation: {e}")

    def _calculate_coordinate_system(self, top_row, bottom_row, depth_image, color_intrin):
        """
        Calculate coordinate system from button positions.
        Uses the same logic as rtBC._save_coordinate_axes_if_observing.
        """
        try:
            # Convert button positions to 3D coordinates
            button_coords_3d = []
            button_labels = []

            # Process detected buttons for coordinate calculation
            for button, label in [(bottom_row[0], "bottom_left"), (bottom_row[1], "bottom_right"),
                                 (top_row[0], "top_left"), (top_row[1], "top_right")]:
                bx, by, br = button
                if 0 <= int(by) < depth_image.shape[0] and 0 <= int(bx) < depth_image.shape[1]:
                    # Use simple depth lookup (could be enhanced with robust estimation)
                    depth_val = depth_image[int(by)][int(bx)]
                    if depth_val is not None and depth_val > 0:
                        point_3d = rs.rs2_deproject_pixel_to_point(
                            color_intrin, (float(bx), float(by)), depth_val / 1000.0
                        )
                        button_coords_3d.append(point_3d)
                        button_labels.append(label)

            # Need exactly 4 buttons to calculate coordinate system
            if len(button_coords_3d) == 4:
                try:
                    # Import button_action for coordinate system calculation
                    from buttonControl.button_action import build_object_coordinate_system

                    # Calculate coordinate system axes
                    x_axis, y_axis, z_axis = build_object_coordinate_system(
                        button_coords_3d[0],  # bottom_left
                        button_coords_3d[1],  # bottom_right
                        button_coords_3d[2],  # top_left
                        button_coords_3d[3],  # top_right
                        robust_normal=True,  # Use robust normal calculation
                        point_cloud=None
                    )

                    # Store the coordinate axes and origin
                    self.last_coordinate_axes = (x_axis, y_axis, z_axis)
                    self.last_observing_frame = 0  # Frame counter not available in button_processor

                    print(f"ButtonController: Coordinate system calculated successfully")

                except Exception as calc_error:
                    print(f"Warning: Could not calculate coordinate system: {calc_error}")
            else:
                print(f"Warning: Need 4 buttons for coordinate system, got {len(button_coords_3d)}")

        except Exception as e:
            print(f"Warning: Error in coordinate system calculation: {e}")

    def _ensure_coordinate_axes_for_finetune(self):
        """
        Ensure that coordinate axes are available for finetune operation.
        In the refactored version, coordinate axes are stored directly in ButtonController.
        """
        if self.last_coordinate_axes is not None:
            print("ButtonController: Coordinate axes available for finetune")
            print(f"  Axes: X={self.last_coordinate_axes[0]}, Y={self.last_coordinate_axes[1]}, Z={self.last_coordinate_axes[2]}")
        else:
            print("Warning: No coordinate axes available for finetune")

    def _get_ready_status_from_touching(self) -> str:
        """
        Convert touching_* status to ready_* status after finetune.
        """
        current_status = self.status
        if current_status == 'touching_left_button':
            return 'ready_left_button'
        elif current_status == 'touching_right_button':
            return 'ready_right_button'
        elif current_status.startswith('touching_knob_'):
            knob_position = current_status.replace('touching_knob_', '')
            return f'ready_knob_{knob_position}'
        else:
            # Fallback
            return 'ready'
    
    def run(self, params: dict) -> tuple:
        """
        Execute button task.
        Args:
            params: Task parameters from client.py
        Returns:
            Tuple of (error_code, result_data)
        """
        if self.arm is None:
            return self._return_error_with_reset(ARM_STATE_ERROR, 'Robotic arm not initialized')
        if self.emergency_stopped:
            # Emergency stop should not trigger reset
            return (EMERGENCY_STOP_ERROR, {
                'error_message': 'Emergency stop active'
            })
        try:
            print(f"ButtonController: Starting button task with params: {params}")
            action = params.get('action', '')
            if not action:
                return self._return_error_with_reset(BUTTON_STATE_ERROR, 'No action specified in parameters')
            print(f"ButtonController: Executing action '{action}'")
            validation_result = self._validate_action(action)
            if validation_result is not None:
                return validation_result
            # 处理checkpoint/ checkpoint_bool
            extra_params = {}
            if 'checkpoint' in params:
                extra_params['checkpoint'] = params['checkpoint']
            # checkpoint_bool用于后续返回
            checkpoint_bool = params.get('checkpoint_bool', False)
            # 执行action
            try:
                result_code, result_message, extra_data = self._execute_action(action, extra_params)
                if self.emergency_stopped:
                    return (EMERGENCY_STOP_ERROR, {
                        'error_message': 'Emergency stop during execution'
                    })
                if result_code != SUCCESS_CODE:
                    return (result_code, {
                        'error_message': result_message
                    })
                print(f"ButtonController: Action {action} execution completed")
            except Exception as e:
                error_message = f"Button action execution error: {str(e)}"
                print(error_message)
                return (BUTTON_OPERATION_ERROR, {
                    'error_message': error_message
                })
            # 构造返回内容
            result = {
                'error_message': f'Action {action} completed successfully',
                'action': action,
                'params_used': params,
                'final_status': self.status,
                'status_history': self.states[-5:]  # Last 5 status changes
            }
            # 若checkpoint_bool为True，且有self._last_search_checkpoint，返回
            if checkpoint_bool and self._last_search_checkpoint is not None:
                result['checkpoint'] = self._last_search_checkpoint
            # 若extra_data中有checkpoint，也返回
            if 'checkpoint' in extra_data and extra_data['checkpoint'] is not None:
                result['checkpoint'] = extra_data['checkpoint']
            return (SUCCESS_CODE, result)
        except Exception as e:
            error_message = f"Button task execution error: {str(e)}\n{traceback.format_exc()}"
            print(error_message)
            return (UNKNOWN_ERROR, {
                'error_message': error_message
            })
    
    def emergency_stop(self):
        """
        Handle emergency stop command.
        Simplified version based on pole_processor.py implementation.

        Immediately stops all button operations and sets status to 'stopped'.
        """
        print("ButtonController: Emergency stop activated")
        self.emergency_stopped = True
        self.set_status('stopped', 'Emergency stop triggered')

        # Immediately stop arm movement
        if self.arm is not None:
            try:
                print("ButtonController: Stopping arm movement immediately")
                self.arm.rm_set_arm_stop()
                self.arm.rm_set_arm_delete_trajectory()
            except Exception as arm_stop_error:
                print(f"Error stopping arm during emergency stop: {arm_stop_error}")

        print("ButtonController: Emergency stop completed")
    
    def reset_emergency_stop(self):
        """Reset emergency stop state."""
        print("ButtonController: Emergency stop reset")
        self.emergency_stopped = False
    

    def smart_wait_for_arm_stable(self, max_wait_time: float = 1.0, check_interval: float = 0.1,
                             position_threshold: float = 0.001) -> bool:
        """
        智能等待机械臂稳定，基于位置变化检查而非固定时间等待

        Args:
            max_wait_time: 最大等待时间（秒）
            check_interval: 检查间隔（秒）
            position_threshold: 位置变化阈值（米）

        Returns:
            bool: True表示机械臂已稳定，False表示超时或急停
        """
        import time
        import math

        start_time = time.time()
        last_pose = None
        stable_count = 0
        required_stable_checks = 3  # 需要连续3次检查都稳定

        while time.time() - start_time < max_wait_time:
            # 检查急停
            if self.emergency_stopped:
                return False

            try:
                # 获取当前位姿
                ret_msg = self.arm.rm_get_current_arm_state()
                if ret_msg[0] != 0:
                    time.sleep(check_interval)
                    continue

                current_pose = np.array(ret_msg[1]['pose'])

                if last_pose is not None:
                    # 计算位置变化
                    pos_change = np.linalg.norm(current_pose[:3] - last_pose[:3])

                    if pos_change < position_threshold:
                        stable_count += 1
                        if stable_count >= required_stable_checks:
                            return True
                    else:
                        stable_count = 0

                last_pose = current_pose
                time.sleep(check_interval)

            except Exception as e:
                # 如果状态检查失败，回退到短暂等待
                time.sleep(check_interval)

        return False  # 超时

    def _wait_for_arm_stop(self, timeout=60.0, max_state_retry=160, max_trajectory_retry=240, verbose=True):
        """
        等待机械臂运动结束，支持急停检测。
        Args:
            timeout: 最大等待时间（秒）
            max_state_retry: 获取状态最大重试次数
            max_trajectory_retry: 运动未结束最大重试次数
            verbose: 是否打印日志
        Returns:
            0 表示运动已结束
            ARM_STATE_ERROR 表示超时或状态异常
            EMERGENCY_STOP_ERROR 表示急停
        """
        # print('debug | start waiting')
        import time
        start_time = time.time()
        trajectory_count = 0
        while True:
            if self.emergency_stopped:
                self._error_message = '[EMERGENCY_STOP]'
                if verbose:
                    print('ButtonController: Emergency stop detected during wait for arm stop')
                return EMERGENCY_STOP_ERROR
            if trajectory_count > max_trajectory_retry or (time.time() - start_time) > timeout:
                self._error_message = "Timeout error in checking arm state (wait_for_arm_stop)"
                if verbose:
                    print('ButtonController: Timeout waiting for arm to stop')
                return ARM_STATE_ERROR
            trajectory_count += 1
            current_trajectory_state = self.arm.rm_get_arm_current_trajectory()
            cur_err_count = 0
            while current_trajectory_state['return_code'] != 0:
                if self.emergency_stopped:
                    self._error_message = '[EMERGENCY_STOP]'
                    if verbose:
                        print('ButtonController: Emergency stop detected during wait for arm stop (state retry)')
                    return EMERGENCY_STOP_ERROR
                if cur_err_count > max_state_retry:
                    self._error_message = f'[Failed to get arm state after multiple attempts: {current_trajectory_state}]'
                    if verbose:
                        print('ButtonController: Too many errors getting arm state')
                    return ARM_STATE_ERROR
                cur_err_count += 1
                if verbose and cur_err_count > 80:
                    print(f'- [{cur_err_count}]Error in get current state: {current_trajectory_state}')
                time.sleep(0.2)
                current_trajectory_state = self.arm.rm_get_arm_current_trajectory()
            if current_trajectory_state['trajectory_type'] == 0:
                break
            time.sleep(0.25)
            if verbose and trajectory_count > 100:
                print(f'[{trajectory_count}] trajectory not stopped yet', current_trajectory_state)
        # print('debug | end waiting')
        return 0

    def move_arm(self, move_type, args):
        """
        Unified arm movement method with emergency stop checking.
        Simplified version based on pole_processor.py implementation.

        Args:
            move_type: Type of movement ('rm_movej', 'rm_movej_p', 'rm_movel', 'rm_movej_canfd')
            args: Tuple of arguments for the movement function

        Returns:
            Error code (SUCCESS_CODE if successful, EMERGENCY_STOP_ERROR if stopped)
        """
        if self.emergency_stopped:
            self._error_message = f'[EMERGENCY_STOP]'
            return EMERGENCY_STOP_ERROR

        if self.arm is None:
            self._error_message = f'[ARM_NOT_AVAILABLE]'
            return ARM_STATE_ERROR

        # Check if arm is currently moving and stop if necessary
        try:
            current_trajectory_state = self.arm.rm_get_arm_current_trajectory()
            if current_trajectory_state['return_code'] == 0 and current_trajectory_state['trajectory_type'] != 0:
                print("ButtonController: Stopping current trajectory before new movement")
                self.arm.rm_set_arm_stop()
                self.arm.rm_set_arm_delete_trajectory()
                import time
                time.sleep(0.2)
        except Exception as e:
            print(f"Warning: Could not check/stop current trajectory: {e}")

        # 等待上一个运动结束
        wait_ret = self._wait_for_arm_stop()
        if wait_ret != 0:
            return wait_ret

        # 等待位姿稳定（前置）
        stable = self.smart_wait_for_arm_stable(max_wait_time=1.0, check_interval=0.1, position_threshold=0.001)
        if not stable:
            self._warn_message = '[Pose not stable before move]'
            print(f'ButtonController: {self._warn_message}')
            

        ret_code = 0

        try:
            if move_type == 'rm_movej':
                ret = self.arm.rm_movej(args[0], args[1], args[2], args[3], args[4])
                if ret != 0 and ret != -3:
                    ret_code = ARM_MOVEMENT_ERROR
                    self._error_message = f'[rm_movej{(args[0], args[1], args[2], args[3], args[4])}: {ret}]'
                if ret == -3:
                    ret_3_count = 0
                    while ret == -3:
                        if self.emergency_stopped:
                            print('[[EMERGENCY_STOP]]')
                            self._error_message = f'[EMERGENCY_STOP]'
                            return EMERGENCY_STOP_ERROR
                        if ret_3_count > 50:
                            print("-3:", ret_3_count, ret, (args[0], args[1], args[2], args[3], args[4]))
                        if ret_3_count > 100:
                            break
                        import time
                        time.sleep(0.1)
                        ret_3_count += 1
                        ret = self.arm.rm_movej(args[0], args[1], args[2], args[3], args[4])

            elif move_type == 'rm_movej_p':
                ret = self.arm.rm_movej_p(args[0], args[1], args[2], args[3], args[4])
                if ret != 0 and ret != -3:
                    ret_code = ARM_MOVEMENT_ERROR
                    self._error_message = f'[rm_movej_p{(args[0], args[1], args[2], args[3], args[4])}: {ret}]'
                if ret == -3:
                    ret_3_count = 0
                    while ret == -3:
                        if self.emergency_stopped:
                            print('[[EMERGENCY_STOP]]')
                            self._error_message = f'[EMERGENCY_STOP]'
                            return EMERGENCY_STOP_ERROR
                        if ret_3_count > 50:
                            print("-3:", ret_3_count, ret, (args[0], args[1], args[2], args[3], args[4]))
                        if ret_3_count > 100:
                            break
                        import time
                        time.sleep(0.1)
                        ret_3_count += 1
                        ret = self.arm.rm_movej_p(args[0], args[1], args[2], args[3], args[4])

            elif move_type == 'rm_movel':
                ret = self.arm.rm_movel(args[0], args[1], args[2], args[3], args[4])
                if ret != 0 and ret != -3:
                    ret_code = ARM_MOVEMENT_ERROR
                    self._error_message = f'[rm_movel{(args[0], args[1], args[2], args[3], args[4])}: {ret}]'
                if ret == -3:
                    ret_3_count = 0
                    while ret == -3:
                        if self.emergency_stopped:
                            print('[[EMERGENCY_STOP]]')
                            self._error_message = f'[EMERGENCY_STOP]'
                            return EMERGENCY_STOP_ERROR
                        if ret_3_count > 50:
                            print("-3:", ret_3_count, ret, (args[0], args[1], args[2], args[3], args[4]))
                        if ret_3_count > 100:
                            break
                        import time
                        time.sleep(0.1)
                        ret_3_count += 1
                        ret = self.arm.rm_movel(args[0], args[1], args[2], args[3], args[4])

            # elif move_type == 'rm_movej_canfd':
            #     ret = self.arm.rm_movej_canfd(args[0], args[1], args[2], args[3], args[4])
            #     if ret != 0:
            #         ret_code = ARM_MOVEMENT_ERROR
            #         self._error_message = f'[rm_movej_canfd{(args[0], args[1], args[2], args[3], args[4])}: {ret}]'
            else:
                ret_code = UNKNOWN_ERROR
                self._error_message = '[Unknown move type]'

        except Exception as e:
            ret_code = ARM_MOVEMENT_ERROR
            self._error_message = f'[{move_type} exception: {str(e)}]'

        # 等待本次运动结束
        wait_ret = self._wait_for_arm_stop()
        if wait_ret != 0:
            return wait_ret

        # 等待位姿稳定（后置）
        stable = self.smart_wait_for_arm_stable(max_wait_time=1.0, check_interval=0.1, position_threshold=0.001)
        if not stable:
            self._warn_message = '[Pose not stable after move]'
            print(f'ButtonController: {self._warn_message}')

        # Final emergency stop check (like pole_processor)
        if self.emergency_stopped:
            self._error_message = f'[EMERGENCY_STOP]'
            return EMERGENCY_STOP_ERROR

        return ret_code
    
    def reset(self) -> int:
        """
        Reset button handler to initial state.
        Enhanced with trajectory_break_count tolerance, based on pole_processor.py.

        Returns:
            Error code (SUCCESS_CODE if successful)
        """
        try:
            print("ButtonController: Starting reset")

            # Reset basic flags
            self.emergency_stopped = False

            # Reset arm state if available
            if self.arm is not None:
                try:
                    # Stop any current movement first
                    print("ButtonController: Stopping current arm movement")
                    self.arm.rm_set_arm_stop()
                    self.arm.rm_set_arm_delete_trajectory()
                    import time
                    time.sleep(0.2)

                    # Clear system errors (like pole_processor.py)
                    current_state = self.arm.rm_get_current_arm_state()
                    current_state = self.arm.rm_get_current_arm_state()
                    reset_count, reset_count_max = 0, 50
                    while current_state[0] != 0 or current_state[1]['err']['err'] != ['0']:
                        if reset_count > reset_count_max:
                            print(f"Warning: Cannot clear system error after {reset_count_max} attempts")
                            self._error_message = f'[Cannot clear system error: {current_state}]'
                            self.set_status('uncertain', 'Reset completed but failed to clear system error')
                            return ARM_STATE_ERROR
                        if self.emergency_stopped:
                            print("ButtonController: Emergency stop detected during reset")
                            return EMERGENCY_STOP_ERROR
                        time.sleep(0.1)
                        reset_count += 1
                        ret = self.arm.rm_clear_system_err()
                        if ret != 0:
                            continue
                        time.sleep(0.4)
                        current_state = self.arm.rm_get_current_arm_state()
                        current_state = self.arm.rm_get_current_arm_state()

                    print("ButtonController: Arm system errors cleared")

                    # Move to zero pose
                    ret = self.move_to_zero_pose()
                    if ret == 0:
                        self._error_message = ""
                        self.set_status('uncertain', 'Reset completed and moved to zero pose')
                    else:
                        print(f"Warning: move_to_zero_pose failed with code {ret}")
                        self._error_message = f"[move_to_zero_pose failed: {ret}]"
                        self.set_status('uncertain', 'Reset completed but failed to move to zero pose')

                except Exception as e:
                    print(f"Warning: Error during arm reset: {e}")
                    self._error_message = f"[Exception during arm reset: {e}]"
                    self.set_status('uncertain', 'Reset completed but exception during arm reset')
            else:
                # No arm, just reset state
                self.set_status('uncertain', 'Reset completed (no arm)')

            # --- Trajectory break count tolerance (from pole_processor.py) ---
            import time
            if self.arm is not None:
                trajectory_count = 0
                trajectory_break_count = 0
                while True:
                    if self.emergency_stopped:
                        print('[[EMERGENCY_STOP--during trajectory check]]')
                        self._error_message = f'[EMERGENCY_STOP--during trajectory check]'
                        return EMERGENCY_STOP_ERROR
                    if trajectory_count > 240:
                        self._error_message = "Timeout error in checking arm state"
                        return ARM_TRAJECTORY_TIMEOUT_ERROR
                    trajectory_count += 1
                    current_trajectory_state = self.arm.rm_get_arm_current_trajectory()
                    cur_err_count = 0
                    while current_trajectory_state['return_code'] != 0:
                        if cur_err_count > 160:
                            self._error_message = f'[Failed to get arm state after multiple attempts: {current_trajectory_state}]'
                            return ARM_STATE_ERROR
                        cur_err_count += 1
                        if cur_err_count > 80:
                            print(f'- [{cur_err_count}]Error in get current state: {current_trajectory_state}')
                        time.sleep(0.2)
                        current_trajectory_state = self.arm.rm_get_arm_current_trajectory()
                    if current_trajectory_state['trajectory_type'] == 0:
                        trajectory_break_count += 1
                        if trajectory_break_count > 3:
                            break
                        time.sleep(0.1)
                        continue
                    else:
                        trajectory_break_count = 0
                    time.sleep(0.25)
                    if trajectory_count > 100:
                        print(f'[{trajectory_count}] trajectory not stopped yet', current_trajectory_state)
                self.arm.rm_get_current_arm_state()
            # --- End trajectory break count tolerance ---

            # Reset internal state variables
            self.states = []
            self.last_coordinate_axes = None
            self.last_coordinate_origin = None
            self.last_observing_frame = None

            print("ButtonController: Reset completed successfully")
            return SUCCESS_CODE

        except Exception as e:
            print(f"Critical reset error: {str(e)}")
            print(f"Error details: {traceback.format_exc()}")
            return UNKNOWN_ERROR
    
    def move_to_zero_pose(self):
        """
        Move the robot arm to the zero position safely.
        Simplified version based on pole_processor.py implementation.

        Returns:
            0 on success, error code on failure
        """
        import time
        try:
            print("ButtonController: Starting move_to_zero_pose")

            if self.arm is None:
                self._error_message = '[ARM_NOT_AVAILABLE]'
                print("ButtonController: Arm not available for move_to_zero_pose")
                return ARM_STATE_ERROR

            # Check gripper state (like pole_processor.py)
            gripper_ret = self.arm.rm_get_gripper_state()
            if gripper_ret[0] != 0 or gripper_ret[1]['error'] != 0:
                print("ButtonController: Error getting gripper state!")
                self._error_message = f'[{gripper_ret}]'
                return GRIPPER_STATE_ERROR
                
            if 400 < gripper_ret[1]['actpos'] < 990:
                # Gripper is grabbing something (not applicable for button operations)
                print("ButtonController: Gripper is grabbing - releasing first")
                ret = self.arm.rm_set_gripper_release(150, True, 10)
                if ret != 0:
                    self._error_message = f'[{ret}]'
                    return GRIPPER_CONTROL_ERROR
            else:
                # Gripper is not grabbing or is releasing
                if gripper_ret[1]['actpos'] <= 400:
                    print("ButtonController: Gripper is not grabbing")
                    ret = self.arm.rm_set_gripper_release(150, True, 10)
                    if ret != 0:
                        self._error_message = f'[{ret}]'
                        return GRIPPER_CONTROL_ERROR
                else:
                    print("ButtonController: Gripper is releasing")

            # Get current arm state
            current_state = self.arm.rm_get_current_arm_state()
            cur_err_count = 0
            while current_state[0] != 0:
                if cur_err_count > 20:
                    self._error_message = f'[Failed to get arm state after multiple attempts: {current_state}]'
                    return ARM_STATE_ERROR
                cur_err_count += 1
                print('ButtonController: Error in get current state')
                time.sleep(0.8)
                current_state = self.arm.rm_get_current_arm_state()

            # Get zero pose from configuration (same as pole_processor.py)
            zero_joints = self.robot_config.zero_pose
            print(f"ButtonController: Moving to configured zero position: {zero_joints}")

            # Move to zero position with safe speed
            ret = self.move_arm('rm_movej', (zero_joints, 20, 50, 0, 1))
            if ret != SUCCESS_CODE:
                self._error_message = f'[Failed to move to zero position: {ret}]'
                return ret

            # Wait for movement to complete (simplified like pole_processor.py)
            trajectory_count = 0
            while True:
                if trajectory_count > 240:  # Increased timeout like pole_processor.py
                    self._error_message = "Timeout error in checking arm state"
                    return ARM_STATE_ERROR
                if self.emergency_stopped:
                    print("ButtonController: Emergency stop detected during trajectory wait")
                    return EMERGENCY_STOP_ERROR
                trajectory_count += 1
                try:
                    current_trajectory_state = self.arm.rm_get_arm_current_trajectory()
                    cur_err_count = 0
                    while current_trajectory_state['return_code'] != 0:
                        if cur_err_count > 160:  # Increased error count like pole_processor.py
                            self._error_message = f'[Failed to get arm state after multiple attempts: {current_trajectory_state}]'
                            return ARM_STATE_ERROR
                        cur_err_count += 1
                        print(f'ButtonController: - [{cur_err_count}]Error in get current state: {current_trajectory_state}')
                        time.sleep(0.2)
                        current_trajectory_state = self.arm.rm_get_arm_current_trajectory()
                    if current_trajectory_state['trajectory_type'] == 0:
                        break
                    time.sleep(0.25)
                    print(f'ButtonController: [{trajectory_count}] trajectory not stopped yet', current_trajectory_state)
                except Exception as e:
                    print(f'ButtonController: Error checking trajectory state: {e}')
                    time.sleep(0.2)

            print("ButtonController: move_to_zero_pose completed successfully")
            return 0

        except Exception as e:
            error_msg = f'move_to_zero_pose error: {str(e)}'
            print(error_msg)
            self._error_message = f'[{error_msg}]'
            return UNKNOWN_ERROR
    
    def get_status(self) -> Dict[str, Any]:
        """Get current status of button handler."""
        return {
            'emergency_stopped': self.emergency_stopped,
            'arm_available': self.arm is not None,
            'camera_available': self.camera_instance is not None and self.camera_instance.color_image is not None,
            'modules_available': BUTTON_MODULES_AVAILABLE,
            'current_status': self.status,
            'status_history_length': len(self.states),
            'last_5_states': self.states[-5:] if len(self.states) >= 5 else self.states
        }

    def _map_high_level_action(self, action_name: str) -> list:
        """
        Map high-level action names to low-level action sequences.
        
        Args:
            action_name: High-level action name
            
        Returns:
            List of low-level actions to execute
        """
        action_mappings = {
            "switch_manual": [
                "find_buttons",
                "observe_buttons",
                "touch_knob",
                "finetune",
                "turn_knob",
                "restore"
            ],
            "turn_on": [
                "observe_buttons",
                "touch_left_button",
                "finetune",
                "click_button",
                "restore"
            ],
            "turn_off": [
                "observe_buttons",
                "touch_right_button",
                "finetune",
                "click_button",
                "restore"
            ],
            "switch_automatic": [
                "observe_buttons",
                "touch_knob",
                "finetune",
                "turn_knob",
                "restore"
            ]
        }
        
        return action_mappings.get(action_name, [action_name])

    def execute_continuous_sequence(self, params=None) -> Tuple[int, Dict[str, Any]]:
        """
        Execute a continuous sequence of button operations.

        Args:
            params: Parameters dict containing:
                - operating: List of operations to perform (optional)
                - checkpoint: List of floats for known_orientation (optional)
                - checkpoint_bool: Boolean to return checkpoint in response (optional)

        Returns:
            Tuple of (error_code, result_data)
        """

        print('Reset before action sequence')
        try:
            reset_code = self.reset()
        except Exception as e:
            print(f"ButtonHandler: Error during reset: {e}")
            return (ARM_STATE_ERROR, {
                'error_message': f'Reset before sequence failed: {e}',
                'init_reset_code': -9999,
                'init_reset_message': f'Reset before sequence failed: {e}'
            })

        # Handle both old and new calling conventions
        if params is None:
            params = {}
        elif isinstance(params, list):
            # Old calling convention: params is actually operating_sequence
            params = {"operating": params}

        # Extract parameters
        operating_sequence = params.get("operating", None)
        checkpoint = params.get("checkpoint", None)
        checkpoint_bool = params.get("checkpoint_bool", False)

        if operating_sequence is None:
            # Default sequence: find_buttons -> observe_buttons -> touch_knob -> finetune -> turn_knob -> restore -> 
            # observe_buttons -> touch_left_button -> finetune -> click_button -> restore -> 
            # observe_buttons -> touch_right_button -> finetune -> click_button -> restore -> 
            # observe_buttons -> touch_knob -> finetune -> turn_knob -> restore
            operating_sequence = [
                "find_buttons",
                "observe_buttons", 
                "observe_buttons",
                "touch_knob",
                "finetune",
                "turn_knob", 
                "restore",
                "observe_buttons",
                "touch_left_button",
                "finetune",
                "click_button",
                "restore",
                "observe_buttons",
                "touch_right_button", 
                "finetune",
                "click_button",
                "restore",
                "observe_buttons",
                "touch_knob",
                "finetune",
                "turn_knob",
                "restore"
            ]
        
        # Expand high-level actions to low-level sequences
        expanded_sequence = []
        for action in operating_sequence:
            if action in ["switch_manual", "turn_on", "turn_off", "switch_automatic"]:
                # Map high-level action to low-level sequence
                low_level_actions = self._map_high_level_action(action)
                expanded_sequence.extend(low_level_actions)
                print(f"ButtonController: Mapped '{action}' to {low_level_actions}")
            else:
                # Keep low-level action as is
                expanded_sequence.append(action)
        
        print(f"ButtonController: Starting continuous sequence with {len(expanded_sequence)} operations (expanded from {len(operating_sequence)} high-level actions)")
        
        # Track progress for reporting
        completed_operations = []
        sequence_checkpoint = None  # Store checkpoint from find_buttons operation
        
        # --- New: Special handling for high-level actions ---

        is_first_knob = True
        skip_next_finetune = False  # Track if we should skip the next finetune operation
        skip_next_turn_knob = False  # Track if we should skip the next turn_knob operation
        skip_next_restore = False  # Track if we should skip the next restore operation
        for i, action in enumerate(expanded_sequence):
            # Check for emergency stop before each operation
            if self.emergency_stopped:
                print("ButtonController: Emergency stop detected during continuous sequence")
                return (EMERGENCY_STOP_ERROR, {
                    'error_message': 'Emergency stop during continuous sequence',
                    'completed_operations': completed_operations,
                    'failed_operation': action,
                    'operation_index': i
                })
            print(f"ButtonController: Executing operation {i+1}/{len(expanded_sequence)}: {action}")

            # Check if we should skip this specific operation
            if action == "finetune" and skip_next_finetune:
                print(f"ButtonController: Skipping finetune after first knob turn was skipped")
                skip_next_finetune = False  # Reset the flag
                completed_operations.append(action)
                continue
            if action == "turn_knob" and skip_next_turn_knob:
                print(f"ButtonController: Skipping turn_knob after first knob turn was skipped")
                skip_next_turn_knob = False  # Reset the flag
                completed_operations.append(action)
                continue
            if action == "restore" and skip_next_restore:
                print(f"ButtonController: Skipping first restore after first knob turn was skipped")
                skip_next_restore = False  # Reset the flag
                completed_operations.append(action)
                continue
                
            if action == "touch_knob":
                knob_angle = self._get_current_knob_angle()
                if knob_angle is not None:
                    knob_position = self._determine_knob_position_from_angle(knob_angle)
                    print(f"ButtonController: Current knob position: {knob_position}, angle: {knob_angle:.1f}°")

                    # For first touch_knob in this sequence, expect right position
                    if is_first_knob:  # First touch_knob operation in this sequence
                        is_first_knob = False

                        if knob_position == 'right':
                            # Set flags to skip the next finetune, turn_knob, and restore operations
                            skip_next_finetune = True
                            skip_next_turn_knob = True
                            skip_next_restore = True
                            print(f"ButtonController: Skipping first knob turn (knob already in 'right' position)")
                            completed_operations.append(action)  # Mark as completed since we're skipping it
                            continue
                    else:  # Second touch_knob operation in this sequence
                        if knob_position != 'right':
                            error_msg = f"Knob position is {knob_position}, expected 'MANNUAL' (angle: {knob_angle:.1f}°)"
                            print(f"ButtonController: {error_msg}")
                            return self._return_error_with_reset(BUTTON_DETECTION_ERROR, error_msg, {
                                'completed_operations': completed_operations,
                                'failed_operation': action,
                                'operation_index': i,
                                'knob_angle': knob_angle,
                                'knob_position': knob_position,
                            })
                else:
                    print("ButtonController: Warning - No real-time knob angle data available for validation")
            
            # Prepare extra_params for this action
            extra_params = {}
            if action == 'find_buttons':
                if checkpoint_bool:
                    extra_params['force_search'] = True
                else:
                    if checkpoint is not None:
                        extra_params['checkpoint'] = checkpoint

            # Execute the action
            try:
                result_code, result_message, extra_data = self._execute_action(action, extra_params=extra_params)
                if action == 'find_buttons' and extra_data and 'checkpoint' in extra_data:
                    sequence_checkpoint = extra_data['checkpoint']
                if result_code != SUCCESS_CODE:
                    print(f"ButtonController: Operation '{action}' failed: {result_message}")
                    return self._return_error_with_reset(result_code, f"Operation '{action}' failed: {result_message}", {
                        'completed_operations': completed_operations,
                        'failed_operation': action,
                        'operation_index': i
                    })
                completed_operations.append(action)
                print(f"ButtonController: Operation '{action}' completed successfully")
                if i < len(expanded_sequence) - 1:
                    import time
                    time.sleep(0.2)
            except Exception as e:
                error_msg = f"Exception during operation '{action}': {str(e)}"
                print(f"ButtonController: {error_msg}")
                return self._return_error_with_reset(BUTTON_OPERATION_ERROR, error_msg, {
                    'completed_operations': completed_operations,
                    'failed_operation': action,
                    'operation_index': i
                })
        print(f"ButtonController: Continuous sequence completed successfully with {len(completed_operations)} operations")
        result_data = {
            'error_message': f'Continuous sequence completed successfully',
            'completed_operations': completed_operations,
            'total_operations': len(expanded_sequence),
            'original_sequence': operating_sequence,
        }
        if checkpoint_bool:
            if sequence_checkpoint is not None:
                result_data['checkpoint'] = sequence_checkpoint
                print(f"ButtonController: Adding checkpoint to response: {sequence_checkpoint}")
            else:
                print("ButtonController: Warning - checkpoint_bool is True but no checkpoint was found")
        # When checkpoint_bool is False, don't add checkpoint field
        # --- New: Safe execution of reset ---
        try:
            reset_code = self.reset()
            result_data['final_reset_code'] = reset_code
            if reset_code == 0:
                result_data['final_reset_message'] = 'Reset after sequence completed successfully'
            else:
                result_data['final_reset_message'] = f'Reset after sequence failed with code {reset_code}'
        except Exception as e:
            result_data['final_reset_code'] = -9999
            result_data['final_reset_message'] = f'Reset after sequence raised exception: {e}'
        return (SUCCESS_CODE, result_data)

    def _collect_action_data(self, action_name):
        """统一的数据收集方法"""
        if self.data_manager is not None:
            try:
                return self.data_manager.collect_data()
            except Exception as e:
                print(f"DataManager collection failed: {e}")
                return self._fallback_data_collection()
        else:
            return self._fallback_data_collection()

    def _fallback_data_collection(self):
        """简单的数据收集降级方案"""
        data = {}
        if self.camera_instance:
            data['color_image'] = self.camera_instance.color_image
            data['depth_image'] = self.camera_instance.depth_image
            data['color_intrin'] = self.camera_instance.color_intrin

        # 添加空的兼容性字段
        data['button_coords'] = []
        data['button_labels'] = []
        data['knob_coord'] = None
        data['knob_angle'] = None
        data['valid_frames'] = 0
        data['point_cloud'] = None
        data['pose'] = None
        data['joint'] = None

        # 添加时间戳
        from datetime import datetime
        data['timestamp'] = datetime.now().strftime("%Y%m%d_%H%M%S_%f")

        return data

    def collect_data(self, frame_count=None):
        """
        统一的数据收集方法，通过data_manager获取检测结果
        
        Args:
            frame_count: 采集帧数，None时使用默认值
            
        Returns:
            dict: 统一格式的检测数据
        """
        if self.data_manager is not None:
            try:
                return self.data_manager.collect_data(frame_count or 5)
            except Exception as e:
                print(f"DataManager collection failed: {e}")
                return self._fallback_data_collection()
        else:
            return self._fallback_data_collection()
