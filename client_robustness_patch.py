#!/usr/bin/env python3
"""
Client.py 健壮性改进补丁
这个文件包含了对 client.py 的关键改进，防止任务异常导致整个系统卡死
"""

import signal
import threading
import traceback
import time
from concurrent.futures import ThreadPoolExecutor, TimeoutError
from utils import *

# 错误代码常量
SUCCESS_CODE = 0
GENERAL_ERROR = 9999
ARM_STATE_ERROR = 1001
PARSING_ERROR = 6001

def safe_execute_task(task_func, task_name, params, timeout=300):
    """
    安全执行任务的包装器
    
    Args:
        task_func: 要执行的任务函数
        task_name: 任务名称（用于日志）
        params: 任务参数
        timeout: 超时时间（秒）
    
    Returns:
        Tuple[int, Dict]: (error_code, result_data)
    """
    print(f"[SafeExecute] Starting task: {task_name} with timeout: {timeout}s")
    
    try:
        # 使用线程池执行任务以支持超时
        with ThreadPoolExecutor(max_workers=1) as executor:
            future = executor.submit(task_func, params)
            result = future.result(timeout=timeout)
        
        # 验证返回值格式
        if not isinstance(result, (tuple, list)) or len(result) < 2:
            print(f"[SafeExecute] Warning: {task_name} returned invalid format: {result}")
            return (GENERAL_ERROR, {
                'error_message': f'Task {task_name} returned invalid format',
                'original_result': str(result),
                'task_name': task_name
            })
        
        error_code, result_data = result[0], result[1]
        
        # 确保 result_data 是字典
        if not isinstance(result_data, dict):
            print(f"[SafeExecute] Converting non-dict result_data to dict: {type(result_data)}")
            result_data = {
                'error_message': str(result_data),
                'original_type': type(result_data).__name__,
                'task_name': task_name
            }
        
        # 确保错误情况下有 error_message
        if error_code != 0 and 'error_message' not in result_data:
            result_data['error_message'] = f'Task {task_name} failed with code {error_code}'
        
        print(f"[SafeExecute] Task {task_name} completed with code: {error_code}")
        return (error_code, result_data)
        
    except TimeoutError as e:
        print(f"[SafeExecute] Task {task_name} timed out: {e}")
        return (GENERAL_ERROR, {
            'error_message': f'Task {task_name} timed out after {timeout} seconds',
            'timeout': timeout,
            'task_name': task_name,
            'error_type': 'timeout'
        })
    
    except Exception as e:
        print(f"[SafeExecute] Task {task_name} failed with exception: {e}")
        print(f"[SafeExecute] Exception details: {traceback.format_exc()}")
        return (GENERAL_ERROR, {
            'error_message': f'Task {task_name} failed: {str(e)}',
            'exception_type': type(e).__name__,
            'traceback': traceback.format_exc(),
            'task_name': task_name,
            'error_type': 'exception'
        })


def safe_send_response(response):
    """
    安全发送响应的包装器
    
    Args:
        response: 要发送的响应字典
    
    Returns:
        bool: 发送是否成功
    """
    try:
        # 验证响应格式
        if not isinstance(response, dict):
            print(f"[SafeSend] Error: Response is not a dict: {type(response)}")
            # 尝试转换为字典格式
            response = {
                "code": GENERAL_ERROR,
                "status": "error",
                "command": "unknown",
                "message": f"Invalid response format: {type(response).__name__}",
                "data": {"original_response": str(response)}
            }
            print(f"[SafeSend] Converted to dict format: {response}")
            # 继续处理而不是直接返回False
        
        # 确保必要字段存在
        required_fields = ['code', 'status', 'command', 'message']
        for field in required_fields:
            if field not in response:
                print(f"[SafeSend] Warning: Missing required field '{field}', adding default")
                response[field] = 'unknown' if field != 'code' else GENERAL_ERROR
        
        # 确保 data 字段是字典
        if 'data' in response and not isinstance(response['data'], dict):
            print(f"[SafeSend] Converting non-dict data field: {type(response['data'])}")
            response['data'] = {'original_data': str(response['data'])}
        
        # 调用原始的 send_response 函数
        return send_response(response)
        
    except Exception as e:
        print(f"[SafeSend] Error in safe_send_response: {e}")
        print(f"[SafeSend] Exception details: {traceback.format_exc()}")
        
        # 尝试发送错误响应
        try:
            error_response = {
                "code": GENERAL_ERROR,
                "status": "error",
                "command": "unknown",
                "task": "unknown",
                "message": f"Response sending error: {str(e)}",
                "data": {
                    'error_message': str(e),
                    'exception_type': type(e).__name__
                }
            }
            return send_response(error_response)
        except:
            print("[SafeSend] Failed to send error response")
            return False


def improved_command_processor_thread():
    """
    改进的命令处理线程 - 替换原始的 command_processor_thread
    """
    global command_queue, current_task, button_handler, last_task
    
    print("[CommandProcessor] Starting improved command processor thread")
    
    while not stop_event.is_set():
        if command_queue:
            with command_lock:
                command = command_queue.pop(0)
            
            try:
                print(f"[CommandProcessor] Processing command: {command}")
                
                command_type = command.get("command")
                task_type = command.get("task")
                params = command.get("params", {})
                
                print(f'[{time.strftime("%m-%d %H:%M:%S")}] Processing command: {command_type}-{task_type}')
                
                # 处理 start-control_toggle 命令
                if command_type == "start" and task_type == "control_toggle":
                    print("[CommandProcessor] Processing start-control_toggle command...")
                    current_task = "control_toggle"
                    
                    # 发送初始响应
                    initial_response = {
                        "code": SUCCESS_CODE,
                        "status": "received",
                        "command": "start",
                        "task": "control_toggle",
                        "message": "Receive start-control_toggle command",
                        "data": {}
                    }
                    safe_send_response(initial_response)
                    
                    # 检查操作序列
                    operating_sequence = params.get("operating", None)
                    checkpoint_bool = params.get("checkpoint_bool", None)
                    
                    # 安全执行任务
                    if operating_sequence is not None:
                        print(f"[CommandProcessor] Executing continuous sequence: {operating_sequence}")
                        result = safe_execute_task(
                            button_handler.execute_continuous_sequence,
                            "execute_continuous_sequence",
                            params,
                            timeout=600  # 10分钟超时
                        )
                    else:
                        print("[CommandProcessor] Executing single action")
                        result = safe_execute_task(
                            button_handler.run,
                            "button_run",
                            params,
                            timeout=300  # 5分钟超时
                        )
                    
                    error_code, result_data = result
                    
                    # 构建响应
                    if error_code == 0:
                        response = {
                            "code": 0,
                            "status": "success",
                            "command": "start",
                            "task": "control_toggle",
                            "message": "Complete start-control_toggle command",
                            "data": result_data
                        }
                        response["data"]["checkpoint_bool"] = checkpoint_bool
                        if checkpoint_bool and isinstance(result_data, dict) and "checkpoint" in result_data:
                            response["data"]["checkpoint"] = result_data["checkpoint"]
                    else:
                        error_message = result_data.get('error_message', 'Unknown error') if isinstance(result_data, dict) else str(result_data)
                        response = {
                            "code": error_code,
                            "status": "error",
                            "command": "start",
                            "task": "control_toggle",
                            "message": "Error during start-control_toggle command: " + error_message,
                            "data": result_data if isinstance(result_data, dict) else {'error_message': str(result_data)}
                        }
                        response["data"]["checkpoint_bool"] = checkpoint_bool
                    
                    safe_send_response(response)
                    current_task = None
                
                # 处理健康检查命令
                elif command_type == "health_check":
                    print("[CommandProcessor] Processing health check...")
                    try:
                        health_status = {
                            "button_handler": "ok" if button_handler else "error",
                            "queue_size": len(command_queue),
                            "current_task": current_task,
                            "last_task": last_task,
                            "timestamp": time.time(),
                            "thread_active": True
                        }
                        
                        response = {
                            "code": SUCCESS_CODE,
                            "status": "success",
                            "command": "health_check",
                            "task": "system",
                            "message": "System health check completed",
                            "data": health_status
                        }
                        safe_send_response(response)
                        
                    except Exception as e:
                        response = {
                            "code": GENERAL_ERROR,
                            "status": "error",
                            "command": "health_check",
                            "task": "system",
                            "message": f"Health check failed: {str(e)}",
                            "data": {"error_message": str(e)}
                        }
                        safe_send_response(response)
                
                # 处理其他命令...
                else:
                    print(f"[CommandProcessor] Unknown command: {command_type}")
                    response = {
                        "code": PARSING_ERROR,
                        "status": "error",
                        "command": command_type,
                        "task": task_type,
                        "message": f"Unknown command: {command_type}",
                        "data": {}
                    }
                    safe_send_response(response)
                    
            except Exception as e:
                print(f"[CommandProcessor] Critical error in command processor: {e}")
                print(f"[CommandProcessor] Exception details: {traceback.format_exc()}")
                
                # 发送错误响应
                error_response = {
                    "code": GENERAL_ERROR,
                    "status": "error",
                    "command": command.get("command", "unknown"),
                    "task": command.get("task", "unknown"),
                    "message": f"Command processor error: {str(e)}",
                    "data": {
                        'error_message': str(e),
                        'exception_type': type(e).__name__,
                        'traceback': traceback.format_exc()
                    }
                }
                safe_send_response(error_response)
                current_task = None
        
        time.sleep(0.1)
    
    print("[CommandProcessor] Command processor thread stopped")


# 使用说明：
# 1. 将此文件中的函数集成到 client.py 中
# 2. 替换原始的 command_processor_thread 为 improved_command_processor_thread
# 3. 在所有任务执行处使用 safe_execute_task 包装器
# 4. 在所有响应发送处使用 safe_send_response 包装器
