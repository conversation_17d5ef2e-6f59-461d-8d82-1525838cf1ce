#!/usr/bin/env python3

import numpy as np
import cv2
import pyrealsense2 as rs
import os
import csv
from datetime import datetime
from .button_detection import detect_buttons_by_status
from .button_action import build_object_coordinate_system
from .coord_ops import robust_depth_estimation
import time

class DataManager:
    def __init__(self, robot_config, arm, camera_instance, status):
        self.robot_config = robot_config
        self.arm = arm
        self.camera_instance = camera_instance
        self.status = status


    def collect_data(self, frame_count=5):
        """
        统一的图像与检测结果采集API

        Args:
            frame_count: 采集帧数，默认5帧

        Returns:
            dict: 统一格式的检测结果，包含：
            - 按钮坐标 (camera/base坐标系)
            - 旋钮坐标和角度
            - 图像数据
            - 机械臂状态
            - 检测统计信息
        """
        try:
            # 执行多帧检测
            data = self.multi_frame_detection(frame_count)

            # 添加机械臂状态
            if self.arm is not None:
                pose, joint = self._get_arm_state()
                data['pose'] = pose
                data['joint'] = joint
            else:
                data['pose'] = None
                data['joint'] = None

            # 添加时间戳
            data['timestamp'] = datetime.now().strftime("%Y%m%d_%H%M%S_%f")

            # 添加兼容性字段以支持旧的 button_processor 接口
            self._add_compatibility_fields(data)

            return data
        except Exception as e:
            print(f"Data collection failed: {e}")
            return None

    def get_detection_summary(self):
        """获取检测结果摘要"""
        data = self.collect_data()
        if not data:
            return None
        
        summary = {
            'buttons_detected': 0,
            'knob_detected': False,
            'handle_angle': None,
            'confidence': 0.0
        }
        
        # 统计检测到的按钮
        for key in ['top_left_button_camera_final', 'top_right_button_camera_final', 
                    'bottom_left_button_camera_final', 'bottom_right_button_camera_final']:
            if data.get(key) is not None:
                summary['buttons_detected'] += 1
        
        # 检查旋钮
        if data.get('knob_camera_final') is not None:
            summary['knob_detected'] = True
            summary['handle_angle'] = data.get('handle_angle_final')
        
        return summary

    def _add_compatibility_fields(self, data):
        """
        添加兼容性字段以支持旧的 button_processor 接口

        Args:
            data: 多帧检测结果数据
        """
        # 构建 button_coords 列表（按钮坐标）
        button_coords = []
        button_labels = []

        # 按照固定顺序添加按钮坐标
        button_mappings = [
            ('bottom_left_button_camera_final', 'bottom_left'),
            ('bottom_right_button_camera_final', 'bottom_right'),
            ('top_left_button_camera_final', 'top_left'),
            ('top_right_button_camera_final', 'top_right')
        ]

        for field_name, label in button_mappings:
            coord = data.get(field_name)
            if coord is not None:
                button_coords.append(coord)
                button_labels.append(label)

        # 添加兼容性字段
        data['button_coords'] = button_coords
        data['button_labels'] = button_labels

        # 旋钮相关字段
        data['knob_coord'] = data.get('knob_camera_final')
        data['knob_angle'] = data.get('handle_angle_final')

        # 添加有效帧数信息
        data['valid_frames'] = len(button_coords)

        # 添加点云数据（如果需要）
        if not data.get('point_cloud'):
            data['point_cloud'] = None

    # Convert 2D detection result to 3D coordinates
    def _convert_button_to_3d(self, button, depth_image, color_intrin):
        bx, by, br = button
        # 直接使用coord_ops中的robust_depth_estimation
        depth_val = robust_depth_estimation(depth_image, bx, by, br, self.status)
        
        if depth_val is not None and depth_val > 0:
            point_3d = rs.rs2_deproject_pixel_to_point(
                color_intrin,
                (float(bx), float(by)),
                depth_val / 1000.0
            )
            return point_3d
        return None
    
    
    def _generate_point_cloud(self, depth_image, color_intrin):
        """Generate point cloud from depth image and intrinsics"""
        if depth_image is None or color_intrin is None:
            return None
        pcd_points = []
        # More efficient point cloud generation using COLOR intrinsics for aligned depth
        for v in range(0, color_intrin.height, 2):
            for u in range(0, color_intrin.width, 2):
                depth_value = depth_image[v, u] / 1000.0
                if 0.1 < depth_value < 3.0:
                    point = rs.rs2_deproject_pixel_to_point(color_intrin, [u, v], depth_value)
                    pcd_points.append(point)
        
        if pcd_points:
            # print(f"Generated point cloud: {len(pcd_points)} points")
            return np.array(pcd_points)
        else:
            return None
    
    def _get_arm_state(self):
        """Get current arm pose and joint angles"""
        try:
            ret_msg = self.arm.rm_get_arm_all_state()
            if ret_msg[0] == 0:
                state = self.arm.rm_get_current_arm_state()
                if state[0] == 0:
                    return state[1]['pose'], state[1]['joint']
        except Exception as e:
            error_message = f"DataManager/_get_arm_state failed: {e}"
            raise Exception(error_message)
    
    
    def _add_coordinate_transformations(self, data):
        """Add base coordinate system transformations"""
        # Transform coordinates to base system
        for key in data:
            if '_camera' in key:
                data[key.replace('_camera', '_base')] = self.camera_to_base_transform(
                    data[key], data['pose']
                )
        
        return data
    
        
    def save_collected_data(self, data, prefix=""):
        """Save already collected data with optional prefix"""
        # Use timestamped directory for regular saves
        save_dir = f'./data/button-debug-saves/{datetime.now().strftime("%Y%m%d_%H")}'

        if not os.path.exists(save_dir):
            os.makedirs(save_dir)

        timestamp = data['timestamp']
        
        # Save images
        self._save_images(data, save_dir, prefix, timestamp)
        
        # Save coordinates (both camera and base systems)
        self._save_coordinates(data, save_dir, prefix, timestamp)
        
        # Save knob data
        self._save_knob_data(data, save_dir, prefix, timestamp)
        
        # # Save point cloud
        # self._save_point_cloud(data, save_dir, prefix, timestamp)
        
        # Save arm state
        self._save_arm_state(data, save_dir, prefix, timestamp)
        
        # Save metadata
        self._save_metadata(data, save_dir, prefix, timestamp)
        
        return data
    
    def _save_images(self, data, save_dir, prefix, timestamp):
        """Save color and depth images"""
        if data['color_image'] is not None:
            cv2.imwrite(f"{save_dir}/{prefix}colorImage_{timestamp}.png", data['color_image'])
            # print(f"Saved {prefix}colorImage_{timestamp}.png")
            
        if data['depth_image'] is not None:
            cv2.imwrite(f"{save_dir}/{prefix}depthImage_{timestamp}.png", data['depth_image'])
            # print(f"Saved {prefix}depthImage_{timestamp}.png")
    
    def _save_coordinates(self, data, save_dir, prefix, timestamp):
        """Save button coordinates in both camera and base coordinate systems"""
        # Save button coordinates in camera coordinate system
        if data.get('button_coords_camera'):
            coords_array = np.array(data['button_coords_camera'])
            header = 'x_camera,y_camera,z_camera'
            np.savetxt(f"{save_dir}/{prefix}buttonCoords_camera_{timestamp}.csv", coords_array, 
                      delimiter=',', header=header, comments='', fmt='%.6f')
            # print(f"Saved {prefix}buttonCoords_camera_{timestamp}.csv with {len(data['button_coords_camera'])} buttons")
        else:
            # Save empty file to indicate no buttons detected
            with open(f"{save_dir}/{prefix}buttonCoords_camera_{timestamp}.csv", 'w') as f:
                f.write('x_camera,y_camera,z_camera\n')
            # print(f"Saved {prefix}buttonCoords_camera_{timestamp}.csv (no buttons detected)")
        
        # Save button coordinates in base coordinate system
        if data.get('button_coords_base') and any(coord is not None for coord in data['button_coords_base']):
            # Filter out None values
            valid_coords = [coord for coord in data['button_coords_base'] if coord is not None]
            if valid_coords:
                coords_array = np.array(valid_coords)
                header = 'x_base,y_base,z_base'
                np.savetxt(f"{save_dir}/{prefix}buttonCoords_base_{timestamp}.csv", coords_array, 
                          delimiter=',', header=header, comments='', fmt='%.6f')
                # print(f"Saved {prefix}buttonCoords_base_{timestamp}.csv with {len(valid_coords)} buttons")
            else:
                with open(f"{save_dir}/{prefix}buttonCoords_base_{timestamp}.csv", 'w') as f:
                    f.write('x_base,y_base,z_base\n')
                # print(f"Saved {prefix}buttonCoords_base_{timestamp}.csv (no valid coordinates)")
        else:
            # Save empty file to indicate no buttons detected in base coordinates
            with open(f"{save_dir}/{prefix}buttonCoords_base_{timestamp}.csv", 'w') as f:
                f.write('x_base,y_base,z_base\n')
            # print(f"Saved {prefix}buttonCoords_base_{timestamp}.csv (no buttons detected)")
    
    def _save_knob_data(self, data, save_dir, prefix, timestamp):
        """Save knob coordinates and angle data"""
        # Save knob coordinate in camera coordinate system
        if data.get('knob_coord_camera') is not None:
            knob_array = np.array(data['knob_coord_camera']).reshape(1, -1)
            header = 'x_camera,y_camera,z_camera'
            np.savetxt(f"{save_dir}/{prefix}knobCoord_camera_{timestamp}.csv", knob_array, 
                      delimiter=',', header=header, comments='', fmt='%.6f')
            # print(f"Saved {prefix}knobCoord_camera_{timestamp}.csv")
        else:
            # Save empty file to indicate no knob detected
            with open(f"{save_dir}/{prefix}knobCoord_camera_{timestamp}.csv", 'w') as f:
                f.write('x_camera,y_camera,z_camera\n')
            # print(f"Saved {prefix}knobCoord_camera_{timestamp}.csv (no knob detected)")
        
        # Save knob coordinate in base coordinate system
        if data.get('knob_coord_base') is not None:
            knob_array = np.array(data['knob_coord_base']).reshape(1, -1)
            header = 'x_base,y_base,z_base'
            np.savetxt(f"{save_dir}/{prefix}knobCoord_base_{timestamp}.csv", knob_array, 
                      delimiter=',', header=header, comments='', fmt='%.6f')
            # print(f"Saved {prefix}knobCoord_base_{timestamp}.csv")
        else:
            # Save empty file to indicate no knob detected in base coordinates
            with open(f"{save_dir}/{prefix}knobCoord_base_{timestamp}.csv", 'w') as f:
                f.write('x_base,y_base,z_base\n')
            # print(f"Saved {prefix}knobCoord_base_{timestamp}.csv (no knob detected)")
        
        # Save knob angle
        if data['knob_angle'] is not None:
            with open(f"{save_dir}/{prefix}knobAngle_{timestamp}.txt", 'w') as f:
                f.write(f"{data['knob_angle']:.6f}\n")
            # print(f"Saved {prefix}knobAngle_{timestamp}.txt with angle {data['knob_angle']:.1f}°")
        else:
            with open(f"{save_dir}/{prefix}knobAngle_{timestamp}.txt", 'w') as f:
                f.write("None\n")
            # print(f"Saved {prefix}knobAngle_{timestamp}.txt (no angle detected)")
    
    def _save_point_cloud(self, data, save_dir, prefix, timestamp):
        """Save point cloud data"""
        if data['point_cloud'] is not None:
            pcd = o3d.geometry.PointCloud()
            pcd.points = o3d.utility.Vector3dVector(data['point_cloud'])
            o3d.io.write_point_cloud(f"{save_dir}/{prefix}pointCloud_{timestamp}.pcd", pcd)
            # print(f"Saved {prefix}pointCloud_{timestamp}.pcd with {len(data['point_cloud'])} points")
    
    def _save_arm_state(self, data, save_dir, prefix, timestamp):
        """Save arm pose and joint angles"""
        if data['pose'] is not None:
            np.savetxt(f"{save_dir}/{prefix}pose_{timestamp}.txt", data['pose'], fmt='%.6f')
            # print(f"Saved {prefix}pose_{timestamp}.txt")
            
        if data['joint'] is not None:
            np.savetxt(f"{save_dir}/{prefix}joint_{timestamp}.txt", data['joint'], fmt='%.6f')
            # print(f"Saved {prefix}joint_{timestamp}.txt")
    
    def _save_metadata(self, data, save_dir, prefix, timestamp):
        """Save coordinate system information and stability metrics"""
        # Save coordinate system information
        coord_info = {
            'has_camera_coords': data.get('button_coords_camera') is not None,
            'has_base_coords': data.get('button_coords_base') is not None,
            'transformation_successful': data.get('button_coords_base') is not None and data['pose'] is not None
        }
        
        with open(f"{save_dir}/{prefix}coordinate_system_info_{timestamp}.txt", 'w') as f:
            for key, value in coord_info.items():
                f.write(f"{key}: {value}\n")
        # print(f"Saved {prefix}coordinate_system_info_{timestamp}.txt")
        
        # Save stability metrics
        if data.get('stability_metrics'):
            with open(f"{save_dir}/{prefix}stability_{timestamp}.txt", 'w') as f:
                metrics = data['stability_metrics']
                f.write(f"Button detection rate: {metrics.get('button_detection_rate', 0):.3f}\n")
                f.write(f"Knob detection rate: {metrics.get('knob_detection_rate', 0):.3f}\n")
                f.write(f"Angle detection rate: {metrics.get('angle_detection_rate', 0):.3f}\n")
                f.write(f"Button stability (std): {metrics.get('button_std', [])}\n")
                f.write(f"Knob stability (std): {metrics.get('knob_std', float('inf')):.6f}\n")
                f.write(f"Angle stability (std): {metrics.get('angle_std', float('inf')):.6f}\n")
            # print(f"Saved {prefix}stability_{timestamp}.txt")

    def multi_frame_detection(self, frame_count=None):
        """
        Multi-frame button and knob detection with outlier filtering

        Args:
            frame_count (int): Number of frames to collect and process.
                               If None, uses value from robot_config.

        Returns:
            dict: Processed multi-frame detection result
            tuple: last frame's color image, depth image, and color intrinsics
        """
        # Use default frame_count from config if not provided
        if frame_count is None:
            processing_settings = self.robot_config.get_processing_settings()
            frame_count = processing_settings.get('frame_count', 5)

        frame_results = {
            'bottom_left_button_camera': [],
            'bottom_left_button_valid_frames': 0,
            'bottom_right_button_camera': [],
            'bottom_right_button_valid_frames': 0,
            'top_left_button_camera': [],
            'top_left_button_valid_frames': 0,
            'top_right_button_camera': [],
            'top_right_button_valid_frames': 0,
            'knob_camera': [],
            'knob_valid_frames': 0,
            'handle_angle': [],
            'handle_angle_valid_frames': 0
        }
        last_color_image = None
        last_depth_image = None
        last_color_intrin = None

        # Collect frames and detections
        for i in range(frame_count):
            try:
                color_image = self.camera_instance.color_image.copy() if self.camera_instance.color_image is not None else None
                depth_image = self.camera_instance.depth_image.copy() if self.camera_instance.depth_image is not None else None
                color_intrin = self.camera_instance.color_intrin if self.camera_instance.color_intrin is not None else None
                if color_image is not None and depth_image is not None and color_intrin is not None:
                    last_color_image = color_image.copy()
                    last_depth_image = depth_image.copy()
                    last_color_intrin = color_intrin.copy()
                
                # Detect buttons on current frame using status-aware detection
                button_data = detect_buttons_by_status(
                    color_image, 
                    status=self.status,
                    verbose=False, 
                    display_process=False
                )
                
                # Handle different button_data formats from status-aware detection
                if not isinstance(button_data, dict):
                    raise Exception(f"DataManager/multi_frame_detection fail at frame {i}: button_data is not a dict")
                
                # 处理统一格式的检测结果
                if button_data['detection_mode'] == 'single':
                    # 单目标检测模式
                    single_target = button_data.get('single_target')
                    target_type = button_data.get('target_type')
                    
                    if single_target is not None:
                        target_3d = self._convert_button_to_3d(single_target, depth_image, color_intrin)
                        
                        # 完善状态映射
                        if self.status == 'touching_left_button' or target_type == 'left_button':
                            frame_results['bottom_left_button_camera'].append(target_3d)
                            frame_results['bottom_left_button_valid_frames'] += 1
                        elif self.status == 'touching_right_button' or target_type == 'right_button':
                            frame_results['bottom_right_button_camera'].append(target_3d)
                            frame_results['bottom_right_button_valid_frames'] += 1
                        elif self.status in ['touching_knob_left', 'touching_knob_right', 'touching_knob_center'] or target_type.startswith('knob'):
                            frame_results['knob_camera'].append(target_3d)
                            frame_results['knob_valid_frames'] += 1
                else:
                    # 多目标检测模式
                    targets = button_data['targets']
                    
                    if targets['top_left_button'] is not None:
                        top_left_button_3d = self._convert_button_to_3d(targets['top_left_button'], depth_image, color_intrin)
                        frame_results['top_left_button_camera'].append(top_left_button_3d)
                        frame_results['top_left_button_valid_frames'] += 1
                    
                    if targets['top_right_button'] is not None:
                        top_right_button_3d = self._convert_button_to_3d(targets['top_right_button'], depth_image, color_intrin)
                        frame_results['top_right_button_camera'].append(top_right_button_3d)
                        frame_results['top_right_button_valid_frames'] += 1
                    
                    if targets['bottom_left_button'] is not None:
                        bottom_left_button_3d = self._convert_button_to_3d(targets['bottom_left_button'], depth_image, color_intrin)
                        frame_results['bottom_left_button_camera'].append(bottom_left_button_3d)
                        frame_results['bottom_left_button_valid_frames'] += 1
                    
                    if targets['bottom_right_button'] is not None:
                        bottom_right_button_3d = self._convert_button_to_3d(targets['bottom_right_button'], depth_image, color_intrin)
                        frame_results['bottom_right_button_camera'].append(bottom_right_button_3d)
                        frame_results['bottom_right_button_valid_frames'] += 1
                    
                    if targets['knob'] is not None:
                        knob_3d = self._convert_button_to_3d(targets['knob'], depth_image, color_intrin)
                        frame_results['knob_camera'].append(knob_3d)
                        frame_results['knob_valid_frames'] += 1
                    
                    if targets['handle_angle'] is not None:
                        handle_angle = targets['handle_angle']
                        frame_results['handle_angle'].append(handle_angle)
                        frame_results['handle_angle_valid_frames'] += 1
                
                    
            except Exception as e:
                print(f"Frame {i} detection failed: {e}")
                continue
        
        # Process collected data with outlier filtering
        stable_result = self._process_multi_frame_data(frame_results)
        stable_result['color_image'] = last_color_image
        stable_result['depth_image'] = last_depth_image
        stable_result['color_intrin'] = last_color_intrin
        
        return stable_result
    
    
    def _process_multi_frame_data(self, frame_results):
        """
        Process multi-frame detection data with outlier filtering
        
        Args:
            frame_results (dict): Frame results from multi-frame detection
        
        Returns:
            dict: Processed stable detection result
        """
        result = {
            'bottom_left_button_camera_final': None,
            'bottom_left_button_camera_stability': None,
            'bottom_right_button_camera_final': None,
            'bottom_right_button_camera_stability': None,
            'top_left_button_camera_final': None,
            'top_left_button_camera_stability': None,
            'top_right_button_camera_final': None,
            'top_right_button_camera_stability': None,
            'knob_camera_final': None,
            'knob_camera_stability': None,
            'handle_angle_final': None,
            'handle_angle_stability': None,
        }

        for target in frame_results:
            if not len(frame_results[target]) == 0:
                target_data = np.array(frame_results[target])
                target_stability = np.std(target_data, axis=0)
                if target == 'handle_angle':
                    target_noOutlier = self._filter_angle_outliers(target_data)
                else:
                    target_noOutlier = self._filter_coordinate_outliers(target_data)
                target_final = np.mean(target_noOutlier, axis=0)
                result[f'{target}_final'] = target_final
                result[f'{target}_stability'] = target_stability
            else:
                result[f'{target}_final'] = None
                result[f'{target}_stability'] = None

        
        return result
    
    def _filter_coordinate_outliers(self, coords_array, threshold=2.0):
        """
        Filter coordinate outliers using IQR method
        
        Args:
            coords_array (np.ndarray): Array of coordinates to filter
            threshold (float): IQR threshold multiplier for outlier detection
            
        Returns:
            np.ndarray: Filtered coordinates array
        """
        if len(coords_array) < 3:
            return coords_array
        
        # Calculate IQR for each dimension
        q1 = np.percentile(coords_array, 25, axis=0)
        q3 = np.percentile(coords_array, 75, axis=0)
        iqr = q3 - q1
        
        # Define outlier bounds
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr
        
        # Filter outliers
        valid_mask = np.all(
            (coords_array >= lower_bound) & (coords_array <= upper_bound), 
            axis=1
        )
        
        return coords_array[valid_mask]
    
    def _filter_angle_outliers(self, angles_list, threshold=15.0):
        """
        Filter angle outliers considering circular nature of angles
        
        Args:
            angles_list (list): List of angles to filter
            threshold (float): Angular threshold in degrees for outlier detection
            
        Returns:
            np.ndarray: Filtered angles array
        """
        if len(angles_list) < 3:
            return angles_list
        
        angles_array = np.array(angles_list)
        
        # Use circular statistics for angles
        angles_rad = np.deg2rad(angles_array)
        mean_angle_rad = np.angle(np.mean(np.exp(1j * angles_rad)))
        
        # Calculate angular differences
        angle_diffs = np.abs(np.angle(np.exp(1j * (angles_rad - mean_angle_rad))))
        
        # Filter based on angular threshold
        valid_mask = angle_diffs <= np.deg2rad(threshold)
        
        return angles_array[valid_mask]