import cv2
import numpy as np
import sys
import os

# Add the current directory to the Python path to import from buttonControl
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from buttonControl.button_detection import detect_buttons_by_status

def detect_all_buttons_in_image(image_path, verbose=True, display_process=True):
    """
    Detect all buttons and knob in an image during 'uncertain' state.
    
    Args:
        image_path (str): Path to the input image
        verbose (bool): Whether to print detailed information
        display_process (bool): Whether to show processing steps
    
    Returns:
        tuple: Detection results from detect_buttons function
    """
    # Read the image
    if not os.path.exists(image_path):
        raise FileNotFoundError(f"Image file not found: {image_path}")
    
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not read image: {image_path}")
    
    # Detect all buttons in uncertain state
    detection_result = detect_buttons_by_status(
        image,
        status='visible',
        verbose=verbose,
        display_process=display_process
    )

    return detection_result
    

if __name__ == "__main__":
    # Detect all buttons
    full_button_img_list = ['darkFull', 'lightFull']
    for img_name in full_button_img_list:
        detection_result = detect_all_buttons_in_image(
            f'./tmp/{img_name}.jpg', verbose=True, display_process=True
        )

        # 处理新的字典格式返回值
        if detection_result and isinstance(detection_result, dict):
            targets = detection_result.get('targets', {})

            # 构建兼容的变量
            top_row = [targets.get('top_left_button'), targets.get('top_right_button')]
            bottom_row = [targets.get('bottom_left_button'), targets.get('bottom_right_button')]
            knob = targets.get('knob')
            handle_angle = targets.get('handle_angle')
            mode_code = None  # 新格式中没有mode_code

            # Print detection details
            print("\n--- Button Detection Results ---")

            # Top row buttons
            if any(top_row):
                print("Top Row Buttons:")
                for i, button in enumerate(top_row, 1):
                    if button:
                        print(f"  Button {i}: {button}")

            # Bottom row buttons
            if any(bottom_row):
                print("Bottom Row Buttons:")
                for i, button in enumerate(bottom_row, 1):
                    if button:
                        print(f"  Button {i}: {button}")

            # Knob details
            if knob:
                print(f"Knob: {knob}")

            # Handle angle and mode
            if handle_angle is not None:
                print(f"Handle Angle: {handle_angle:.2f}°")

            if mode_code is not None:
                mode_map = {1: "Manual", 0: "Stop", -1: "Auto"}
                print(f"Mode: {mode_map.get(mode_code, 'Unknown')}")

            print(f"\nDetection successful: {detection_result.get('success', False)}")
            print(f"Detection mode: {detection_result.get('detection_mode', 'unknown')}")
        else:
            print(f"Detection failed for {img_name}")
