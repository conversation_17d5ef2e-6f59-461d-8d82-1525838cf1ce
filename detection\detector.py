import cv2
from model.onnx_inference import YOLOInference
import glob
import time

class detectorDetection:
    def __init__(self,
                 name=r'./model/detector_6.onnx',
                 conf=0.7,
                 imgsz=640):
        if name is None:
            name = r'./model/detector_6.onnx'
        self.name = name
        self.conf = conf
        # self.model = YOLO(name, verbose=False)
        self.model = YOLOInference(name, conf_thresh=conf)
        self.imgsz = imgsz

    def detect(self, source, detect_count=0, save=False, save_txt=False):
        # pred_raw = self.model.predict(source=source, save=save, save_txt=save_txt, conf=self.conf, imgsz=self.imgsz,
        #                              verbose=False)
        # pred = pred_raw[0].boxes.data.detach().cpu().numpy()
        time1 = time.time()
        pred = self.model.runs(source)
        res = []
        if len(pred) > 0:
            for r in pred:
                x1, y1, x2, y2, conf, cls = r
                xc, yc, w, h = (x1 + x2) / 2, (y1 + y2) / 2, x2 - x1, y2 - y1
                if w > 90 or h > 90:
                    continue
                res.append((int(xc), int(yc), int((w + h) / 2), int(w), int(h), conf))
                # print(f'{cls} {conf} {xc} {yc} {w} {h}')
        print(f"[{detect_count}]-{len(res)>0} detector:{int(1000*(time.time()-time1))}ms")

        return (res[0] if len(res) > 0 else None), res


def detection():
    detector = detectorDetection()
    detect_list = glob.glob(r'G:\Windows\PycharmProjects1\bot\data\b_smoker\labeling\imgs\*')

    for imp in detect_list:
        im = cv2.imread(imp)
        res = detector.detect(im)
        print(res)
    return


def calculate_iou(box1, box2):
    width = max(min(box1[2], box2[2]) - max(box1[0], box2[0]), 0)
    height = max(min(box1[3], box2[3]) - max(box1[1], box2[1]), 0)
    intersect = width * height
    area_1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
    area_2 = (box2[2] - box2[0]) * (box2[3] - box2[1])
    union = area_1 + area_2 - intersect
    return intersect / union


if __name__ == "__main__":
    detection()
    exit(1)
