import cv2
import numpy as np
import sys
import os

# Add the current directory to the Python path to import from buttonControl
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from buttonControl.button_detection import detect_buttons_by_status, convert_coordinates_to_original

def detect_left_button_in_image(image_path, verbose=True, display_process=True):
    """
    Detect the left button in a given image.
    
    Args:
        image_path (str): Path to the input image
        verbose (bool): Whether to print detailed information
        display_process (bool): Whether to show processing steps
    
    Returns:
        dict: Detection results including target, confidence, display image, etc.
    """
    # Read the image
    if not os.path.exists(image_path):
        raise FileNotFoundError(f"Image file not found: {image_path}")
    
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not read image: {image_path}")
    
    # Detect left button
    detection_result = detect_buttons_by_status(
        image, 
        status='touching_left_button', 
        verbose=verbose, 
        display_process=display_process
    )
    
    return detection_result
    

if __name__ == "__main__":
    # Detect left button
    left_button_img_list = ['less_than_4_buttons_colorImage_20250730_094620_886']
        # 'less_than_4_buttons_colorImage_20250730_091940_387']
        # 'all_single_frame_detection_failed_colorImage_20250730_071950_117',\
        #                     'less_than_4_buttons_colorImage_20250730_071951_608',\
        #                         'less_than_4_buttons_colorImage_20250730_073402_571',\
        #                             'less_than_4_buttons_colorImage_20250730_073957_372',\
        #                                 'less_than_4_buttons_colorImage_20250730_074759_405']
    for img_name in left_button_img_list:
        result = detect_left_button_in_image(f'./tmp/{img_name}.png', verbose=True, display_process=True)
    
        # Print detection details
        print("\n--- Left Button Detection Results ---")
        if isinstance(result, dict):
            single_target = result.get('single_target')
            print(f"Target Found: {single_target is not None}")
            if single_target:
                print(f"Button Center: {single_target}")
                print(f"Confidence: {result.get('confidence', 0.0):.2f}")
            print(f"Detection Mode: {result.get('detection_mode', 'unknown')}")
            print(f"Success: {result.get('success', False)}")
        else:
            print(f"Unexpected result format: {type(result)}")

        print(f"\nDetection completed for {img_name}")