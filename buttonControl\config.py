import json
import os
import threading
import time
from typing import Dict, Tuple, Any, Optional


class RobotConfig:
    
    def __init__(self, config_file: str = None):
        # If no config file specified, use default path in buttonControl directory
        if config_file is None:
            config_file = os.path.join(os.path.dirname(__file__), "robot_config.json")
            print(f'Using preset robot_config.json: {config_file}')
        self.config_file = config_file

        # Default distance parameters (in meters)
        self.observation_distance = 0.4      # First step approach distance
        self.touch_distance = 0.25           # Second step approach distance
        self.click_depth = 0.09              # Actual click depth
        self.turn_depth = 0.06              # Knob turn depth
        self.smallest_working_distance = 0.5  # Minimum working distance for search
        self.largest_working_distance = 1.0   # Maximum working distance for search
        
        # Default button and knob offsets (x, y) in button coordinate system
        self.left_button_offsets = (-0.01, 0.045)   # Offset for left button approach
        self.right_button_offsets = (-0.011, -0.03)  # Offset for right button approach
        self.knob_offsets = (-0.005, 0.005)           # Offset for knob approach
        
        # Default movement parameters
        self.movement_v = 30           # 速度参数
        self.movement_r = 50           # 半径参数  
        self.movement_connect = 0      # 连接参数
        self.movement_block = 1        # 阻塞参数
        
        # Default robot settings
        self.robot_ip = "************"
        self.robot_port = 8080
        self.arm_model = "RM_MODEL_RM_63_III_E"
        self.force_type = "RM_MODEL_RM_B_E"

        # Default search parameters
        self.joint5_angle = -101.048

        # Default auto-reset delay (in seconds)
        self.auto_reset_delay = 2.0

        # Default camera settings
        self.camera_width = 640
        self.camera_height = 480
        self.camera_fps = 30
        
        # Default processing settings
        self.use_robust_normal = True     # 是否使用鲁棒法向量计算
        self.frame_count = 10              # Frame collection count
        self.target_button = 'left'        # Default target button
        
        # Default depth estimation settings (hollow circle parameters)
        self.outer_large_radius_ratio = 2.0   # Outer radius for hollow circle (multiplier of button radius)
        self.outer_small_radius_ratio = 1.5   # Inner radius for hollow circle (multiplier of button radius)
        self.inner_radius_ratio = 0.6         # Inner radius for fallback shrink circle (multiplier of button radius)
        
        # Default fine-tuning settings
        self.left_button_ideal_xyz_cam = [0.086747, 0.078263, 0.237]    # Left button center ideal position (camera coordinates: x, y, z in meters)
        self.right_button_ideal_xyz_cam = [0.004522, 0.080774, 0.241]   # Right button center ideal position (camera coordinates: x, y, z in meters)
        self.knob_center_ideal_xyz_cam = [0.04416216, 0.0780184, 0.243]
        self.knob_left_ideal_xyz_cam = [0.04883879, 0.07846441, 0.242]
        self.knob_right_ideal_xyz_cam = [0.04350585, 0.07683502, 0.24240116]
        # self.knob_ideal_xyz_cam = [0.048945, 0.082361, 0.241]  # 已废弃
        self.finetune_threshold = 0.001                    # Fine-tuning error threshold in meters (below this threshold, fine-tuning is considered complete)
        self.finetune_max_iterations = 3                  # Maximum number of fine-tuning iterations (stop if exceeded)
        
        # Default zero pose (same as pole_processor.py)
        self.zero_pose = [2.103, 75.533, -163.534, -7.251, 163.381, -188.2]

        # Add known_orientation (mock default)
        self.known_orientation = [-3.146, 13.908, -33.719, -7.169, -25.607, -11.949]

        # Load configuration from file if it exists
        self.load_config()
    
    
    def get_distances(self) -> Dict[str, float]:
        """Get all distance parameters"""
        return {
            'observation_distance': self.observation_distance,
            'touch_distance': self.touch_distance,
            'click_depth': self.click_depth,
            'turn_depth': self.turn_depth,
            'smallest_working_distance': self.smallest_working_distance,
            'largest_working_distance': self.largest_working_distance
        }
    
    def get_offsets(self) -> Dict[str, Tuple[float, float]]:
        """Get all offset parameters"""
        return {
            'left_button_offsets': self.left_button_offsets,
            'right_button_offsets': self.right_button_offsets,
            'knob_offsets': self.knob_offsets
        }
    
    def get_movement_parameters(self) -> Dict[str, Any]:
        """Get all movement parameters"""
        return {
            'v': self.movement_v,
            'r': self.movement_r,
            'connect': self.movement_connect,
            'block': self.movement_block
        }
    
    def get_robot_settings(self) -> Dict[str, Any]:
        """Get all robot settings"""
        return {
            'robot_ip': self.robot_ip,
            'robot_port': self.robot_port,
            'arm_model': self.arm_model,
            'force_type': self.force_type
        }
    
    def get_processing_settings(self) -> Dict[str, Any]:
        """Get all processing settings"""
        return {
            'use_robust_normal': self.use_robust_normal,
            'frame_count': self.frame_count,
            'target_button': self.target_button,
            'outer_large_radius_ratio': self.outer_large_radius_ratio,
            'outer_small_radius_ratio': self.outer_small_radius_ratio,
            'inner_radius_ratio': self.inner_radius_ratio,
            'left_button_ideal_xyz_cam': self.left_button_ideal_xyz_cam,
            'right_button_ideal_xyz_cam': self.right_button_ideal_xyz_cam,
            'knob_center_ideal_xyz_cam': self.knob_center_ideal_xyz_cam,
            'knob_left_ideal_xyz_cam': self.knob_left_ideal_xyz_cam,
            'knob_right_ideal_xyz_cam': self.knob_right_ideal_xyz_cam,
            'finetune_threshold': self.finetune_threshold,
            'finetune_max_iterations': self.finetune_max_iterations
        }
    
    def get_camera_settings(self) -> Dict[str, Any]:
        """Get all camera settings"""
        return {
            'camera_width': self.camera_width,
            'camera_height': self.camera_height,
            'camera_fps': self.camera_fps
        }

    def get_search_parameters(self) -> Dict[str, Any]:
        """Get all search parameters"""
        return {
            'joint5_angle': self.joint5_angle
        }
    
    def get_known_orientation(self):
        return self.known_orientation

    def get_all_config(self) -> Dict[str, Any]:
        """Get all configuration parameters"""
        return {
            'distances': self.get_distances(),
            'offsets': self.get_offsets(),
            'movement': self.get_movement_parameters(),
            'robot': self.get_robot_settings(),
            'processing': self.get_processing_settings(),
            'camera': self.get_camera_settings(),
            'search_parameters': self.get_search_parameters(),
            'zero_pose': self.zero_pose,
            'known_orientation': self.known_orientation,
            'auto_reset_delay': self.auto_reset_delay
        }
    
    
    def load_config(self) -> bool:
        """Load configuration from file"""
        try:
            if not os.path.exists(self.config_file):
                print(f"Config file {self.config_file} not found, using defaults")
                return False
                
            with open(self.config_file, 'r') as f:
                config_data = json.load(f)
            
            # Update distances
            if 'distances' in config_data:
                distances = config_data['distances']
                self.observation_distance = distances.get('observation_distance', self.observation_distance)
                self.touch_distance = distances.get('touch_distance', self.touch_distance)
                self.click_depth = distances.get('click_depth', self.click_depth)
                self.turn_depth = distances.get('turn_depth', self.turn_depth)
                self.smallest_working_distance = distances.get('smallest_working_distance', self.smallest_working_distance)
                self.largest_working_distance = distances.get('largest_working_distance', self.largest_working_distance)
            
            # Update offsets
            if 'offsets' in config_data:
                offsets = config_data['offsets']
                self.left_button_offsets = tuple(offsets.get('left_button_offsets', self.left_button_offsets))
                self.right_button_offsets = tuple(offsets.get('right_button_offsets', self.right_button_offsets))
                self.knob_offsets = tuple(offsets.get('knob_offsets', self.knob_offsets))
            
            # Update movement parameters
            if 'movement' in config_data:
                movement = config_data['movement']
                self.movement_v = movement.get('v', self.movement_v)
                self.movement_r = movement.get('r', self.movement_r)
                self.movement_connect = movement.get('connect', self.movement_connect)
                self.movement_block = movement.get('block', self.movement_block)
            
            # Update robot settings
            if 'robot' in config_data:
                robot = config_data['robot']
                self.robot_ip = robot.get('robot_ip', self.robot_ip)
                self.robot_port = robot.get('robot_port', self.robot_port)
                self.arm_model = robot.get('arm_model', self.arm_model)
                self.force_type = robot.get('force_type', self.force_type)
            
            # Update processing settings
            if 'processing' in config_data:
                processing = config_data['processing']
                self.use_robust_normal = processing.get('use_robust_normal', self.use_robust_normal)
                self.frame_count = processing.get('frame_count', self.frame_count)
                self.target_button = processing.get('target_button', self.target_button)
                self.outer_large_radius_ratio = processing.get('outer_large_radius_ratio', self.outer_large_radius_ratio)
                self.outer_small_radius_ratio = processing.get('outer_small_radius_ratio', self.outer_small_radius_ratio)
                self.inner_radius_ratio = processing.get('inner_radius_ratio', self.inner_radius_ratio)
                self.left_button_ideal_xyz_cam = processing.get('left_button_ideal_xyz_cam', self.left_button_ideal_xyz_cam)
                self.right_button_ideal_xyz_cam = processing.get('right_button_ideal_xyz_cam', self.right_button_ideal_xyz_cam)
                self.knob_center_ideal_xyz_cam = processing.get('knob_center_ideal_xyz_cam', self.knob_center_ideal_xyz_cam)
                self.knob_left_ideal_xyz_cam = processing.get('knob_left_ideal_xyz_cam', self.knob_left_ideal_xyz_cam)
                self.knob_right_ideal_xyz_cam = processing.get('knob_right_ideal_xyz_cam', self.knob_right_ideal_xyz_cam)
                self.finetune_threshold = processing.get('finetune_threshold', self.finetune_threshold)
                self.finetune_max_iterations = processing.get('finetune_max_iterations', self.finetune_max_iterations)
            
            # Update camera settings
            if 'camera' in config_data:
                camera = config_data['camera']
                self.camera_width = camera.get('camera_width', self.camera_width)
                self.camera_height = camera.get('camera_height', self.camera_height)
                self.camera_fps = camera.get('camera_fps', self.camera_fps)
            
            # Update zero pose
            if 'zero_pose' in config_data:
                self.zero_pose = config_data.get('zero_pose', self.zero_pose)
            # Update known_orientation
            if 'known_orientation' in config_data:
                ko = config_data['known_orientation']
                if isinstance(ko, list) and len(ko) == 6:
                    self.known_orientation = ko
                else:
                    self.known_orientation = None

            # Update search parameters
            if 'search_parameters' in config_data:
                search_params = config_data['search_parameters']
                self.joint5_angle = search_params.get('joint5_angle', self.joint5_angle)

            # Update auto-reset delay
            if 'auto_reset_delay' in config_data:
                self.auto_reset_delay = config_data.get('auto_reset_delay', self.auto_reset_delay)
            else:
                self.auto_reset_delay = 0.0

            print(f"Configuration loaded from {self.config_file}")
            return True
            
        except Exception as e:
            print(f"Warning: Failed to load config from {self.config_file}: {e}")
            return False




# Global configuration instance
# This allows other modules to import and use the same configuration
robot_config = RobotConfig()

# Convenience functions for easy access
def get_config() -> RobotConfig:
    """Get the global configuration instance"""
    return robot_config

def get_distances() -> Dict[str, float]:
    """Get distance parameters using the global config"""
    return robot_config.get_distances()

def get_offsets() -> Dict[str, Tuple[float, float]]:
    """Get offset parameters using the global config"""
    return robot_config.get_offsets()

def get_movement_parameters() -> Dict[str, Any]:
    """Get movement parameters using the global config"""
    return robot_config.get_movement_parameters() 