# 机械臂控制数据结构

本文档描述了机械臂控制中服务器和客户端之间通信使用的数据结构。

## 错误代码

| 代码   | 英文变量名                | 描述                                         |
|--------|--------------------------|----------------------------------------------|
| 0      | SUCCESS_CODE             | 成功                                         |
| 1001   | ARM_STATE_ERROR          | 机械臂状态错误 - 多次尝试后未能获取机械臂状态 |
| 1002   | ARM_MOVEMENT_ERROR       | 机械臂移动错误 - 通用机械臂移动错误          |
| 1003   | GRIPPER_STATE_ERROR      | 夹爪状态错误 - 未能获取夹爪状态              |
| 1004   | GRIPPER_CONTROL_ERROR    | 夹爪控制错误 - 未能控制夹爪                  |
| 2001   | NO_DETECTOR_ERROR        | 未检测到烟感探测器 - 未找到烟感探测器            |
| 2002   | NO_POLE_ERROR            | 未检测到烟杆 - 未检测到烟杆                  |
| 2003   | NO_POLE_HANDLER_ERROR    | 未找到烟杆抓手 - 未找到烟杆抓手          |
| 2004   | NO_DEPTH_INFO_ERROR      | 无深度信息错误 - 没有探测器深度信息          |
| 2005   | POLE_TIMEOUT_ERROR       | 烟感测试超时错误 - 追踪探测器时超时              |
| 2006   | POLE_UNREACHABLE_ERROR   | 烟感无法到达错误 - 无法到达，返回相对前进xy坐标 |
| 3001   | CAMERA_ERROR             | 相机错误 - 相机不可用或故障                  |
| 4001   | EMERGENCY_STOP_ERROR     | 紧急停止错误 - 收到紧急停止命令而终止程序      |
| 5001   | MEMORY_ERROR             | 内存错误 - 没有可用的图片存储空间或内存      |
| 6001   | PARSING_ERROR            | 解析错误 - 解析接收到的命令JSON出错          |
| 9999   | UNKNOWN_ERROR            | 通用错误 - 未分类错误                        |

## 命令结构

### 1. 烟感测试命令

#### 请求

**默认参数:**
```json
{
  "command": "start",
  "task": "smoke_test",
  "params": {}
}
```

**自定义参数:**
```json
{
  "command": "start",
  "task": "smoke_test",
  "params": {
    "pole_length": 1150,
    "wait_time": 10,
    "unreachable_action": "continue"
  }
}
```

| 参数 | 类型 | 描述 | 默认值 |
|-----------|------|-------------|---------|
| pole_length | 整数 | 烟杆长度(毫米) | 1150 |
| wait_time | 整数 | 在探测器处等待的时间(秒) | 10 |
| unreachable_action | 字符串 | 探测器无法到达时的操作("continue"继续或"stop"停止) | "continue" |

#### 响应

**初始响应:**
```json
{
  "code": 0,
  "status": "received",
  "command": "start",
  "task": "smoke_test",
  "message": "start-smoke_test command received",
  "data": {}
}
```

**成功响应:**
```json
{
  "code": 0,
  "status": "success",
  "command": "start",
  "task": "smoke_test",
  "message": "start-smoke_test command complete",
  "data": {
    "result": true,
    "path": "data/shot-images-saves/0701123456_001/001_detection.png"
  }
}
```

**错误响应(示例2006):**
```json
{
  "code": 2006,
  "status": "error",
  "command": "start",
  "task": "smoke_test",
  "message": "Could not find suitable start position",
  "data": {
    "offset": [100, -500],
    "result": true,
    "path": "data/shot-images-saves/0701123456_001/001_detection.png"
  }
}
```

### 2. 温感测试命令（实际任务暂空）

#### 请求

**默认参数:**
```json
{
  "command": "start",
  "task": "therm_test",
  "params": {}
}
```

#### 响应

**初始响应:**
```json
{
  "code": 0,
  "status": "received",
  "command": "start",
  "task": "therm_test",
  "message": "start-threm_test command received",
  "data": {}
}
```

**成功响应:**
```json
{
  "code": 0,
  "status": "success",
  "command": "start",
  "task": "therm_test",
  "message": "start-threm_test command complete",
  "data": {}
}
```

**错误响应(示例2001):**
```json
{
  "code": 2001,
  "status": "error",
  "command": "start",
  "task": "therm_test",
  "message": "Failed to get arm state after multiple attempts",
  "data": {}
}
```

### 3. 按钮控制命令

#### 请求

```json
{
  "command": "start",
  "task": "control_toggle",
  "params": {
    "operating":["switch_manual", "turn_on", "turn_off", "switch_automatic"]
  }
}
```

`operating`字段将包含按钮控制任务的模块子任务顺序。

#### 响应

**初始响应:**
```json
{
  "code": 0,
  "status": "received",
  "command": "start",
  "task": "control_toggle",
  "message": "start-control_toggle command received",
  "data": {}
}
```

**成功响应:**
```json
{
  "code": 0,
  "status": "success",
  "command": "start",
  "task": "control_toggle",
  "message": "start-control_toggle command complete",
  "data": {}
}
```

**错误响应(示例2001):**
```json
{
  "code": 2001,
  "status": "error",
  "command": "start",
  "task": "control_toggle",
  "message": "Failed to get arm state after multiple attempts",
  "data": {}
}
```

### 4. 紧急停止命令

#### 请求

```json
{
  "command": "emergency_stop"
}
```

#### 响应

```json
{
  "code": 0,
  "status": "received",
  "command": "emergency_stop",
  "task": "control_toggle",
  "message": "emergency_stop command received",
  "data": {}
}
```

对正在运行或正在暂停的任务进行紧急急停终止。
`task`字段将包含触发紧急停止时正在运行的任务名称。
紧急急停命令无终止反馈。

### 5. 重置命令

#### 请求

```json
{
  "command": "reset"
}
```

#### 响应

**初始响应:**
```json
{
  "code": 0,
  "status": "received",
  "command": "reset",
  "message": "Reset command received",
  "data": {}
}
```

**成功响应:**
```json
{
  "code": 0,
  "status": "success",
  "command": "reset",
  "message": "Reset command complete",
  "data": {}
}
```

**错误响应(示例1002):**
```json
{
  "code": 1002,
  "status": "error",
  "command": "reset",
  "message": "Failed to move to zero pose, error code: 1002",
  "data": {}
}
```


### 6. 烟感检测拍照命令

#### 请求

```json
{
  "command": "start",
  "task": "photo",
  "params": {
    "photo_direct_mode": false
  }
}
```

| 参数 | 类型 | 描述 | 默认值 |
|-----------|------|-------------|---------|
| photo_direct_mode | 布尔值 | 如果为true，在找到第一个探测器后停止；如果为false，在所有位置拍照 | false |

#### 响应

**初始响应:**
```json
{
  "code": 0,
  "status": "received",
  "command": "start",
  "task": "photo",
  "message": "start-photo command received",
  "data": {}
}
```

**成功响应:**
```json
{
  "code": 0,
  "status": "success",
  "command": "start",
  "task": "photo",
  "message": "start-photo command complete",
  "data": {
    "result": true,
    "path": "data/shot-images-saves/0701123456_001"
  }
}
```

**错误响应:**
```json
{
  "code": 3001,
  "status": "error",
  "command": "start",
  "task": "photo",
  "message": "Camera frame error",
  "data": {
    "result": false,
    "path": "data/shot-images-saves/0701123456_001"
  }
}
```



### 7. 暂停命令

#### 请求

```json
{
  "command": "pause"
}
```

#### 响应

```json
{
  "code": 0,
  "status": "received",
  "command": "pause",
  "task": "control_toggle",
  "message": "Pause command received",
  "data": {}
}
```

`task`字段将包含触发暂停时正在运行的任务名称。
暂停命令无终止反馈。

### 8. 继续命令

#### 请求

```json
{
  "command": "resume"
}
```

#### 响应

**初始响应:**
```json
{
  "code": 0,
  "status": "received",
  "command": "resume",
  "task": "control_toggle",
  "message": "Continue command received",
  "data": {}
}
```

继续命令无终止反馈。



## 响应状态值

| 状态 | 描述 |
|--------|-------------|
| received | 命令已接收并正在处理中 |
| success | 命令已成功完成 |
| error | 命令执行过程中发生错误 |

## 任务工作流程

### 烟感测试任务

1. 服务器发送烟感测试命令
2. 客户端响应"received"状态
3. 客户端执行烟感测试流程:
   - 返回到零位位置
   - 移动到准备抓杆位置
   - 校准烟杆位置
   - 抓取烟杆
   - 提升至工作位置
   - 寻找合适的起始位置
   - 移动至烟雾探测器
   - 等待指定时间
   - 返回至抓取位置
   - 返回至准备放杆位置
   - 返回至零位位置
4. 客户端响应最终状态(成功或错误)

### 温感测试任务

1. 服务器发送温感测试命令
2. 客户端响应"received"状态
3. 客户端执行温感测试流程: 暂无
4. 客户端响应最终状态(成功或错误)

### 烟感检测拍照任务

1. 服务器发送烟感检测拍照命令
2. 客户端响应"received"状态
3. 客户端执行拍照流程:
   - 移动通过预定义位置
   - 在每个位置拍照
   - 在图像中检测烟雾探测器
   - 将图像保存到指定路径
4. 客户端响应最终状态(成功或错误)

### 按钮控制任务

1. 服务器发送按钮控制命令
2. 客户端响应"received"状态
3. 客户端执行按钮控制流程(具体按指定顺序实现)
4. 客户端响应最终状态(成功或错误)

### 紧急停止

1. 服务器发送紧急停止命令
2. 客户端立即停止所有机械臂移动指令
3. 客户端响应"received"状态

### 重置

1. 服务器发送重置命令
2. 客户端响应"received"状态
3. 客户端将机械臂重置并返回零位位置
4. 客户端响应最终状态(成功或错误) 

### 暂停

1. 服务器发送暂停命令
2. 客户端立即暂停现有任务
3. 客户端响应"received"状态

### 继续

1. 服务器发送继续命令
2. 客户端继续执行当前任务
3. 客户端响应"received"状态
